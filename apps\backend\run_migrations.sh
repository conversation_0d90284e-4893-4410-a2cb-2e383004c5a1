#!/bin/bash
# Script to run database migrations

# Set variables
PROJECT_ROOT="/mnt/e/Projects/lonors/loni"
BACKEND_DIR="$PROJECT_ROOT/apps/backend"
LOG_FILE="$PROJECT_ROOT/apps/data/logs/devtools-db-migrations.log"

# Create log file if it doesn't exist
mkdir -p "$PROJECT_ROOT/apps/data/logs"
touch "$LOG_FILE"

# Function to log messages
log_message() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

# Start migration execution
log_message "Starting database migrations execution"

# Change to backend directory
cd "$BACKEND_DIR" || { log_message "ERROR: Failed to change to backend directory"; exit 1; }

# Run migrations with uv
log_message "Running database migrations..."
uv run alembic upgrade head 2>&1 | tee -a "$LOG_FILE"

# Check exit status
if [ ${PIPESTATUS[0]} -eq 0 ]; then
    log_message "Database migrations completed successfully"
else
    log_message "ERROR: Database migrations failed"
    exit 1
fi