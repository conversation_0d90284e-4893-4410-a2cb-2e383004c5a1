'use client';

import { Card } from '@/components/common/Card';
import { motion } from 'framer-motion';
import type { ScanResult, IssueSeverity, IssueCategory } from '@/lib/types';

interface ScanResultsChartProps {
  scanResult: ScanResult;
  chartType?: 'severity' | 'category' | 'timeline';
  className?: string;
}

const severityColors = {
  critical: '#ef4444',
  high: '#f97316',
  medium: '#eab308',
  low: '#3b82f6',
  info: '#6b7280'
};

const categoryColors = {
  security: '#ef4444',
  performance: '#8b5cf6',
  maintainability: '#06b6d4',
  reliability: '#10b981',
  style: '#f59e0b',
  dependency: '#84cc16',
  documentation: '#6b7280'
};

export function ScanResultsChart({ 
  scanResult, 
  chartType = 'severity',
  className 
}: ScanResultsChartProps) {
  const renderSeverityChart = () => {
    const data = Object.entries(scanResult.summary.issuesBySeverity).filter(([_, count]) => count > 0);
    const total = data.reduce((sum, [_, count]) => sum + count, 0);
    
    if (total === 0) {
      return (
        <div className="flex items-center justify-center h-32 text-muted-foreground">
          No issues found
        </div>
      );
    }
    
    let cumulativePercentage = 0;
    
    return (
      <div className="space-y-4">
        <div className="relative h-4 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden">
          {data.map(([severity, count]) => {
            const percentage = (count / total) * 100;
            const startPercentage = cumulativePercentage;
            cumulativePercentage += percentage;
            
            return (
              <motion.div
                key={severity}
                className="absolute h-full"
                style={{
                  backgroundColor: severityColors[severity as IssueSeverity],
                  left: `${startPercentage}%`,
                  width: `${percentage}%`
                }}
                initial={{ width: 0 }}
                animate={{ width: `${percentage}%` }}
                transition={{ duration: 1, delay: 0.2 }}
              />
            );
          })}
        </div>
        
        <div className="grid grid-cols-2 md:grid-cols-5 gap-3">
          {data.map(([severity, count]) => (
            <div key={severity} className="text-center">
              <div 
                className="w-4 h-4 rounded-full mx-auto mb-1"
                style={{ backgroundColor: severityColors[severity as IssueSeverity] }}
              />
              <div className="text-sm font-medium">{count}</div>
              <div className="text-xs text-muted-foreground capitalize">{severity}</div>
            </div>
          ))}
        </div>
      </div>
    );
  };
  
  const renderCategoryChart = () => {
    const data = Object.entries(scanResult.summary.issuesByCategory).filter(([_, count]) => count > 0);
    const total = data.reduce((sum, [_, count]) => sum + count, 0);
    
    if (total === 0) {
      return (
        <div className="flex items-center justify-center h-32 text-muted-foreground">
          No issues found
        </div>
      );
    }
    
    const maxCount = Math.max(...data.map(([_, count]) => count));
    
    return (
      <div className="space-y-3">
        {data.map(([category, count]) => {
          const percentage = (count / maxCount) * 100;
          
          return (
            <div key={category} className="space-y-1">
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium capitalize">{category}</span>
                <span className="text-sm text-muted-foreground">{count}</span>
              </div>
              <div className="h-2 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden">
                <motion.div
                  className="h-full rounded-full"
                  style={{ backgroundColor: categoryColors[category as IssueCategory] }}
                  initial={{ width: 0 }}
                  animate={{ width: `${percentage}%` }}
                  transition={{ duration: 1, delay: 0.1 }}
                />
              </div>
            </div>
          );
        })}
      </div>
    );
  };
  
  const renderMetricsCard = () => {
    const { summary, metrics } = scanResult;
    
    return (
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <div className="text-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
          <div className="text-2xl font-bold text-primary">{summary.totalIssues}</div>
          <div className="text-xs text-muted-foreground">Total Issues</div>
        </div>
        
        <div className="text-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
          <div className="text-2xl font-bold text-blue-600">{summary.filesScanned}</div>
          <div className="text-xs text-muted-foreground">Files Scanned</div>
        </div>
        
        <div className="text-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
          <div className="text-2xl font-bold text-green-600">
            {summary.linesOfCode.toLocaleString()}
          </div>
          <div className="text-xs text-muted-foreground">Lines of Code</div>
        </div>
        
        <div className="text-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
          <div className="text-2xl font-bold text-purple-600">
            {summary.score || 'N/A'}
            {summary.score && '/100'}
          </div>
          <div className="text-xs text-muted-foreground">Quality Score</div>
        </div>
        
        {metrics.performanceMetrics && (
          <>
            <div className="text-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
              <div className="text-lg font-bold text-orange-600">
                {metrics.performanceMetrics.cyclomatic_complexity.toFixed(1)}
              </div>
              <div className="text-xs text-muted-foreground">Cyclomatic Complexity</div>
            </div>
            
            <div className="text-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
              <div className="text-lg font-bold text-pink-600">
                {metrics.performanceMetrics.cognitive_complexity.toFixed(1)}
              </div>
              <div className="text-xs text-muted-foreground">Cognitive Complexity</div>
            </div>
            
            <div className="text-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
              <div className="text-lg font-bold text-indigo-600">
                {metrics.performanceMetrics.maintainability_index}
              </div>
              <div className="text-xs text-muted-foreground">Maintainability Index</div>
            </div>
          </>
        )}
        
        {metrics.dependencyMetrics && (
          <div className="text-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
            <div className="text-lg font-bold text-red-600">
              {metrics.dependencyMetrics.vulnerable}
            </div>
            <div className="text-xs text-muted-foreground">Vulnerable Dependencies</div>
          </div>
        )}
      </div>
    );
  };
  
  const getChartTitle = () => {
    switch (chartType) {
      case 'severity':
        return 'Issues by Severity';
      case 'category':
        return 'Issues by Category';
      case 'timeline':
        return 'Scan Metrics';
      default:
        return 'Scan Results';
    }
  };
  
  return (
    <Card className={`p-6 ${className}`}>
      <h3 className="text-lg font-semibold mb-4">{getChartTitle()}</h3>
      
      {chartType === 'severity' && renderSeverityChart()}
      {chartType === 'category' && renderCategoryChart()}
      {chartType === 'timeline' && renderMetricsCard()}
      
      {scanResult.summary.technicalDebt && (
        <div className="mt-4 pt-4 border-t">
          <div className="flex justify-between items-center text-sm">
            <span className="text-muted-foreground">Technical Debt:</span>
            <span className="font-medium">{scanResult.summary.technicalDebt}</span>
          </div>
        </div>
      )}
      
      <div className="mt-4 pt-4 border-t">
        <div className="flex justify-between items-center text-xs text-muted-foreground">
          <span>Scan completed: {scanResult.completedAt.toLocaleDateString()}</span>
          <span>Duration: {Math.round(scanResult.metrics.scanDuration / 60)}m {scanResult.metrics.scanDuration % 60}s</span>
        </div>
      </div>
    </Card>
  );
}