"""
Settings module for the LONI backend application.
"""

from functools import lru_cache

from .app import AppSettings
from .database import DatabaseSettings
from .external_services import ExternalServicesSettings
from .security import SecuritySettings


class Settings(AppSettings, DatabaseSettings, ExternalServicesSettings, SecuritySettings):
    """Unified settings class combining all configuration sections."""
    
    class Config:
        env_file = ".env"
        case_sensitive = True


@lru_cache()
def get_settings() -> Settings:
    """Get cached settings instance."""
    return Settings()


# Global settings instance for backward compatibility
settings = get_settings()

# Export individual settings classes for type hinting
__all__ = [
    "Settings",
    "AppSettings", 
    "DatabaseSettings",
    "ExternalServicesSettings",
    "SecuritySettings",
    "settings",
    "get_settings"
]