{"pagination": {"ListCloudFrontOriginAccessIdentities": {"input_token": "<PERSON><PERSON>", "output_token": "CloudFrontOriginAccessIdentityList.NextMarker", "limit_key": "MaxItems", "more_results": "CloudFrontOriginAccessIdentityList.IsTruncated", "result_key": "CloudFrontOriginAccessIdentityList.Items"}, "ListDistributions": {"input_token": "<PERSON><PERSON>", "output_token": "DistributionList.NextMarker", "limit_key": "MaxItems", "more_results": "DistributionList.IsTruncated", "result_key": "DistributionList.Items"}, "ListInvalidations": {"input_token": "<PERSON><PERSON>", "output_token": "InvalidationList.NextMarker", "limit_key": "MaxItems", "more_results": "InvalidationList.IsTruncated", "result_key": "InvalidationList.Items"}, "ListStreamingDistributions": {"input_token": "<PERSON><PERSON>", "output_token": "StreamingDistributionList.NextMarker", "limit_key": "MaxItems", "more_results": "StreamingDistributionList.IsTruncated", "result_key": "StreamingDistributionList.Items"}, "ListKeyValueStores": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxItems", "output_token": "KeyValueStoreList.NextMarker", "result_key": "KeyValueStoreList.Items"}, "ListPublicKeys": {"input_token": "<PERSON><PERSON>", "output_token": "PublicKeyList.NextMarker", "limit_key": "MaxItems", "result_key": "PublicKeyList.Items"}, "ListConnectionGroups": {"input_token": "<PERSON><PERSON>", "output_token": "NextMarker", "limit_key": "MaxItems", "result_key": "ConnectionGroups"}, "ListDistributionTenants": {"input_token": "<PERSON><PERSON>", "output_token": "NextMarker", "limit_key": "MaxItems", "result_key": "DistributionTenantList"}, "ListDistributionTenantsByCustomization": {"input_token": "<PERSON><PERSON>", "output_token": "NextMarker", "limit_key": "MaxItems", "result_key": "DistributionTenantList"}, "ListDistributionsByConnectionMode": {"input_token": "<PERSON><PERSON>", "output_token": "DistributionList.NextMarker", "limit_key": "MaxItems", "result_key": "DistributionList.Items"}, "ListDomainConflicts": {"input_token": "<PERSON><PERSON>", "output_token": "NextMarker", "limit_key": "MaxItems", "result_key": "DomainConflicts"}, "ListInvalidationsForDistributionTenant": {"input_token": "<PERSON><PERSON>", "output_token": "InvalidationList.NextMarker", "limit_key": "MaxItems", "result_key": "InvalidationList.Items"}, "ListOriginAccessControls": {"input_token": "<PERSON><PERSON>", "output_token": "OriginAccessControlList.NextMarker", "limit_key": "MaxItems", "result_key": "OriginAccessControlList.Items"}}}