#!/usr/bin/env node
/**
 * Setup script for frontend TDD testing infrastructure
 * Configures Bun with Jest/Vitest for comprehensive testing
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

const FRONTEND_PATH = path.resolve(__dirname, '../../apps/frontend');

// Jest configuration for Bun
const jestConfig = {
  preset: 'ts-jest',
  testEnvironment: 'jsdom',
  setupFilesAfterEnv: ['<rootDir>/src/test/setup.ts'],
  testMatch: [
    '<rootDir>/src/**/__tests__/**/*.{ts,tsx}',
    '<rootDir>/src/**/*.{test,spec}.{ts,tsx}'
  ],
  moduleNameMapping: {
    '^@/(.*)$': '<rootDir>/src/$1',
    '^@shared/(.*)$': '<rootDir>/src/shared/$1',
    '^@widgets/(.*)$': '<rootDir>/src/widgets/$1',
    '^@entities/(.*)$': '<rootDir>/src/entities/$1',
    '^@features/(.*)$': '<rootDir>/src/features/$1',
    '^@processes/(.*)$': '<rootDir>/src/processes/$1'
  },
  collectCoverageFrom: [
    'src/**/*.{ts,tsx}',
    '!src/**/*.d.ts',
    '!src/**/*.stories.{ts,tsx}',
    '!src/**/__tests__/**',
    '!src/**/node_modules/**'
  ],
  coverageThreshold: {
    global: {
      branches: 75,
      functions: 75,
      lines: 75,
      statements: 75
    }
  },
  transform: {
    '^.+\\.(ts|tsx)$': ['ts-jest', {
      tsconfig: {
        jsx: 'react-jsx'
      }
    }]
  }
};

// Vitest configuration (alternative to Jest)
const vitestConfig = `/// <reference types="vitest" />
import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import path from 'path'

export default defineConfig({
  plugins: [react()],
  test: {
    environment: 'jsdom',
    setupFiles: ['./src/test/setup.ts'],
    globals: true,
    css: true,
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html'],
      exclude: [
        'node_modules/',
        'src/test/',
        '**/*.d.ts',
        '**/*.stories.{ts,tsx}',
        '**/__tests__/**',
      ],
      thresholds: {
        global: {
          branches: 75,
          functions: 75,
          lines: 75,
          statements: 75
        }
      }
    }
  },
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
      '@shared': path.resolve(__dirname, './src/shared'),
      '@widgets': path.resolve(__dirname, './src/widgets'),
      '@entities': path.resolve(__dirname, './src/entities'),
      '@features': path.resolve(__dirname, './src/features'),
      '@processes': path.resolve(__dirname, './src/processes')
    }
  }
})
`;

// Test setup file
const testSetup = `import '@testing-library/jest-dom'
import { configure } from '@testing-library/react'

// Configure testing library
configure({ testIdAttribute: 'data-testid' })

// Mock Next.js router
jest.mock('next/router', () => ({
  useRouter: () => ({
    route: '/',
    pathname: '/',
    query: {},
    asPath: '/',
    push: jest.fn(),
    pop: jest.fn(),
    reload: jest.fn(),
    back: jest.fn(),
    prefetch: jest.fn(),
    beforePopState: jest.fn(),
    events: {
      on: jest.fn(),
      off: jest.fn(),
      emit: jest.fn(),
    },
  }),
}))

// Mock Next.js navigation
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: jest.fn(),
    replace: jest.fn(),
    prefetch: jest.fn(),
    back: jest.fn(),
    forward: jest.fn(),
    refresh: jest.fn(),
  }),
  useSearchParams: () => new URLSearchParams(),
  usePathname: () => '/',
}))

// Global test utilities
global.ResizeObserver = class ResizeObserver {
  observe() {}
  unobserve() {}
  disconnect() {}
}

// Mock IntersectionObserver
global.IntersectionObserver = class IntersectionObserver {
  constructor() {}
  observe() {}
  disconnect() {}
  unobserve() {}
}

// Setup for framer-motion
jest.mock('framer-motion', () => ({
  motion: {
    div: 'div',
    button: 'button',
    span: 'span',
    p: 'p',
    h1: 'h1',
    h2: 'h2',
    h3: 'h3',
    section: 'section',
    article: 'article',
  },
  AnimatePresence: ({ children }) => children,
  useAnimation: () => ({}),
  useMotionValue: () => ({}),
  useSpring: () => ({}),
  useTransform: () => ({}),
}))
`;

// TDD helper utilities
const tddHelpers = `/**
 * TDD Helper Utilities for London School Methodology
 */

export class MockBuilder<T = any> {
  private mocks: Record<string, jest.Mock> = {}

  mock(method: keyof T): jest.Mock {
    const methodName = String(method)
    if (!this.mocks[methodName]) {
      this.mocks[methodName] = jest.fn()
    }
    return this.mocks[methodName]
  }

  mockResolvedValue(method: keyof T, value: any): this {
    this.mock(method).mockResolvedValue(value)
    return this
  }

  mockRejectedValue(method: keyof T, error: any): this {
    this.mock(method).mockRejectedValue(error)
    return this
  }

  mockReturnValue(method: keyof T, value: any): this {
    this.mock(method).mockReturnValue(value)
    return this
  }

  build(): jest.Mocked<T> {
    return this.mocks as jest.Mocked<T>
  }

  verifyInteractions(): void {
    Object.entries(this.mocks).forEach(([method, mock]) => {
      if (mock.mock.calls.length === 0) {
        console.warn(\`Mock \${method} was never called\`)
      }
    })
  }
}

export class TestScenarioBuilder {
  private given: Array<() => void> = []
  private when: (() => Promise<any>) | null = null
  private then: Array<() => void> = []

  givenThat(setup: () => void): this {
    this.given.push(setup)
    return this
  }

  whenI(action: () => Promise<any>): this {
    this.when = action
    return this
  }

  thenI(assertion: () => void): this {
    this.then.push(assertion)
    return this
  }

  async execute(): Promise<any> {
    // Setup
    this.given.forEach(setup => setup())
    
    // Action
    const result = this.when ? await this.when() : null
    
    // Assertions
    this.then.forEach(assertion => assertion())
    
    return result
  }
}

export const createMock = <T = any>(): MockBuilder<T> => new MockBuilder<T>()
export const scenario = (): TestScenarioBuilder => new TestScenarioBuilder()

// Contract testing utilities
export interface ContractTest<T> {
  name: string
  input: any
  expectedOutput: any
  setup?: () => T
  cleanup?: () => void
}

export function runContractTests<T>(
  implementation: T,
  contracts: ContractTest<T>[]
): void {
  describe('Contract Tests', () => {
    contracts.forEach(contract => {
      it(\`should satisfy contract: \${contract.name}\`, async () => {
        let instance = implementation
        
        if (contract.setup) {
          instance = contract.setup()
        }
        
        try {
          // This would need to be customized based on the specific contract
          // For now, it's a placeholder for contract verification
          expect(instance).toBeDefined()
        } finally {
          if (contract.cleanup) {
            contract.cleanup()
          }
        }
      })
    })
  })
}
`;

// Test template generator
const testTemplate = `import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { createMock, scenario } from '../test/tdd-helpers'
import { ComponentName } from '../ComponentName'

describe('ComponentName', () => {
  // London School TDD: Start with collaborator contracts
  const mockDependency = createMock<DependencyType>()
    .mockReturnValue('method', 'expected-value')
    .build()

  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('Behavior Verification', () => {
    it('should coordinate with dependencies correctly', async () => {
      await scenario()
        .givenThat(() => {
          // Setup test conditions
        })
        .whenI(async () => {
          render(<ComponentName dependency={mockDependency} />)
          // Trigger behavior
        })
        .thenI(() => {
          // Verify interactions
          expect(mockDependency.method).toHaveBeenCalledWith(/* expected args */)
        })
        .execute()
    })
  })

  describe('Integration Scenarios', () => {
    it('should handle user interaction workflow', async () => {
      render(<ComponentName dependency={mockDependency} />)
      
      // User action
      fireEvent.click(screen.getByRole('button'))
      
      await waitFor(() => {
        expect(mockDependency.method).toHaveBeenCalled()
      })
    })
  })
})
`;

function setupFrontendTesting() {
  console.log('🔧 Setting up frontend TDD testing infrastructure...');

  if (!fs.existsSync(FRONTEND_PATH)) {
    console.error('❌ Frontend directory not found:', FRONTEND_PATH);
    process.exit(1);
  }

  process.chdir(FRONTEND_PATH);

  // Read current package.json
  const packageJsonPath = path.join(FRONTEND_PATH, 'package.json');
  let packageJson = {};
  
  if (fs.existsSync(packageJsonPath)) {
    packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
  }

  // Add testing dependencies
  const testingDeps = {
    '@testing-library/react': '^14.0.0',
    '@testing-library/jest-dom': '^6.1.0',
    '@testing-library/user-event': '^14.5.0',
    '@types/jest': '^29.5.0',
    'jest': '^29.7.0',
    'jest-environment-jsdom': '^29.7.0',
    'ts-jest': '^29.1.0',
    'vitest': '^1.0.0',
    '@vitejs/plugin-react': '^4.0.0'
  };

  // Add test scripts
  const testScripts = {
    'test': 'bun test',
    'test:watch': 'bun test --watch',
    'test:coverage': 'bun test --coverage',
    'test:ui': 'bun test --ui',
    'tdd': 'python3 ../../scripts/tdd/tdd-workflow.py --component frontend --watch'
  };

  // Update package.json
  packageJson.devDependencies = { ...(packageJson.devDependencies || {}), ...testingDeps };
  packageJson.scripts = { ...(packageJson.scripts || {}), ...testScripts };

  fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2));

  // Create test directory structure
  const testDir = path.join(FRONTEND_PATH, 'src', 'test');
  if (!fs.existsSync(testDir)) {
    fs.mkdirSync(testDir, { recursive: true });
  }

  // Write configuration files
  fs.writeFileSync(path.join(FRONTEND_PATH, 'jest.config.js'), 
    `module.exports = ${JSON.stringify(jestConfig, null, 2)};`);
    
  fs.writeFileSync(path.join(FRONTEND_PATH, 'vitest.config.ts'), vitestConfig);
  
  fs.writeFileSync(path.join(testDir, 'setup.ts'), testSetup);
  
  fs.writeFileSync(path.join(testDir, 'tdd-helpers.ts'), tddHelpers);

  // Create template files
  const templatesDir = path.join(testDir, 'templates');
  if (!fs.existsSync(templatesDir)) {
    fs.mkdirSync(templatesDir);
  }
  
  fs.writeFileSync(path.join(templatesDir, 'component.test.tsx'), testTemplate);

  console.log('✅ Frontend testing infrastructure setup complete');
  console.log('📦 Installing dependencies...');

  // Install dependencies
  try {
    execSync('bun install', { stdio: 'inherit' });
    console.log('✅ Dependencies installed successfully');
  } catch (error) {
    console.error('❌ Failed to install dependencies:', error.message);
  }
}

if (require.main === module) {
  setupFrontendTesting();
}

module.exports = { setupFrontendTesting };