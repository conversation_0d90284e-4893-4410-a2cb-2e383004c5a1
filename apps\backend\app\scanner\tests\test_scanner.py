"""
Tests for the ProjectScanner system.

These tests verify the comprehensive issue detection algorithms and 
ensure all detectors work correctly together.
"""

import pytest
import tempfile
import shutil
from pathlib import Path
from datetime import datetime

from ..scanner import ProjectScanner
from ..models import (
    ScanRequest,
    ScanConfiguration,
    IssueSeverity,
    IssueType
)


@pytest.fixture
def temp_project():
    """Create a temporary project for testing."""
    temp_dir = Path(tempfile.mkdtemp())
    
    # Create sample Python files with various issues
    (temp_dir / "main.py").write_text('''
import pickle
import os

def complex_function():
    """This function has high complexity."""
    for i in range(10):
        for j in range(10):
            for k in range(10):
                if i % 2 == 0:
                    if j % 2 == 0:
                        if k % 2 == 0:
                            print(f"Complex: {i}, {j}, {k}")
                        else:
                            print("Odd k")
                    else:
                        print("Odd j")
                else:
                    print("Odd i")

# Security issues
SECRET_KEY = "hardcoded-secret-key-123"
os.system("ls -la")

def sql_injection_example(user_input):
    query = f"SELECT * FROM users WHERE id = {user_input}"
    return query
''')
    
    # Create requirements.txt with vulnerable dependencies
    (temp_dir / "requirements.txt").write_text('''
django==3.0.0
flask==1.0.0
pillow==7.0.0
pyyaml==5.3.0
requests>=2.0.0
''')
    
    # Create package.json with JavaScript issues
    (temp_dir / "package.json").write_text('''
{
    "name": "test-project",
    "version": "1.0.0",
    "dependencies": {
        "lodash": "4.17.15",
        "axios": "0.21.1"
    },
    "scripts": {
        "dangerous": "rm -rf /tmp/*"
    }
}
''')
    
    # Create JavaScript file with performance issues
    (temp_dir / "app.js").write_text('''
function inefficientLoop() {
    for (var i = 0; i < array.length; i++) {
        document.getElementById('element-' + i).innerHTML += "data";
    }
}

eval("console.log('dangerous')");
''')
    
    # Create large configuration file
    config_content = '{\n' + ',\n'.join([f'  "key{i}": "value{i}"' for i in range(2000)]) + '\n}'
    (temp_dir / "config.json").write_text(config_content)
    
    yield temp_dir
    
    # Cleanup
    shutil.rmtree(temp_dir)


def test_project_scanner_initialization():
    """Test ProjectScanner initialization."""
    scanner = ProjectScanner()
    assert scanner.detectors == {}
    
    info = scanner.get_detector_info()
    assert 'available_detectors' in info
    assert info['total_detectors'] == 5
    assert 'code_quality' in info['available_detectors']
    assert 'security' in info['available_detectors']


def test_full_project_scan(temp_project):
    """Test full project scanning with all detectors enabled."""
    scanner = ProjectScanner()
    
    request = ScanRequest(
        project_path=str(temp_project),
        configuration=ScanConfiguration(
            enable_code_quality=True,
            enable_security=True,
            enable_dependencies=True,
            enable_structure=True,
            enable_performance=True,
            min_severity=IssueSeverity.INFO
        )
    )
    
    response = scanner.scan_project(request)
    
    # Verify scan completed successfully
    assert response.success == True
    assert response.scan_id is not None
    assert isinstance(response.scan_start, datetime)
    assert isinstance(response.scan_end, datetime)
    assert response.scan_duration > 0
    
    # Verify issues were detected
    assert len(response.issues) > 0
    
    # Check for expected issue types
    issue_types = {issue.type for issue in response.issues}
    
    # Should detect code complexity
    assert IssueType.CODE_COMPLEXITY in issue_types
    
    # Should detect security vulnerabilities
    assert IssueType.SECURITY_VULNERABILITY in issue_types or IssueType.HARDCODED_SECRET in issue_types
    
    # Should detect vulnerable dependencies
    assert IssueType.VULNERABLE_DEPENDENCY in issue_types
    
    # Should detect performance issues
    assert IssueType.INEFFICIENT_ALGORITHM in issue_types or IssueType.LARGE_FILE in issue_types
    
    # Verify summary statistics
    assert response.summary.total_issues == len(response.issues)
    assert len(response.summary.issues_by_severity) > 0
    assert len(response.summary.issues_by_type) > 0
    assert response.summary.average_risk_score >= 0
    
    # Verify metrics
    assert response.metrics.files_processed > 0
    assert response.metrics.scan_duration > 0
    assert len(response.metrics.detector_metrics) > 0


def test_scan_configuration_filtering(temp_project):
    """Test that scan configuration properly filters results."""
    scanner = ProjectScanner()
    
    # Scan with high severity filter
    request = ScanRequest(
        project_path=str(temp_project),
        configuration=ScanConfiguration(
            min_severity=IssueSeverity.HIGH,
            max_issues_per_type=5
        )
    )
    
    response = scanner.scan_project(request)
    
    # All issues should be HIGH or CRITICAL severity
    for issue in response.issues:
        assert issue.severity in [IssueSeverity.HIGH, IssueSeverity.CRITICAL]
    
    # Check that limits are respected
    issue_type_counts = {}
    for issue in response.issues:
        issue_type_counts[issue.type] = issue_type_counts.get(issue.type, 0) + 1
    
    for count in issue_type_counts.values():
        assert count <= 5


def test_code_quality_detection(temp_project):
    """Test specific code quality detection."""
    scanner = ProjectScanner()
    
    request = ScanRequest(
        project_path=str(temp_project),
        configuration=ScanConfiguration(
            enable_code_quality=True,
            enable_security=False,
            enable_dependencies=False,
            enable_structure=False,
            enable_performance=False
        )
    )
    
    response = scanner.scan_project(request)
    
    # Should detect code complexity issues
    complexity_issues = [
        issue for issue in response.issues 
        if issue.type == IssueType.CODE_COMPLEXITY
    ]
    assert len(complexity_issues) > 0
    
    # Verify issue details
    for issue in complexity_issues:
        assert issue.title is not None
        assert issue.description is not None
        assert issue.location.file_path is not None
        assert issue.detector == "CodeQuality"


def test_security_detection(temp_project):
    """Test specific security detection."""
    scanner = ProjectScanner()
    
    request = ScanRequest(
        project_path=str(temp_project),
        configuration=ScanConfiguration(
            enable_code_quality=False,
            enable_security=True,
            enable_dependencies=False,
            enable_structure=False,
            enable_performance=False
        )
    )
    
    response = scanner.scan_project(request)
    
    # Should detect security issues
    security_issues = [
        issue for issue in response.issues 
        if issue.type in [IssueType.SECURITY_VULNERABILITY, IssueType.HARDCODED_SECRET]
    ]
    assert len(security_issues) > 0
    
    # Verify OWASP categories are assigned
    owasp_issues = [issue for issue in security_issues if issue.owasp_category]
    assert len(owasp_issues) > 0


def test_dependency_detection(temp_project):
    """Test dependency vulnerability detection."""
    scanner = ProjectScanner()
    
    request = ScanRequest(
        project_path=str(temp_project),
        configuration=ScanConfiguration(
            enable_code_quality=False,
            enable_security=False,
            enable_dependencies=True,
            enable_structure=False,
            enable_performance=False
        )
    )
    
    response = scanner.scan_project(request)
    
    # Should detect vulnerable dependencies
    vuln_deps = [
        issue for issue in response.issues 
        if issue.type == IssueType.VULNERABLE_DEPENDENCY
    ]
    assert len(vuln_deps) > 0


def test_performance_detection(temp_project):
    """Test performance issue detection."""
    scanner = ProjectScanner()
    
    request = ScanRequest(
        project_path=str(temp_project),
        configuration=ScanConfiguration(
            enable_code_quality=False,
            enable_security=False,
            enable_dependencies=False,
            enable_structure=False,
            enable_performance=True
        )
    )
    
    response = scanner.scan_project(request)
    
    # Should detect performance issues
    perf_issues = [
        issue for issue in response.issues 
        if issue.type in [IssueType.LARGE_FILE, IssueType.INEFFICIENT_ALGORITHM]
    ]
    assert len(perf_issues) > 0


def test_structure_detection(temp_project):
    """Test structural issue detection."""
    scanner = ProjectScanner()
    
    request = ScanRequest(
        project_path=str(temp_project),
        configuration=ScanConfiguration(
            enable_code_quality=False,
            enable_security=False,
            enable_dependencies=False,
            enable_structure=True,
            enable_performance=False
        )
    )
    
    response = scanner.scan_project(request)
    
    # Should detect missing files or structure issues
    structure_issues = [
        issue for issue in response.issues 
        if issue.type in [IssueType.MISSING_FILE, IssueType.ARCHITECTURE_VIOLATION]
    ]
    assert len(structure_issues) >= 0  # May or may not find issues in simple test project


def test_risk_scoring_and_prioritization(temp_project):
    """Test that risk scoring and prioritization works correctly."""
    scanner = ProjectScanner()
    
    request = ScanRequest(
        project_path=str(temp_project),
        configuration=ScanConfiguration(min_severity=IssueSeverity.INFO)
    )
    
    response = scanner.scan_project(request)
    
    # Verify issues have proper risk scores
    for issue in response.issues:
        assert 0 <= issue.risk_score <= 100
        assert 0 <= issue.impact_score <= 100
        assert 0 <= issue.likelihood_score <= 100
    
    # Verify issues are sorted by risk score (highest first)
    for i in range(len(response.issues) - 1):
        assert response.issues[i].risk_score >= response.issues[i + 1].risk_score
    
    # Verify summary contains risk distribution
    assert 'low' in response.summary.risk_distribution
    assert 'medium' in response.summary.risk_distribution
    assert 'high' in response.summary.risk_distribution
    assert 'critical' in response.summary.risk_distribution


def test_error_handling(temp_project):
    """Test error handling for invalid project paths."""
    scanner = ProjectScanner()
    
    # Test with non-existent path
    with pytest.raises(ValueError):
        ScanRequest(project_path="/non/existent/path")
    
    # Test with file instead of directory
    test_file = temp_project / "test_file.txt"
    test_file.write_text("test")
    
    with pytest.raises(ValueError):
        ScanRequest(project_path=str(test_file))


if __name__ == "__main__":
    pytest.main([__file__])