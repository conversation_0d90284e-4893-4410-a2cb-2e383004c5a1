# TDD Workflow Automation

Comprehensive Test-Driven Development workflow automation implementing London School (mockist) methodology with quality gates and pre-commit validation.

## 🎯 Overview

This TDD automation framework provides:

- **Test-First Enforcement**: Pre-commit hooks that ensure tests exist before implementation
- **London School TDD**: Mock-driven development focusing on behavior verification  
- **Automated Test Discovery**: Intelligent test file discovery and execution
- **Coverage Quality Gates**: Enforced coverage thresholds (80% backend, 75% frontend)
- **Integrated Toolchain**: Seamless integration with uv (Python) and Bun (JavaScript/TypeScript)

## 📁 Architecture

```
scripts/tdd/
├── tdd-workflow.py           # Main TDD workflow orchestrator
├── london-school-tdd.py      # London School methodology implementation
├── coverage-reporter.py      # Advanced coverage reporting
├── setup-frontend-testing.js # Frontend testing infrastructure setup
├── hooks/
│   └── pre-commit-tdd        # Pre-commit hook for TDD validation
└── templates/               # Test templates and examples
```

## 🚀 Quick Start

### 1. Setup TDD Infrastructure

```bash
# Backend setup (uv + pytest)
cd apps/backend
python3 ../../scripts/tdd/tdd-workflow.py --component backend

# Frontend setup (bun + jest/vitest)
cd apps/frontend
node ../../scripts/tdd/setup-frontend-testing.js
```

### 2. Install Pre-commit Hooks

```bash
# Install pre-commit hook
cp scripts/tdd/hooks/pre-commit-tdd .git/hooks/pre-commit
chmod +x .git/hooks/pre-commit

# Or use pre-commit framework
pip install pre-commit
echo "
repos:
- repo: local
  hooks:
  - id: tdd-validation
    name: TDD Validation
    entry: python3 scripts/tdd/hooks/pre-commit-tdd
    language: system
    stages: [commit]
" > .pre-commit-config.yaml

pre-commit install
```

### 3. Run TDD Workflow

```bash
# Run complete TDD cycle
python3 scripts/tdd/tdd-workflow.py --component all

# Run in watch mode
python3 scripts/tdd/tdd-workflow.py --watch

# Run with specific coverage thresholds
python3 scripts/tdd/tdd-workflow.py \
  --min-coverage-backend 85 \
  --min-coverage-frontend 80
```

## 🧪 London School TDD Methodology

### Core Principles

1. **Outside-In Development**: Start with acceptance tests, work inward
2. **Mock-Driven Design**: Use mocks to define object contracts
3. **Behavior Verification**: Focus on interactions, not state
4. **Collaboration Testing**: Test how objects work together

### Implementation Process

```python
# 1. Define collaborator contracts with mocks
user_repository_mock = Mock(spec=UserRepository)
notification_service_mock = Mock(spec=NotificationService)

# 2. Setup expectations (behavior specification)
user_repository_mock.save.return_value = User(id="123", email="<EMAIL>")
notification_service_mock.send_welcome.return_value = True

# 3. Execute the subject under test
user_service = UserService(user_repository_mock, notification_service_mock)
result = await user_service.register(user_data)

# 4. Verify interactions (not state)
user_repository_mock.save.assert_called_with(
    expect.objectContaining({"email": user_data.email})
)
notification_service_mock.send_welcome.assert_called_with("123")
```

### Auto-Generation Tools

```bash
# Analyze class for TDD opportunities
python3 scripts/tdd/london-school-tdd.py --analyze app/services/user_service.py

# Generate London School test template
python3 scripts/tdd/london-school-tdd.py \
  --generate app/services/user_service.py \
  --output tests/

# Validate existing tests follow London School principles
python3 scripts/tdd/london-school-tdd.py \
  --validate tests/test_services/test_user_service.py
```

## 📊 Coverage & Quality Gates

### Backend Quality Gates (80% minimum)

- **Lines Coverage**: 80%
- **Branch Coverage**: 80%
- **Function Coverage**: 80%
- **Statement Coverage**: 80%

### Frontend Quality Gates (75% minimum)

- **Lines Coverage**: 75%
- **Branch Coverage**: 75%
- **Function Coverage**: 75%
- **Statement Coverage**: 75%

### Coverage Reporting

```bash
# Generate comprehensive coverage report
python3 scripts/tdd/coverage-reporter.py \
  --backend-path apps/backend \
  --frontend-path apps/frontend \
  --output coverage-report.html

# Console report only
python3 scripts/tdd/coverage-reporter.py

# With custom thresholds
python3 scripts/tdd/coverage-reporter.py \
  --min-backend-coverage 85 \
  --min-frontend-coverage 80 \
  --enforce-increase
```

## 🔧 Integration with Existing Tools

### Backend (uv + pytest)

```bash
# Run tests with TDD workflow
cd apps/backend
uv run python -m pytest tests/ --cov=app --cov-report=json

# TDD-integrated test running
python3 ../../scripts/tdd/tdd-workflow.py --component backend
```

### Frontend (Bun + Jest/Vitest)

```bash
# Run tests with coverage
cd apps/frontend
bun test --coverage

# TDD-integrated test running
python3 ../../scripts/tdd/tdd-workflow.py --component frontend
```

## 🚪 Pre-commit Validation

The pre-commit hook enforces:

1. **Test-First Development**: Implementation files must have corresponding tests
2. **Test Quality**: Tests must cover new functionality
3. **Passing Tests**: All existing tests must pass
4. **Coverage Thresholds**: Quality gates must be met

### Bypass in Emergencies

```bash
# Skip TDD validation (NOT recommended)
git commit --no-verify -m "Emergency fix"

# Better: Fix the validation issues
python3 scripts/tdd/tdd-workflow.py --enforce-test-first --file src/new-feature.ts
```

## 📚 Test Templates

### Backend Test Template (London School)

```python
import pytest
from unittest.mock import Mock
from app.services.user_service import UserService


class TestUserService:
    """London School TDD tests focusing on behavior verification"""
    
    def setup_method(self):
        # Setup collaborator mocks
        self.user_repository_mock = Mock()
        self.notification_service_mock = Mock()
        
        # Subject under test with injected dependencies
        self.user_service = UserService(
            user_repository=self.user_repository_mock,
            notification_service=self.notification_service_mock
        )
    
    def test_register_coordinates_user_creation_workflow(self):
        # Arrange - Setup mock expectations
        user_data = {"email": "<EMAIL>", "name": "Test User"}
        saved_user = User(id="123", email="<EMAIL>")
        
        self.user_repository_mock.save.return_value = saved_user
        self.notification_service_mock.send_welcome.return_value = True
        
        # Act - Execute behavior
        result = self.user_service.register(user_data)
        
        # Assert - Verify interactions
        self.user_repository_mock.save.assert_called_once_with(
            pytest.expect.objectContaining({"email": "<EMAIL>"})
        )
        self.notification_service_mock.send_welcome.assert_called_once_with("123")
        assert result.success is True
```

### Frontend Test Template (London School)

```typescript
import { render, screen, fireEvent } from '@testing-library/react'
import { createMock, scenario } from '../test/tdd-helpers'
import { UserRegistration } from '../UserRegistration'

describe('UserRegistration', () => {
  // London School: Define collaborator contracts
  const mockUserService = createMock<UserService>()
    .mockResolvedValue('register', { success: true, id: '123' })
    .build()

  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('should coordinate user registration workflow', async () => {
    await scenario()
      .givenThat(() => {
        render(<UserRegistration userService={mockUserService} />)
      })
      .whenI(async () => {
        fireEvent.change(screen.getByLabelText('Email'), {
          target: { value: '<EMAIL>' }
        })
        fireEvent.click(screen.getByRole('button', { name: 'Register' }))
      })
      .thenI(() => {
        expect(mockUserService.register).toHaveBeenCalledWith({
          email: '<EMAIL>'
        })
      })
      .execute()
  })
})
```

## 🔄 Red-Green-Refactor Cycle

### Automated Cycle Support

```bash
# Watch mode automatically runs the cycle
python3 scripts/tdd/tdd-workflow.py --watch

# Manual cycle steps
python3 scripts/tdd/tdd-workflow.py --component backend  # Should fail (RED)
# Implement feature
python3 scripts/tdd/tdd-workflow.py --component backend  # Should pass (GREEN)
# Refactor with confidence
```

## 🎛️ Configuration

### TDD Workflow Configuration

```python
# In scripts/tdd/tdd-workflow.py
TDDConfig(
    backend_path=Path('apps/backend'),
    frontend_path=Path('apps/frontend'),
    min_coverage_backend=80.0,
    min_coverage_frontend=75.0,
    enable_london_school=True,
    auto_discovery=True,
    watch_mode=False
)
```

### Quality Gate Configuration

```python
# Custom quality gates
quality_gates = [
    QualityGate(
        name='Critical Path Coverage',
        line_threshold=95.0,
        branch_threshold=90.0,
        function_threshold=100.0,
        statement_threshold=95.0,
        enforce_increase=True
    )
]
```

## 🐛 Troubleshooting

### Common Issues

1. **"No test file found"**
   ```bash
   # Create test file first
   touch tests/test_new_feature.py
   # Add basic test structure
   ```

2. **"Coverage below threshold"**
   ```bash
   # Run coverage report to identify gaps
   python3 scripts/tdd/coverage-reporter.py
   # Focus on uncovered lines
   ```

3. **"Mock expectations not met"**
   ```bash
   # Use London School validation
   python3 scripts/tdd/london-school-tdd.py --validate tests/test_file.py
   ```

### Debug Mode

```bash
# Enable verbose logging
DEBUG=1 python3 scripts/tdd/tdd-workflow.py --component all

# Individual component debugging  
python3 scripts/tdd/tdd-workflow.py --component backend --verbose
```

## 🎯 Best Practices

### 1. Test-First Always
- Write failing tests before implementation
- Use pre-commit hooks to enforce this
- Focus on behavior, not implementation details

### 2. Mock External Dependencies
- Database connections
- HTTP clients  
- File system operations
- Third-party services

### 3. Verify Interactions, Not State
```python
# Good (London School)
mock_service.process.assert_called_with(expected_data)

# Avoid (Classical TDD)
assert result.status == 'processed'
```

### 4. Keep Tests Fast
- Use mocks to avoid slow operations
- Parallel test execution where possible
- Focus on unit tests over integration tests

### 5. Maintain High Coverage
- 80%+ backend coverage
- 75%+ frontend coverage
- Focus on critical business logic

## 🔗 Integration with IDE

### VS Code
```json
{
  "python.testing.pytestEnabled": true,
  "python.testing.pytestArgs": ["--cov=app"],
  "typescript.preferences.includePackageJsonAutoImports": "auto"
}
```

### JetBrains IDEs
- Enable pytest runner
- Configure coverage display
- Setup auto-test execution

## 🚀 Advanced Features

### Contract Testing
```python
# Define service contracts
@contract_test
def test_user_service_contract():
    service = UserService(mock_repo, mock_notifier)
    # Contract specifications...
```

### Mutation Testing
```bash
# Install mutation testing
pip install mutmut

# Run mutation tests
mutmut run --paths-to-mutate=app/
```

### Performance Testing
```python
# Performance-aware tests
@pytest.mark.performance
def test_registration_performance():
    with timer() as t:
        service.register(user_data)
    assert t.elapsed < 0.1  # 100ms max
```

This TDD automation framework ensures consistent, high-quality test-driven development across the entire LONI platform, integrating seamlessly with the modern uv + Bun toolchain while enforcing London School best practices.