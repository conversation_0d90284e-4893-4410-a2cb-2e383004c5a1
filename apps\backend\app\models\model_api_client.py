"""API client for Ollama model operations."""
import time
from typing import Dict, List, Any, Optional

import httpx

from app.config import settings
from app.logger import get_logger, log_model_operation

logger = get_logger("model_api_client")


class ModelApiClient:
    """API client for Ollama operations."""

    def __init__(self, host: Optional[str] = None, api_timeout: Optional[int] = None):
        """Initialize the API client."""
        self._ollama_host = host or settings.ollama_host
        self._api_timeout = api_timeout or settings.ollama_api_timeout
        self._http_client: Optional[httpx.AsyncClient] = None

    async def _get_http_client(self) -> httpx.AsyncClient:
        """Get or create HTTP client."""
        if self._http_client is None:
            self._http_client = httpx.AsyncClient(
                base_url=self._ollama_host,
                timeout=self._api_timeout
            )
        return self._http_client

    async def list_models(self) -> Optional[List[str]]:
        """List available Ollama models via API."""
        try:
            client = await self._get_http_client()
            response = await client.get("/api/tags")

            if response.status_code == 200:
                data = response.json()
                models = [model["name"] for model in data.get("models", [])]
                logger.debug("Retrieved models via API", model_count=len(models))
                return models
            else:
                logger.warning(
                    "API request failed, falling back to CLI",
                    status_code=response.status_code,
                    response_text=response.text[:200]
                )
                return None

        except Exception as e:
            logger.debug(f"API method failed, falling back to CLI: {e}")
            return None

    async def pull_model(self, model_name: str) -> Optional[Dict[str, Any]]:
        """Pull model using Ollama API."""
        try:
            client = await self._get_http_client()

            # Start the pull operation
            response = await client.post(
                "/api/pull",
                json={"name": model_name, "stream": False}
            )

            if response.status_code == 200:
                data = response.json()
                logger.info("Model pulled successfully via API", model_name=model_name)
                return data
            else:
                logger.warning(
                    "API pull failed, falling back to CLI",
                    model_name=model_name,
                    status_code=response.status_code,
                    response=response.text[:200]
                )
                return None

        except Exception as e:
            logger.debug(f"API pull failed, falling back to CLI: {e}")
            return None

    async def use_model(self, model_name: str, input_data: str, **kwargs) -> Optional[Dict[str, Any]]:
        """Use model via Ollama API."""
        try:
            client = await self._get_http_client()

            request_data = {
                "model": model_name,
                "prompt": input_data,
                "stream": False,
                **kwargs
            }

            response = await client.post("/api/generate", json=request_data)

            if response.status_code == 200:
                data = response.json()
                logger.info("Model inference successful via API", model_name=model_name)
                return data
            else:
                logger.warning(
                    "API inference failed, falling back to CLI",
                    model_name=model_name,
                    status_code=response.status_code,
                    response=response.text[:200]
                )
                return None

        except Exception as e:
            logger.debug(f"API inference failed, falling back to CLI: {e}")
            return None

    async def get_model_info(self, model_name: str) -> Optional[Dict[str, Any]]:
        """Get detailed information about a model."""
        try:
            client = await self._get_http_client()
            response = await client.post("/api/show", json={"name": model_name})

            if response.status_code == 200:
                data = response.json()
                logger.debug("Retrieved model info", model_name=model_name)
                return data
            else:
                logger.warning(
                    "Failed to get model info",
                    model_name=model_name,
                    status_code=response.status_code
                )
                return None

        except Exception as e:
            logger.error(
                "Error getting model info",
                model_name=model_name,
                error=str(e)
            )
            return None

    async def remove_model(self, model_name: str) -> bool:
        """Remove a model using API."""
        try:
            client = await self._get_http_client()
            response = await client.delete(f"/api/delete", params={"name": model_name})

            return response.status_code == 200

        except Exception as e:
            logger.debug(f"API remove failed: {e}")
            return False

    async def health_check(self) -> Dict[str, Any]:
        """Check Ollama service health."""
        try:
            client = await self._get_http_client()
            response = await client.get("/")

            return {
                "status": "healthy" if response.status_code == 200 else "unhealthy",
                "version": response.json().get("version", "unknown") if response.status_code == 200 else None,
                "host": self._ollama_host,
                "response_time_ms": response.elapsed.total_seconds() * 1000 if hasattr(response, 'elapsed') else None
            }

        except Exception as e:
            return {
                "status": "unhealthy",
                "error": str(e),
                "host": self._ollama_host
            }

    async def close(self) -> None:
        """Close the HTTP client and cleanup resources."""
        if self._http_client:
            await self._http_client.aclose()
            self._http_client = None
            logger.debug("HTTP client closed")
