import { apiClient } from '@/shared/lib/api-client';

export interface Model {
  id: {
    provider: string;
    name: string;
    version: string;
  };
  status: 'installed' | 'downloading' | 'installing' | 'error' | 'updating' | 'deleting' | 'configuring';
  metadata: {
    size_on_disk_bytes?: number;
    installed_at?: string;
    source?: string;
    checksum?: string;
    last_used?: string;
    description?: string;
    capabilities?: string[];
    requirements?: {
      memory_gb?: number;
      disk_gb?: number;
      gpu_required?: boolean;
    };
    health?: {
      status: 'healthy' | 'unhealthy' | 'unknown';
      last_check?: string;
      error?: string;
    };
    extra?: Record<string, any>;
  };
}

export interface AvailableModel {
  provider: string;
  name: string;
  version: string;
  metadata: {
    size_bytes?: number;
    description?: string;
    capabilities?: string[];
    requirements?: {
      memory_gb?: number;
      disk_gb?: number;
      gpu_required?: boolean;
    };
    popularity_score?: number;
    last_updated?: string;
    [key: string]: any;
  };
}

export interface InstallProgress {
  status: 'pending' | 'downloading' | 'extracting' | 'installing' | 'complete' | 'error';
  progress: number; // 0-100
  downloaded_bytes?: number;
  total_bytes?: number;
  current_step?: string;
  error_message?: string;
}

export interface PaginatedResponse<T> {
  items: T[];
  total?: number;
  page?: number;
  page_size?: number;
}

export interface InstallRequest {
  provider: string;
  name: string;
  version?: string;
}

export interface ConfigureRequest {
  provider: string;
  name: string;
  config: Record<string, any>;
}

export interface HealthCheckResult {
  status: 'healthy' | 'unhealthy' | 'unknown';
  details: {
    model_loaded: boolean;
    memory_usage?: number;
    response_time_ms?: number;
    last_request?: string;
    error_count?: number;
  };
  timestamp: string;
}

export interface ModelError {
  code: string;
  message: string;
  details?: Record<string, any>;
  timestamp: string;
}

class ModelService {
  private baseUrl = '/models';

  async getInstalledModels(): Promise<PaginatedResponse<Model>> {
    const response = await apiClient.get(`${this.baseUrl}/installed`);
    return response;
  }

  async getAvailableModels(
    provider?: string,
    page: number = 1,
    pageSize: number = 50,
    query?: string
  ): Promise<PaginatedResponse<AvailableModel>> {
    const params = new URLSearchParams();
    if (provider) params.append('provider', provider);
    if (page) params.append('page', page.toString());
    if (pageSize) params.append('page_size', pageSize.toString());
    if (query) params.append('q', query);
    
    const response = await apiClient.get(`${this.baseUrl}/available?${params.toString()}`);
    return response;
  }

  async installModel(provider: string, name: string, version?: string): Promise<{ task_id: string }> {
    const request: InstallRequest = { provider, name, version };
    const response = await apiClient.post(`${this.baseUrl}/install`, request);
    return response;
  }

  async getInstallProgress(taskId: string): Promise<InstallProgress> {
    const response = await apiClient.get(`${this.baseUrl}/install/${taskId}/progress`);
    return response;
  }

  async cancelInstall(taskId: string): Promise<void> {
    await apiClient.delete(`${this.baseUrl}/install/${taskId}`);
  }

  async configureModel(provider: string, name: string, config: Record<string, any>): Promise<any> {
    const request: ConfigureRequest = { provider, name, config };
    const response = await apiClient.post(`${this.baseUrl}/configure`, request);
    return response;
  }

  async deleteModel(provider: string, name: string, version?: string): Promise<any> {
    const params = new URLSearchParams();
    if (version) params.append('version', version);
    
    const response = await apiClient.delete(
      `${this.baseUrl}/${provider}/${name}?${params.toString()}`
    );
    return response;
  }

  async checkModelHealth(provider: string, name: string): Promise<HealthCheckResult> {
    const response = await apiClient.get(`${this.baseUrl}/${provider}/${name}/health`);
    return response;
  }

  async runHealthCheck(provider: string, name: string): Promise<HealthCheckResult> {
    const response = await apiClient.post(`${this.baseUrl}/${provider}/${name}/health`);
    return response;
  }

  async getModelLogs(provider: string, name: string, limit: number = 100): Promise<Array<{timestamp: string, level: string, message: string}>> {
    const response = await apiClient.get(`${this.baseUrl}/${provider}/${name}/logs?limit=${limit}`);
    return response;
  }

  async validateConfiguration(provider: string, name: string, config: Record<string, any>): Promise<{valid: boolean, errors?: string[]}> {
    const response = await apiClient.post(`${this.baseUrl}/${provider}/${name}/validate-config`, { config });
    return response;
  }

  // Stream installation progress using Server-Sent Events
  subscribeToInstallProgress(taskId: string, onProgress: (progress: InstallProgress) => void, onError?: (error: Error) => void): () => void {
    if (typeof window === 'undefined') {
      throw new Error('SSE not supported in server environment');
    }

    const eventSource = new EventSource(`${this.baseUrl}/install/${taskId}/stream`);
    
    eventSource.onmessage = (event) => {
      try {
        const progress: InstallProgress = JSON.parse(event.data);
        onProgress(progress);
      } catch (error) {
        console.error('Failed to parse progress data:', error);
        onError?.(new Error('Invalid progress data received'));
      }
    };
    
    eventSource.onerror = (event) => {
      console.error('SSE error:', event);
      onError?.(new Error('Connection to progress stream failed'));
      eventSource.close();
    };
    
    // Return cleanup function
    return () => {
      eventSource.close();
    };
  }
}

export const modelService = new ModelService();