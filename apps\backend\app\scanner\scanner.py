"""
Main ProjectScanner orchestration class.

This module coordinates all the detectors and provides the main interface
for scanning projects to detect issues.
"""

import uuid
from datetime import datetime
from pathlib import Path
from typing import List, Optional
import concurrent.futures
import threading
import time

from .models import (
    DetectorResult,
    Issue,
    IssueSeverity,
    IssueType,
    ProjectMetadata,
    ScanConfiguration,
    ScanMetrics,
    ScanRequest,
    ScanResponse,
    ScanSummary
)
from .detectors import (
    CodeQualityDetector,
    SecurityDetector,
    DependencyDetector,
    StructureDetector,
    PerformanceDetector
)


class ProjectScanner:
    """
    Main orchestration class for project scanning.
    
    Coordinates all detectors and provides unified scanning interface
    with comprehensive issue detection, scoring, and prioritization.
    """
    
    def __init__(self):
        """Initialize the project scanner."""
        self.detectors = {}
        self._scan_lock = threading.Lock()
        
    def scan_project(self, request: ScanRequest) -> ScanResponse:
        """
        Scan a project for various types of issues.
        
        Args:
            request: Scan request containing project path and configuration
            
        Returns:
            ScanResponse with all detected issues and metrics
        """
        with self._scan_lock:
            return self._perform_scan(request)
    
    def _perform_scan(self, request: ScanRequest) -> ScanResponse:
        """Perform the actual scanning process."""
        scan_id = request.scan_id or str(uuid.uuid4())
        scan_start = datetime.now()
        project_path = Path(request.project_path)
        
        try:
            # Initialize detectors
            self._initialize_detectors(request.configuration)
            
            # Collect project metadata
            metadata = self._collect_project_metadata(project_path)
            
            # Run detectors
            detector_results = self._run_detectors(project_path, request.configuration)
            
            # Aggregate results
            all_issues = []
            total_files_processed = 0
            
            for result in detector_results:
                if result.success:
                    all_issues.extend(result.issues)
                    total_files_processed += result.files_processed
            
            # Apply scoring and prioritization
            scored_issues = self._score_and_prioritize_issues(all_issues, metadata)
            
            # Filter issues based on configuration
            filtered_issues = self._filter_issues(scored_issues, request.configuration)
            
            # Generate summary and metrics
            summary = self._generate_summary(filtered_issues)
            metrics = self._generate_metrics(detector_results, total_files_processed)
            
            scan_end = datetime.now()
            
            return ScanResponse(
                scan_id=scan_id,
                project_path=request.project_path,
                scan_start=scan_start,
                scan_end=scan_end,
                issues=filtered_issues,
                summary=summary,
                metrics=metrics,
                success=True
            )
        
        except Exception as e:
            scan_end = datetime.now()
            return ScanResponse(
                scan_id=scan_id,
                project_path=request.project_path,
                scan_start=scan_start,
                scan_end=scan_end,
                issues=[],
                summary=ScanSummary(),
                metrics=ScanMetrics(),
                success=False,
                errors=[str(e)]
            )
    
    def _initialize_detectors(self, config: ScanConfiguration) -> None:
        """Initialize enabled detectors based on configuration."""
        self.detectors.clear()
        
        if config.enable_code_quality:
            self.detectors['code_quality'] = CodeQualityDetector(config)
        
        if config.enable_security:
            self.detectors['security'] = SecurityDetector(config)
        
        if config.enable_dependencies:
            self.detectors['dependency'] = DependencyDetector(config)
        
        if config.enable_structure:
            self.detectors['structure'] = StructureDetector(config)
        
        if config.enable_performance:
            self.detectors['performance'] = PerformanceDetector(config)
    
    def _collect_project_metadata(self, project_path: Path) -> ProjectMetadata:
        """Collect metadata about the project."""
        metadata = ProjectMetadata()
        
        try:
            # Basic project info
            metadata.project_name = project_path.name
            
            # Detect languages
            languages = set()
            frameworks = set()
            total_files = 0
            total_size = 0
            
            for file_path in project_path.rglob('*'):
                if file_path.is_file():
                    total_files += 1
                    try:
                        file_size = file_path.stat().st_size
                        total_size += file_size
                        
                        # Language detection based on file extension
                        ext = file_path.suffix.lower()
                        if ext == '.py':
                            languages.add('python')
                        elif ext in {'.js', '.jsx'}:
                            languages.add('javascript')
                        elif ext in {'.ts', '.tsx'}:
                            languages.add('typescript')
                        elif ext == '.java':
                            languages.add('java')
                        elif ext in {'.c', '.cpp', '.cc', '.cxx'}:
                            languages.add('cpp')
                        elif ext == '.go':
                            languages.add('go')
                        elif ext == '.rs':
                            languages.add('rust')
                        elif ext == '.php':
                            languages.add('php')
                        elif ext == '.rb':
                            languages.add('ruby')
                        elif ext in {'.html', '.htm'}:
                            languages.add('html')
                        elif ext == '.css':
                            languages.add('css')
                        elif ext in {'.sql', '.sqlite'}:
                            languages.add('sql')
                    except OSError:
                        continue
            
            # Framework detection
            if (project_path / 'package.json').exists():
                metadata.package_managers.append('npm')
                try:
                    import json
                    with open(project_path / 'package.json', 'r') as f:
                        package_data = json.load(f)
                        deps = {**package_data.get('dependencies', {}), **package_data.get('devDependencies', {})}
                        
                        # Detect JavaScript frameworks
                        if 'react' in deps:
                            frameworks.add('react')
                        if 'vue' in deps:
                            frameworks.add('vue')
                        if 'angular' in deps or '@angular/core' in deps:
                            frameworks.add('angular')
                        if 'express' in deps:
                            frameworks.add('express')
                        if 'next' in deps or 'nextjs' in deps:
                            frameworks.add('nextjs')
                except (json.JSONDecodeError, OSError):
                    pass
            
            # Python framework detection
            if (project_path / 'requirements.txt').exists() or (project_path / 'pyproject.toml').exists():
                metadata.package_managers.append('pip')
                
                # Check for Python frameworks in requirements
                req_files = [f for f in [project_path / 'requirements.txt', project_path / 'pyproject.toml'] if f.exists()]
                for req_file in req_files:
                    try:
                        content = req_file.read_text(encoding='utf-8')
                        content_lower = content.lower()
                        
                        if 'django' in content_lower:
                            frameworks.add('django')
                        if 'flask' in content_lower:
                            frameworks.add('flask')
                        if 'fastapi' in content_lower:
                            frameworks.add('fastapi')
                        if 'tornado' in content_lower:
                            frameworks.add('tornado')
                        if 'pyramid' in content_lower:
                            frameworks.add('pyramid')
                        if 'sqlalchemy' in content_lower:
                            frameworks.add('sqlalchemy')
                    except OSError:
                        continue
            
            # Git info
            if (project_path / '.git').exists():
                metadata.git_info = {'has_git': True}
            
            # Project type detection
            if 'python' in languages:
                if 'django' in frameworks:
                    metadata.project_type = 'django'
                elif 'flask' in frameworks or 'fastapi' in frameworks:
                    metadata.project_type = 'python_web'
                else:
                    metadata.project_type = 'python'
            elif 'javascript' in languages or 'typescript' in languages:
                if 'react' in frameworks:
                    metadata.project_type = 'react'
                elif 'vue' in frameworks:
                    metadata.project_type = 'vue'
                elif 'angular' in frameworks:
                    metadata.project_type = 'angular'
                elif 'express' in frameworks:
                    metadata.project_type = 'nodejs'
                else:
                    metadata.project_type = 'javascript'
            
            metadata.languages = list(languages)
            metadata.frameworks = list(frameworks)
            metadata.total_files = total_files
            metadata.total_size = total_size
            
        except Exception:
            pass  # If metadata collection fails, continue with empty metadata
        
        return metadata
    
    def _run_detectors(self, project_path: Path, config: ScanConfiguration) -> List[DetectorResult]:
        """Run all enabled detectors."""
        results = []
        
        # Run detectors in parallel for better performance
        with concurrent.futures.ThreadPoolExecutor(max_workers=len(self.detectors)) as executor:
            future_to_detector = {}
            
            for detector_name, detector in self.detectors.items():
                future = executor.submit(detector.detect, project_path)
                future_to_detector[future] = detector_name
            
            for future in concurrent.futures.as_completed(future_to_detector):
                detector_name = future_to_detector[future]
                try:
                    result = future.result(timeout=300)  # 5 minute timeout per detector
                    results.append(result)
                except concurrent.futures.TimeoutError:
                    # Create timeout error result
                    results.append(DetectorResult(
                        detector_name=detector_name,
                        issues=[],
                        execution_time=300.0,
                        files_processed=0,
                        success=False,
                        error_message=f"Detector {detector_name} timed out after 5 minutes"
                    ))
                except Exception as e:
                    # Create error result
                    results.append(DetectorResult(
                        detector_name=detector_name,
                        issues=[],
                        execution_time=0.0,
                        files_processed=0,
                        success=False,
                        error_message=str(e)
                    ))
        
        return results
    
    def _score_and_prioritize_issues(self, issues: List[Issue], metadata: ProjectMetadata) -> List[Issue]:
        """Apply advanced scoring and prioritization to issues."""
        for issue in issues:
            # Adjust scores based on project context
            adjusted_impact = self._adjust_impact_score(issue, metadata)
            adjusted_likelihood = self._adjust_likelihood_score(issue, metadata)
            
            # Recalculate risk score
            issue.impact_score = adjusted_impact
            issue.likelihood_score = adjusted_likelihood
            issue.risk_score = round((adjusted_impact * adjusted_likelihood) / 100, 2)
        
        # Sort by risk score (highest first)
        return sorted(issues, key=lambda x: x.risk_score, reverse=True)
    
    def _adjust_impact_score(self, issue: Issue, metadata: ProjectMetadata) -> int:
        """Adjust impact score based on project context."""
        base_score = issue.impact_score
        
        # Adjust based on project type
        if metadata.project_type in ['django', 'flask', 'fastapi', 'nodejs', 'react']:
            # Web applications - security issues are more critical
            if issue.type in [IssueType.SECURITY_VULNERABILITY, IssueType.HARDCODED_SECRET]:
                base_score = min(100, base_score + 10)
        
        # Adjust based on file location
        if 'test' in issue.location.file_path.lower():
            # Issues in test files are less critical
            base_score = max(10, base_score - 15)
        elif any(pattern in issue.location.file_path.lower() for pattern in ['main', 'index', 'app', 'server']):
            # Issues in main files are more critical
            base_score = min(100, base_score + 10)
        
        # Adjust based on project size
        if metadata.total_files > 1000:
            # Large projects - maintainability issues are more critical
            if issue.type in [IssueType.CODE_COMPLEXITY, IssueType.MAINTAINABILITY]:
                base_score = min(100, base_score + 5)
        
        return base_score
    
    def _adjust_likelihood_score(self, issue: Issue, metadata: ProjectMetadata) -> int:
        """Adjust likelihood score based on project context."""
        base_score = issue.likelihood_score
        
        # Adjust based on frameworks used
        if 'django' in metadata.frameworks:
            # Django projects - certain security issues are more common
            if issue.type == IssueType.SQL_INJECTION:
                base_score = min(100, base_score + 10)
        
        if 'react' in metadata.frameworks:
            # React projects - XSS issues are more likely
            if 'xss' in issue.title.lower():
                base_score = min(100, base_score + 10)
        
        return base_score
    
    def _filter_issues(self, issues: List[Issue], config: ScanConfiguration) -> List[Issue]:
        """Filter issues based on configuration."""
        filtered = []
        
        severity_order = {
            IssueSeverity.INFO: 0,
            IssueSeverity.LOW: 1,
            IssueSeverity.MEDIUM: 2,
            IssueSeverity.HIGH: 3,
            IssueSeverity.CRITICAL: 4
        }
        
        min_severity_level = severity_order[config.min_severity]
        issue_counts_by_type = {}
        
        for issue in issues:
            # Filter by severity
            if severity_order[issue.severity] < min_severity_level:
                continue
            
            # Filter by max issues per type
            issue_type = issue.type
            if issue_type not in issue_counts_by_type:
                issue_counts_by_type[issue_type] = 0
            
            if issue_counts_by_type[issue_type] >= config.max_issues_per_type:
                continue
            
            issue_counts_by_type[issue_type] += 1
            filtered.append(issue)
        
        return filtered
    
    def _generate_summary(self, issues: List[Issue]) -> ScanSummary:
        """Generate summary statistics for the scan."""
        summary = ScanSummary()
        
        summary.total_issues = len(issues)
        
        # Count by severity
        for issue in issues:
            if issue.severity not in summary.issues_by_severity:
                summary.issues_by_severity[issue.severity] = 0
            summary.issues_by_severity[issue.severity] += 1
        
        # Count by type
        for issue in issues:
            if issue.type not in summary.issues_by_type:
                summary.issues_by_type[issue.type] = 0
            summary.issues_by_type[issue.type] += 1
        
        # Top files by issues
        file_issue_counts = {}
        for issue in issues:
            file_path = issue.location.file_path
            if file_path not in file_issue_counts:
                file_issue_counts[file_path] = 0
            file_issue_counts[file_path] += 1
        
        summary.top_files_by_issues = [
            {"file": file_path, "issues": count}
            for file_path, count in sorted(file_issue_counts.items(), key=lambda x: x[1], reverse=True)[:10]
        ]
        
        # Risk distribution
        risk_ranges = {
            "low": 0,      # 0-30
            "medium": 0,   # 31-60
            "high": 0,     # 61-80
            "critical": 0  # 81-100
        }
        
        total_risk = 0
        for issue in issues:
            risk = issue.risk_score
            total_risk += risk
            
            if risk <= 30:
                risk_ranges["low"] += 1
            elif risk <= 60:
                risk_ranges["medium"] += 1
            elif risk <= 80:
                risk_ranges["high"] += 1
            else:
                risk_ranges["critical"] += 1
        
        summary.risk_distribution = risk_ranges
        summary.average_risk_score = round(total_risk / max(len(issues), 1), 2)
        
        return summary
    
    def _generate_metrics(self, detector_results: List[DetectorResult], total_files: int) -> ScanMetrics:
        """Generate scan metrics."""
        metrics = ScanMetrics()
        
        metrics.files_processed = total_files
        total_execution_time = 0
        
        for result in detector_results:
            total_execution_time += result.execution_time
            
            # Store per-detector metrics
            metrics.detector_metrics[result.detector_name] = {
                'execution_time': result.execution_time,
                'files_processed': result.files_processed,
                'issues_found': len(result.issues),
                'success': result.success,
                'error_message': result.error_message
            }
        
        metrics.scan_duration = total_execution_time
        
        return metrics
    
    def get_detector_info(self) -> dict:
        """Get information about available detectors."""
        return {
            'available_detectors': {
                'code_quality': {
                    'name': 'Code Quality Detector',
                    'description': 'Detects code complexity, duplication, and quality issues',
                    'supported_files': ['.py', '.js', '.ts', '.jsx', '.tsx', '.java', '.c', '.cpp']
                },
                'security': {
                    'name': 'Security Detector',
                    'description': 'Detects security vulnerabilities and OWASP Top 10 issues',
                    'supported_files': ['.py', '.js', '.ts', '.json', '.yaml', '.yml', '.env', '.ini']
                },
                'dependency': {
                    'name': 'Dependency Detector',
                    'description': 'Analyzes dependencies for vulnerabilities and issues',
                    'supported_files': ['requirements.txt', 'package.json', 'pyproject.toml', 'Pipfile']
                },
                'structure': {
                    'name': 'Structure Detector',
                    'description': 'Detects architectural and structural issues',
                    'supported_files': ['.py', '.js', '.ts', '.json', '.yaml', '.md']
                },
                'performance': {
                    'name': 'Performance Detector',
                    'description': 'Identifies performance bottlenecks and inefficiencies',
                    'supported_files': ['.py', '.js', '.ts', '.sql', '.json']
                }
            },
            'total_detectors': 5,
            'version': '1.0.0'
        }