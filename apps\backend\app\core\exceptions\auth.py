"""
Authentication and authorization exception classes for the LONI backend application.
"""

from typing import Any, Dict, Optional

from .base import AppException


class PermissionError(AppException):
    """
    Exception raised when user lacks permission for an action.
    
    Used for authorization failures when users attempt to access
    resources or perform actions they don't have permission for.
    """
    
    def __init__(
        self,
        message: str = "Permission denied",
        required_permission: Optional[str] = None
    ):
        """Initialize permission exception."""
        details = {}
        if required_permission:
            details["required_permission"] = required_permission
            
        super().__init__(
            message=message,
            error_code="PERMISSION_DENIED",
            details=details,
            status_code=403
        )


class AuthenticationError(AppException):
    """
    Exception raised when authentication fails.
    
    Used for login failures, invalid tokens, and other
    authentication-related errors.
    """
    
    def __init__(
        self,
        message: str = "Authentication failed",
        auth_method: Optional[str] = None
    ):
        """Initialize authentication exception."""
        details = {}
        if auth_method:
            details["auth_method"] = auth_method
            
        super().__init__(
            message=message,
            error_code="AUTHENTICATION_FAILED",
            details=details,
            status_code=401
        )