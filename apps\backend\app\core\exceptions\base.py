"""
Base exception classes for the LONI backend application.
"""

from typing import Any, Dict, Optional


class AppException(Exception):
    """
    Base application exception.
    
    All custom exceptions should inherit from this class.
    Provides structured error information for consistent error handling.
    """
    
    def __init__(
        self,
        message: str,
        error_code: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None,
        status_code: int = 500
    ):
        """Initialize application exception."""
        super().__init__(message)
        self.message = message
        self.error_code = error_code or self.__class__.__name__
        self.details = details or {}
        self.status_code = status_code
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert exception to dictionary for API responses."""
        return {
            "message": self.message,
            "error_code": self.error_code,
            "details": self.details,
            "status_code": self.status_code
        }