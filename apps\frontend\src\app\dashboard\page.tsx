import { AppLayout } from "@/components/layout/AppLayout";
import { Card } from "@/components/common/Card";
import { Button } from "@/components/common/Button";
import { ROUTES } from "@/lib/constants";
import Link from "next/link";
import {
  AudioLines,
  Bot,
  FileAudio,
  TrendingUp,
  Clock,
  CheckCircle,
  AlertCircle
} from "lucide-react";

export default function Dashboard() {
  const stats = [
    {
      title: "Total Transcriptions",
      value: "1,234",
      change: "+12%",
      icon: <FileAudio className="h-5 w-5" />,
      trend: "up"
    },
    {
      title: "Active Models",
      value: "8",
      change: "+2",
      icon: <Bot className="h-5 w-5" />,
      trend: "up"
    },
    {
      title: "Processing Time",
      value: "2.3s",
      change: "-15%",
      icon: <Clock className="h-5 w-5" />,
      trend: "down"
    },
    {
      title: "Success Rate",
      value: "99.5%",
      change: "+0.2%",
      icon: <CheckCircle className="h-5 w-5" />,
      trend: "up"
    }
  ];

  const recentTranscriptions = [
    {
      id: "1",
      fileName: "meeting_notes.wav",
      duration: "15:32",
      status: "completed",
      timestamp: "2024-01-15 14:30"
    },
    {
      id: "2",
      fileName: "interview.mp3",
      duration: "45:12",
      status: "processing",
      timestamp: "2024-01-15 13:45"
    },
    {
      id: "3",
      fileName: "lecture.flac",
      duration: "1:02:18",
      status: "failed",
      timestamp: "2024-01-15 12:20"
    }
  ];

  return (
    <AppLayout>
      <div className="space-y-8">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold">Dashboard</h1>
            <p className="text-muted-foreground">Overview of your AI transcription activities</p>
          </div>
          <Button gradient>
            <Link href={ROUTES.TRANSCRIBE} className="flex items-center gap-2">
              <AudioLines className="h-4 w-4" />
              New Transcription
            </Link>
          </Button>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {stats.map((stat, index) => (
            <Card key={index} className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-muted-foreground">{stat.title}</p>
                  <p className="text-2xl font-bold">{stat.value}</p>
                  <p className={`text-sm flex items-center gap-1 ${
                    stat.trend === 'up' ? 'text-green-600' : 'text-red-600'
                  }`}>
                    <TrendingUp className={`h-3 w-3 ${
                      stat.trend === 'down' ? 'rotate-180' : ''
                    }`} />
                    {stat.change}
                  </p>
                </div>
                <div className="text-primary">{stat.icon}</div>
              </div>
            </Card>
          ))}
        </div>

        {/* Recent Activity */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card className="p-6">
            <h2 className="text-xl font-semibold mb-4">Recent Transcriptions</h2>
            <div className="space-y-4">
              {recentTranscriptions.map((item) => (
                <div key={item.id} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex-1">
                    <p className="font-medium">{item.fileName}</p>
                    <p className="text-sm text-muted-foreground">
                      {item.duration} • {item.timestamp}
                    </p>
                  </div>
                  <div className={`flex items-center gap-2 ${
                    item.status === 'completed' ? 'text-green-600' :
                    item.status === 'processing' ? 'text-blue-600' : 'text-red-600'
                  }`}>
                    {item.status === 'completed' ? (
                      <CheckCircle className="h-4 w-4" />
                    ) : item.status === 'processing' ? (
                      <Clock className="h-4 w-4" />
                    ) : (
                      <AlertCircle className="h-4 w-4" />
                    )}
                    <span className="text-sm capitalize">{item.status}</span>
                  </div>
                </div>
              ))}
            </div>
            <div className="mt-4">
              <Button variant="outline" size="sm">
                <Link href={ROUTES.TRANSCRIBE}>View All</Link>
              </Button>
            </div>
          </Card>

          <Card className="p-6">
            <h2 className="text-xl font-semibold mb-4">Model Status</h2>
            <div className="space-y-4">
              <div className="flex items-center justify-between p-3 border rounded-lg">
                <div>
                  <p className="font-medium">Whisper Base</p>
                  <p className="text-sm text-muted-foreground">Audio Transcription</p>
                </div>
                <div className="flex items-center gap-2 text-green-600">
                  <CheckCircle className="h-4 w-4" />
                  <span className="text-sm">Active</span>
                </div>
              </div>
              <div className="flex items-center justify-between p-3 border rounded-lg">
                <div>
                  <p className="font-medium">Llama 2 7B</p>
                  <p className="text-sm text-muted-foreground">Text Generation</p>
                </div>
                <div className="flex items-center gap-2 text-yellow-600">
                  <Clock className="h-4 w-4" />
                  <span className="text-sm">Loading</span>
                </div>
              </div>
              <div className="flex items-center justify-between p-3 border rounded-lg">
                <div>
                  <p className="font-medium">CodeLlama</p>
                  <p className="text-sm text-muted-foreground">Code Generation</p>
                </div>
                <div className="flex items-center gap-2 text-gray-500">
                  <AlertCircle className="h-4 w-4" />
                  <span className="text-sm">Inactive</span>
                </div>
              </div>
            </div>
            <div className="mt-4">
              <Button variant="outline" size="sm">
                <Link href={ROUTES.MODELS}>Manage Models</Link>
              </Button>
            </div>
          </Card>
        </div>

        {/* Quick Actions */}
        <Card className="p-6">
          <h2 className="text-xl font-semibold mb-4">Quick Actions</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Button className="h-20 flex flex-col gap-2">
              <Link href={ROUTES.TRANSCRIBE} className="flex flex-col gap-2">
                <AudioLines className="h-6 w-6" />
                <span>Start Transcription</span>
              </Link>
            </Button>
            <Button variant="outline" className="h-20 flex flex-col gap-2">
              <Link href={ROUTES.MODELS} className="flex flex-col gap-2">
                <Bot className="h-6 w-6" />
                <span>Manage Models</span>
              </Link>
            </Button>
            <Button variant="outline" className="h-20 flex flex-col gap-2">
              <Link href={ROUTES.DESIGN} className="flex flex-col gap-2">
                <FileAudio className="h-6 w-6" />
                <span>Design System</span>
              </Link>
            </Button>
          </div>
        </Card>
      </div>
    </AppLayout>
  );
}

