"""
Project Scanner Module

This module provides functionality for scanning projects to detect various types of issues:
- Code quality issues (complexity, duplicates, style violations)
- Security vulnerabilities (hard-coded secrets, unsafe patterns)
- Dependency issues (outdated packages, known vulnerabilities)
- Structure problems (circular imports, unused files)
- Performance issues (large files, inefficient patterns)
"""

from .scanner import ProjectScanner
from .models import ScanRequest, ScanResponse, Issue, IssueType, IssueSeverity
from .detectors import (
    CodeQualityDetector,
    SecurityDetector,
    DependencyDetector,
    StructureDetector,
    PerformanceDetector
)

__all__ = [
    "ProjectScanner",
    "ScanRequest",
    "ScanResponse", 
    "Issue",
    "IssueType",
    "IssueSeverity",
    "CodeQualityDetector",
    "SecurityDetector",
    "DependencyDetector",
    "StructureDetector",
    "PerformanceDetector"
]