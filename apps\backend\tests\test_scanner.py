"""
Tests for the project scanner functionality.

Covers:
- Scanner core functionality
- Issue detection algorithms
- API endpoints
- Storage operations
"""

import pytest
import tempfile
import json
from pathlib import Path
from unittest.mock import Mock, patch
from fastapi.testclient import TestClient

from app.scanner.scanner import ProjectScanner
from app.scanner.models import (
    ScanRequest, ScanTarget, ScanConfig, IssueType, IssueSeverity
)
from app.scanner.detectors import (
    CodeQualityDetector, SecurityDetector, DependencyDetector,
    StructureDetector, PerformanceDetector
)
from app.scanner.storage import ScanStorage
from app.app import create_app


class TestProjectScanner:
    """Test the core ProjectScanner class."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.scanner = ProjectScanner()
    
    def test_scanner_initialization(self):
        """Test scanner initializes with all detectors."""
        assert len(self.scanner.detectors) == 5
        assert IssueType.CODE_QUALITY in self.scanner.detectors
        assert IssueType.SECURITY in self.scanner.detectors
        assert IssueType.DEPENDENCY in self.scanner.detectors
        assert IssueType.STRUCTURE in self.scanner.detectors
        assert IssueType.PERFORMANCE in self.scanner.detectors
    
    @pytest.mark.asyncio
    async def test_file_discovery(self):
        """Test file discovery with include/exclude patterns."""
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            
            # Create test files
            (temp_path / "test.py").write_text("print('hello')")
            (temp_path / "test.js").write_text("console.log('hello');")
            (temp_path / "test.txt").write_text("text file")
            (temp_path / "node_modules").mkdir()
            (temp_path / "node_modules" / "module.js").write_text("module code")
            
            target = ScanTarget(
                project_path=str(temp_path),
                include_patterns=["**/*.py", "**/*.js"],
                exclude_patterns=["**/node_modules/**"]
            )
            
            files = await self.scanner._discover_files(target)
            file_names = [f.name for f in files]
            
            assert "test.py" in file_names
            assert "test.js" in file_names
            assert "test.txt" not in file_names
            assert "module.js" not in file_names
    
    @pytest.mark.asyncio
    async def test_scan_project_complete(self):
        """Test complete project scanning workflow."""
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            
            # Create a test Python file with issues
            test_file = temp_path / "test.py"
            test_file.write_text("""
# Test file with various issues
def function_with_long_name_that_violates_conventions():
    password = "hardcoded_password_123"  # Security issue
    for i in range(len(some_list)):      # Code quality issue
        print(i)
    return password

class verylongclassname:  # Naming convention issue
    pass
""")
            
            request = ScanRequest(
                target=ScanTarget(project_path=str(temp_path)),
                config=ScanConfig(scan_types=[IssueType.CODE_QUALITY, IssueType.SECURITY]),
                user_id="test_user",
                request_id="test_request"
            )
            
            result = await self.scanner.scan_project(request)
            
            assert result.status == "completed"
            assert result.scan_id is not None
            assert len(result.issues) > 0
            assert result.summary is not None
            assert result.summary.total_files_scanned == 1


class TestCodeQualityDetector:
    """Test the code quality detector."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.detector = CodeQualityDetector()
    
    def test_python_function_too_long(self):
        """Test detection of overly long Python functions."""
        code = """
def long_function():
""" + "\n".join([f"    line_{i} = {i}" for i in range(60)]) + """
    return sum(range(60))
"""
        
        issues = self.detector.detect_issues("test.py", code)
        long_function_issues = [i for i in issues if "too long" in i.title.lower()]
        assert len(long_function_issues) > 0
        assert long_function_issues[0].severity == IssueSeverity.MEDIUM
    
    def test_high_complexity_function(self):
        """Test detection of high complexity functions."""
        code = """
def complex_function(x):
    if x > 10:
        if x > 20:
            if x > 30:
                if x > 40:
                    if x > 50:
                        if x > 60:
                            return "very high"
                        else:
                            return "high"
                    else:
                        return "medium-high"
                else:
                    return "medium"
            else:
                return "low-medium"
        else:
            return "low"
    else:
        return "very low"
"""
        
        issues = self.detector.detect_issues("test.py", code)
        complexity_issues = [i for i in issues if "complexity" in i.title.lower()]
        assert len(complexity_issues) > 0
    
    def test_unsupported_file_types(self):
        """Test that unsupported file types return no issues."""
        issues = self.detector.detect_issues("test.txt", "some text content")
        assert len(issues) == 0


class TestSecurityDetector:
    """Test the security detector."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.detector = SecurityDetector()
    
    def test_hardcoded_password_detection(self):
        """Test detection of hardcoded passwords."""
        code = """
DATABASE_PASSWORD = "super_secret_password_123"
api_key = "sk-1234567890abcdef"
"""
        
        issues = self.detector.detect_issues("config.py", code)
        secret_issues = [i for i in issues if "hardcoded" in i.title.lower()]
        assert len(secret_issues) > 0
        assert any("password" in issue.title.lower() for issue in secret_issues)
    
    def test_sql_injection_detection(self):
        """Test detection of SQL injection vulnerabilities."""
        code = """
def get_user(user_id):
    query = "SELECT * FROM users WHERE id = '" + user_id + "'"
    return execute_query(query)
"""
        
        issues = self.detector.detect_issues("database.py", code)
        sql_issues = [i for i in issues if "sql injection" in i.title.lower()]
        assert len(sql_issues) > 0
    
    def test_insecure_function_usage(self):
        """Test detection of insecure function usage."""
        code = """
import subprocess
user_input = get_user_input()
subprocess.call(user_input, shell=True)  # Dangerous
result = eval(user_input)  # Also dangerous
"""
        
        issues = self.detector.detect_issues("unsafe.py", code)
        insecure_issues = [i for i in issues if "insecure" in i.title.lower()]
        assert len(insecure_issues) > 0


class TestDependencyDetector:
    """Test the dependency detector."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.detector = DependencyDetector()
    
    def test_package_json_analysis(self):
        """Test analysis of package.json files."""
        package_json = json.dumps({
            "name": "test-project",
            "dependencies": {
                "lodash": "^3.0.0",  # Vulnerable version
                "express": "*"       # Unpinned version
            }
        })
        
        issues = self.detector.detect_issues("package.json", package_json)
        dependency_issues = [i for i in issues if "dependency" in i.title.lower()]
        assert len(dependency_issues) > 0
    
    def test_requirements_txt_analysis(self):
        """Test analysis of requirements.txt files."""
        requirements = """
# Dependencies
requests==2.25.1
flask  # No version specified
django>=3.0
"""
        
        issues = self.detector.detect_issues("requirements.txt", requirements)
        unpinned_issues = [i for i in issues if "unpinned" in i.title.lower()]
        assert len(unpinned_issues) > 0


class TestPerformanceDetector:
    """Test the performance detector."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.detector = PerformanceDetector()
    
    def test_large_file_detection(self):
        """Test detection of large files."""
        # Create content that exceeds the large file threshold
        large_content = "x" * 1_500_000  # 1.5MB
        
        issues = self.detector.detect_issues("large_file.py", large_content)
        size_issues = [i for i in issues if "large file" in i.title.lower()]
        assert len(size_issues) > 0
    
    def test_inefficient_python_patterns(self):
        """Test detection of inefficient Python patterns."""
        code = """
# Inefficient patterns
for i in range(len(my_list)):
    print(my_list[i])

if 'key' in my_dict.keys():
    print("found")

result = []
for item in items:
    result += [process(item)]
"""
        
        issues = self.detector.detect_issues("inefficient.py", code)
        pattern_issues = [i for i in issues if "inefficient" in i.title.lower()]
        assert len(pattern_issues) > 0


class TestScanStorage:
    """Test the scan storage functionality."""
    
    def setup_method(self):
        """Set up test fixtures."""
        # Use in-memory database for testing
        self.storage = ScanStorage(":memory:")
    
    @pytest.mark.asyncio
    async def test_store_and_retrieve_scan(self):
        """Test storing and retrieving scan results."""
        from app.scanner.models import Issue, ScanResponse, ScanSummary
        from datetime import datetime, timezone
        
        # Create a mock scan result
        issues = [
            Issue(
                id="issue_1",
                type=IssueType.CODE_QUALITY,
                severity=IssueSeverity.MEDIUM,
                title="Test issue",
                description="This is a test issue",
                file_path="/test/file.py",
                line_number=10,
                rule_id="test_rule"
            )
        ]
        
        summary = ScanSummary(
            total_files_scanned=5,
            total_issues_found=1,
            issues_by_type={IssueType.CODE_QUALITY: 1},
            issues_by_severity={IssueSeverity.MEDIUM: 1},
            scan_duration_seconds=15.5,
            files_with_issues=1,
            average_issues_per_file=0.2
        )
        
        scan_result = ScanResponse(
            scan_id="test_scan_123",
            request_id="test_request_123",
            user_id="test_user",
            status="completed",
            created_at=datetime.now(timezone.utc),
            completed_at=datetime.now(timezone.utc),
            target=ScanTarget(project_path="/test/project"),
            config=ScanConfig(),
            summary=summary,
            issues=issues,
            errors=[]
        )
        
        # Store the scan result
        success = await self.storage.store_scan_result(scan_result)
        assert success
        
        # Retrieve the scan result
        retrieved = await self.storage.get_scan_result("test_scan_123", "test_user")
        assert retrieved is not None
        assert retrieved["scan_id"] == "test_scan_123"
        assert len(retrieved["issues"]) == 1
    
    @pytest.mark.asyncio
    async def test_list_user_scans(self):
        """Test listing scans for a user."""
        # This would require setting up multiple scan results
        scans = await self.storage.list_user_scans("test_user")
        assert isinstance(scans, list)


class TestScannerAPI:
    """Test the scanner API endpoints."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.app = create_app()
        self.client = TestClient(self.app)
    
    def test_health_endpoint(self):
        """Test the scanner health endpoint."""
        response = self.client.get("/api/v1/scanner/health")
        assert response.status_code == 200
        
        data = response.json()
        assert "status" in data
        assert "components" in data
    
    @patch('app.scanner.api.verify_api_key')
    def test_start_scan_endpoint(self, mock_verify):
        """Test starting a scan via API."""
        mock_verify.return_value = {"user_id": "test_user", "permissions": ["scan:write"]}
        
        with tempfile.TemporaryDirectory() as temp_dir:
            scan_request = {
                "target": {
                    "project_path": temp_dir,
                    "include_patterns": ["**/*.py"],
                    "exclude_patterns": ["**/venv/**"]
                },
                "config": {
                    "scan_types": ["code_quality", "security"]
                },
                "user_id": "test_user",
                "request_id": "test_request_123"
            }
            
            response = self.client.post(
                "/api/v1/scanner/scan",
                json=scan_request,
                headers={"Authorization": "Bearer test_token"}
            )
            
            assert response.status_code == 200
            data = response.json()
            assert "scan_id" in data
            assert data["status"] == "initializing"
    
    @patch('app.scanner.api.verify_api_key')
    def test_invalid_project_path(self, mock_verify):
        """Test handling of invalid project paths."""
        mock_verify.return_value = {"user_id": "test_user", "permissions": ["scan:write"]}
        
        scan_request = {
            "target": {
                "project_path": "/nonexistent/path",
                "include_patterns": ["**/*.py"]
            },
            "config": {"scan_types": ["code_quality"]},
            "user_id": "test_user",
            "request_id": "test_request_123"
        }
        
        response = self.client.post(
            "/api/v1/scanner/scan",
            json=scan_request,
            headers={"Authorization": "Bearer test_token"}
        )
        
        assert response.status_code == 422  # Validation error
    
    def test_unauthorized_access(self):
        """Test unauthorized access to scanner endpoints."""
        response = self.client.get("/api/v1/scanner/scans")
        assert response.status_code == 403  # No Authorization header
        
        response = self.client.get(
            "/api/v1/scanner/scans",
            headers={"Authorization": "Bearer invalid_token"}
        )
        assert response.status_code == 401  # Invalid token


class TestIntegration:
    """Integration tests for the complete scanner system."""
    
    @pytest.mark.asyncio
    async def test_end_to_end_scan(self):
        """Test complete end-to-end scanning workflow."""
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            
            # Create a realistic project structure
            (temp_path / "main.py").write_text("""
def main():
    password = "admin123"  # Security issue
    for i in range(len(data)):  # Code quality issue
        process(data[i])
""")
            
            (temp_path / "package.json").write_text(json.dumps({
                "name": "test-project",
                "dependencies": {
                    "lodash": "^3.0.0"  # Vulnerable dependency
                }
            }))
            
            scanner = ProjectScanner()
            request = ScanRequest(
                target=ScanTarget(project_path=str(temp_path)),
                config=ScanConfig(
                    scan_types=[IssueType.CODE_QUALITY, IssueType.SECURITY, IssueType.DEPENDENCY]
                ),
                user_id="integration_test_user",
                request_id="integration_test_request"
            )
            
            result = await scanner.scan_project(request)
            
            # Verify results
            assert result.status == "completed"
            assert len(result.issues) > 0
            
            # Check that different types of issues were found
            issue_types = {issue.type for issue in result.issues}
            assert IssueType.SECURITY in issue_types  # Hardcoded password
            assert IssueType.CODE_QUALITY in issue_types  # range(len()) pattern
            
            # Verify summary statistics
            assert result.summary.total_files_scanned > 0
            assert result.summary.total_issues_found == len(result.issues)
            assert result.summary.scan_duration_seconds > 0


if __name__ == "__main__":
    pytest.main([__file__])