#!/bin/bash
# Development environment setup automation script
# This script sets up the complete development environment with all tooling

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Script configuration
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/../.." && pwd)"
BACKEND_DIR="$PROJECT_ROOT/apps/backend"
FRONTEND_DIR="$PROJECT_ROOT/apps/frontend"
LOG_FILE="$PROJECT_ROOT/apps/data/logs/dev-setup.log"

# Logging function
log_message() {
    local level=$1
    local message=$2
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo -e "${!level}[$level] $timestamp: $message${NC}" | tee -a "$LOG_FILE"
}

# Error handling
error_exit() {
    log_message "RED" "ERROR: $1"
    exit 1
}

# Success message
success_message() {
    log_message "GREEN" "$1"
}

# Warning message
warning_message() {
    log_message "YELLOW" "$1"
}

# Info message
info_message() {
    log_message "BLUE" "$1"
}

# Create log directory
mkdir -p "$PROJECT_ROOT/apps/data/logs"

info_message "Starting development environment setup..."

# Check system requirements
check_requirements() {
    info_message "Checking system requirements..."
    
    # Check Python
    if ! command -v python3.11 &> /dev/null; then
        error_exit "Python 3.11+ is required but not found"
    fi
    
    # Check Node.js
    if ! command -v node &> /dev/null; then
        error_exit "Node.js is required but not found"
    fi
    
    # Check Bun
    if ! command -v bun &> /dev/null; then
        warning_message "Bun not found. Installing..."
        curl -fsSL https://bun.sh/install | bash
        export PATH="$HOME/.bun/bin:$PATH"
    fi
    
    # Check UV
    if ! command -v uv &> /dev/null; then
        warning_message "UV not found. Installing..."
        curl -LsSf https://astral.sh/uv/install.sh | sh
        export PATH="$HOME/.cargo/bin:$PATH"
    fi
    
    # Check Docker
    if ! command -v docker &> /dev/null; then
        warning_message "Docker not found. Please install Docker manually."
    fi
    
    success_message "System requirements check completed"
}

# Setup backend environment
setup_backend() {
    info_message "Setting up backend environment..."
    
    cd "$BACKEND_DIR"
    
    # Create virtual environment and install dependencies
    info_message "Installing Python dependencies..."
    uv sync --dev || error_exit "Failed to install Python dependencies"
    
    # Copy environment file if it doesn't exist
    if [[ ! -f .env ]]; then
        if [[ -f env.example ]]; then
            cp env.example .env
            warning_message "Created .env from env.example. Please configure your environment variables."
        fi
    fi
    
    success_message "Backend environment setup completed"
}

# Setup frontend environment
setup_frontend() {
    info_message "Setting up frontend environment..."
    
    cd "$FRONTEND_DIR"
    
    # Install dependencies
    info_message "Installing JavaScript dependencies..."
    bun install || error_exit "Failed to install JavaScript dependencies"
    
    success_message "Frontend environment setup completed"
}

# Setup pre-commit hooks
setup_precommit() {
    info_message "Setting up pre-commit hooks..."
    
    cd "$PROJECT_ROOT"
    
    # Install pre-commit in backend environment
    cd "$BACKEND_DIR"
    uv run pre-commit install || error_exit "Failed to install pre-commit hooks"
    
    # Run pre-commit on all files to verify setup
    info_message "Running pre-commit on all files (first run may be slow)..."
    cd "$PROJECT_ROOT"
    "$BACKEND_DIR/venv/bin/pre-commit" run --all-files || warning_message "Pre-commit found issues. Run manually to fix."
    
    success_message "Pre-commit hooks setup completed"
}

# Setup development tools
setup_dev_tools() {
    info_message "Setting up development tools..."
    
    # Install global tools
    if command -v npm &> /dev/null; then
        npm install -g @commitizen/cli || warning_message "Failed to install commitizen globally"
    fi
    
    # Create secrets baseline for detect-secrets
    cd "$PROJECT_ROOT"
    if [[ ! -f .secrets.baseline ]]; then
        "$BACKEND_DIR/venv/bin/detect-secrets" scan --baseline .secrets.baseline || warning_message "Failed to create secrets baseline"
    fi
    
    success_message "Development tools setup completed"
}

# Verify installation
verify_installation() {
    info_message "Verifying installation..."
    
    local errors=0
    
    # Test backend linting
    cd "$BACKEND_DIR"
    if ! uv run ruff check app/ --quiet; then
        warning_message "Backend linting check failed"
        ((errors++))
    fi
    
    # Test frontend linting
    cd "$FRONTEND_DIR"
    if ! bun run lint:check; then
        warning_message "Frontend linting check failed"
        ((errors++))
    fi
    
    # Test backend type checking
    cd "$BACKEND_DIR"
    if ! uv run mypy app/; then
        warning_message "Backend type checking failed"
        ((errors++))
    fi
    
    # Test frontend type checking
    cd "$FRONTEND_DIR"
    if ! bun run type-check; then
        warning_message "Frontend type checking failed"
        ((errors++))
    fi
    
    if [[ $errors -eq 0 ]]; then
        success_message "All verification checks passed!"
    else
        warning_message "Some verification checks failed. Please review the issues above."
    fi
}

# Print setup completion message
print_completion_message() {
    info_message "Development environment setup completed!"
    echo ""
    info_message "Available commands:"
    echo "  Backend:"
    echo "    cd apps/backend && uv run ruff check app/ --fix  # Lint and fix"
    echo "    cd apps/backend && uv run ruff format app/       # Format code"
    echo "    cd apps/backend && uv run pytest tests/          # Run tests"
    echo "    cd apps/backend && uv run mypy app/               # Type checking"
    echo ""
    echo "  Frontend:"
    echo "    cd apps/frontend && bun run lint                  # Lint and fix"
    echo "    cd apps/frontend && bun run format                # Format code"
    echo "    cd apps/frontend && bun run test                  # Run tests"
    echo "    cd apps/frontend && bun run type-check            # Type checking"
    echo ""
    echo "  Pre-commit:"
    echo "    pre-commit run --all-files                        # Run all hooks"
    echo ""
    echo "  VSCode:"
    echo "    Open the project in VSCode for integrated development experience"
    echo "    Recommended extensions will be suggested automatically"
}

# Main execution
main() {
    check_requirements
    setup_backend
    setup_frontend
    setup_precommit
    setup_dev_tools
    verify_installation
    print_completion_message
    
    success_message "Setup completed successfully! Happy coding! 🚀"
}

# Run main function
main "$@"