#!/usr/bin/env python3
import os
import re

def analyze_file_sizes():
    """Analyze file sizes to check against complexity limits."""
    large_files = []

    for root, dirs, files in os.walk("apps/backend/app"):
        for file in files:
            if file.endswith(".py"):
                filepath = os.path.join(root, file)
                try:
                    with open(filepath, 'r', encoding='utf-8') as f:
                        lines = f.readlines()
                        line_count = len(lines)

                    if line_count > 500:  # Hard cap from guidelines
                        large_files.append((filepath, line_count))
                except Exception as e:
                    continue

    return large_files

def analyze_function_complexity():
    """Analyze function complexity (length and cyclomatic complexity indicators)."""
    complex_functions = []

    for root, dirs, files in os.walk("apps/backend/app"):
        for file in files:
            if file.endswith(".py"):
                filepath = os.path.join(root, file)
                try:
                    with open(filepath, 'r', encoding='utf-8') as f:
                        content = f.read()

                    # Find functions and their line counts
                    functions = re.findall(r'def\s+(\w+)\([^)]*\):', content)
                    lines = content.split('\n')

                    current_func = None
                    func_start = -1

                    for i, line in enumerate(lines):
                        # Check for function definition
                        func_match = re.match(r'def\s+(\w+)\([^)]*\):', line.strip())
                        if func_match:
                            if current_func:
                                # Calculate previous function length
                                func_length = i - func_start
                                if func_length > 30:  # Hard cap from guidelines
                                    complex_functions.append((filepath, current_func, func_length))

                            current_func = func_match.group(1)
                            func_start = i
                        elif line.strip() == '' and current_func and i - func_start > 30:
                            # Function ended (blank line after long function)
                            complex_functions.append((filepath, current_func, i - func_start))
                            current_func = None
                            func_start = -1

                    # Check last function
                    if current_func and len(lines) - func_start > 30:
                        complex_functions.append((filepath, current_func, len(lines) - func_start))

                except Exception as e:
                    continue

    return complex_functions

if __name__ == "__main__":
    print("=== LARGE FILES (>500 LOC) ===")
    large_files = analyze_file_sizes()
    for filepath, size in large_files:
        print(f"{filepath}: {size} lines")

    print("\n=== COMPLEX FUNCTIONS (>30 LOC) ===")
    complex_functions = analyze_function_complexity()
    for filepath, func_name, size in complex_functions:
        print(f"{filepath}::{func_name}: {size} lines")
