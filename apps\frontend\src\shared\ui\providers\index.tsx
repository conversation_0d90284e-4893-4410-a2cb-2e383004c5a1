"use client";

import * as React from "react";
import { ThemeProvider } from "@/shared/ui/providers/theme-provider";
import { CanvasProvider } from "@/shared/ui/providers/canvas-provider";
import { DesignSystemProvider } from "@/shared/ui/providers/design-system-provider";
import { ToastProvider } from "@/shared/ui/toast";

type ProvidersProps = {
  children: React.ReactNode;
};

export function Providers({ children }: ProvidersProps) {
  return (
    <ThemeProvider attribute="class" defaultTheme="system" enableSystem>
      <ToastProvider>
        <DesignSystemProvider>
          <CanvasProvider>{children}</CanvasProvider>
        </DesignSystemProvider>
      </ToastProvider>
    </ThemeProvider>
  );
}

export { useCanvas } from "@/shared/ui/providers/canvas-provider";
export { useThemeContext } from "@/shared/ui/providers/theme-provider";
export { useToast } from "@/shared/ui/toast";
export { useDesignSystem } from "@/shared/ui/providers/design-system-provider";