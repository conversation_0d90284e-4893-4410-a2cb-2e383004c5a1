"""
FastAPI endpoints for the project scanner.

Provides REST API endpoints for:
- Initiating project scans
- Checking scan progress
- Retrieving scan results
- Managing scanner health
"""

import asyncio
import uuid
from typing import List, Optional, Dict, Any
from datetime import datetime, timezone
from fastapi import APIRouter, HTTPException, Depends, BackgroundTasks, Security
from fastapi.security import HTTPAuthorizationCredentials, HTTPBearer
from fastapi.responses import JSONResponse

from ..logger import get_logger
from .scanner import ProjectScanner
from .models import (
    ScanRequest, ScanResponse, ScanProgress, ErrorResponse, HealthResponse,
    IssueType, IssueSeverity
)

logger = get_logger("scanner_api")
security = HTTPBearer()

# Initialize the scanner
scanner = ProjectScanner()

# Create router
router = APIRouter(prefix="/api/v1/scanner", tags=["scanner"])

# Store for active scans (in production, use Redis or database)
active_scans: Dict[str, ScanResponse] = {}


async def verify_api_key(credentials: HTTPAuthorizationCredentials = Security(security)) -> Dict[str, Any]:
    """
    Verify API key for scanner endpoints.
    
    Args:
        credentials: HTTP Bearer token credentials
        
    Returns:
        User information dictionary
        
    Raises:
        HTTPException: If API key is invalid
    """
    # In production, implement proper API key validation
    # For now, we'll accept any token that looks like a valid format
    token = credentials.credentials
    
    if not token or len(token) < 10:
        raise HTTPException(
            status_code=401,
            detail="Invalid API key format"
        )
    
    # Mock user info - in production, extract from token/database
    return {
        "user_id": "scanner_user_" + token[:8],
        "permissions": ["scan:read", "scan:write"]
    }


@router.post("/scan", response_model=ScanResponse)
async def start_scan(
    request: ScanRequest,
    background_tasks: BackgroundTasks,
    user: Dict[str, Any] = Depends(verify_api_key)
):
    """
    Start a new project scan.
    
    Args:
        request: Scan request with target and configuration
        background_tasks: FastAPI background tasks
        user: Authenticated user information
        
    Returns:
        ScanResponse: Initial scan response with scan ID
    """
    try:
        logger.info(
            "Starting new scan",
            user_id=request.user_id,
            project_path=request.target.project_path,
            scan_types=request.config.scan_types
        )
        
        # Validate user permission
        if request.user_id != user["user_id"]:
            raise HTTPException(
                status_code=403,
                detail="User ID mismatch"
            )
        
        # Generate scan ID
        scan_id = str(uuid.uuid4())
        
        # Create initial response
        response = ScanResponse(
            scan_id=scan_id,
            request_id=request.request_id,
            user_id=request.user_id,
            status="initializing",
            created_at=datetime.now(timezone.utc),
            target=request.target,
            config=request.config,
            issues=[],
            errors=[]
        )
        
        # Store initial scan state
        active_scans[scan_id] = response
        
        # Start scan in background
        background_tasks.add_task(run_scan_background, request, scan_id)
        
        logger.info(
            "Scan initiated",
            scan_id=scan_id,
            user_id=request.user_id
        )
        
        return response
        
    except Exception as e:
        logger.error(
            "Error starting scan",
            error=str(e),
            error_type=type(e).__name__,
            user_id=request.user_id if request else "unknown"
        )
        raise HTTPException(
            status_code=500,
            detail=f"Failed to start scan: {str(e)}"
        )


async def run_scan_background(request: ScanRequest, scan_id: str):
    """
    Run the actual scan in the background.
    
    Args:
        request: Scan request
        scan_id: Unique scan identifier
    """
    try:
        # Update scan status
        if scan_id in active_scans:
            active_scans[scan_id].status = "running"
        
        # Perform the scan
        result = await scanner.scan_project(request)
        
        # Update with final results
        result.scan_id = scan_id
        active_scans[scan_id] = result
        
        logger.info(
            "Scan completed",
            scan_id=scan_id,
            total_issues=len(result.issues),
            status=result.status
        )
        
    except Exception as e:
        logger.error(
            "Error during background scan",
            scan_id=scan_id,
            error=str(e),
            error_type=type(e).__name__
        )
        
        # Update with error status
        if scan_id in active_scans:
            active_scans[scan_id].status = "failed"
            active_scans[scan_id].errors.append(str(e))
            active_scans[scan_id].completed_at = datetime.now(timezone.utc)


@router.get("/scan/{scan_id}", response_model=ScanResponse)
async def get_scan_results(
    scan_id: str,
    user: Dict[str, Any] = Depends(verify_api_key)
):
    """
    Get scan results by scan ID.
    
    Args:
        scan_id: Unique scan identifier
        user: Authenticated user information
        
    Returns:
        ScanResponse: Complete scan results
    """
    try:
        if scan_id not in active_scans:
            raise HTTPException(
                status_code=404,
                detail=f"Scan {scan_id} not found"
            )
        
        scan_result = active_scans[scan_id]
        
        # Verify user has access to this scan
        if scan_result.user_id != user["user_id"]:
            raise HTTPException(
                status_code=403,
                detail="Access denied to this scan"
            )
        
        logger.info(
            "Scan results retrieved",
            scan_id=scan_id,
            status=scan_result.status,
            user_id=user["user_id"]
        )
        
        return scan_result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Error retrieving scan results",
            scan_id=scan_id,
            error=str(e),
            error_type=type(e).__name__
        )
        raise HTTPException(
            status_code=500,
            detail=f"Failed to retrieve scan results: {str(e)}"
        )


@router.get("/scan/{scan_id}/progress", response_model=ScanProgress)
async def get_scan_progress(
    scan_id: str,
    user: Dict[str, Any] = Depends(verify_api_key)
):
    """
    Get current scan progress.
    
    Args:
        scan_id: Unique scan identifier
        user: Authenticated user information
        
    Returns:
        ScanProgress: Current scan progress
    """
    try:
        # Check if scan exists
        if scan_id not in active_scans:
            raise HTTPException(
                status_code=404,
                detail=f"Scan {scan_id} not found"
            )
        
        scan_result = active_scans[scan_id]
        
        # Verify user has access
        if scan_result.user_id != user["user_id"]:
            raise HTTPException(
                status_code=403,
                detail="Access denied to this scan"
            )
        
        # Get progress from scanner
        progress = await scanner.get_scan_progress(scan_id)
        
        if progress is None:
            # If no active progress, create from scan result
            if scan_result.status == "completed":
                progress = ScanProgress(
                    scan_id=scan_id,
                    status="completed",
                    progress_percentage=100.0,
                    files_scanned=scan_result.summary.total_files_scanned if scan_result.summary else 0,
                    total_files=scan_result.summary.total_files_scanned if scan_result.summary else 0,
                    issues_found=len(scan_result.issues)
                )
            else:
                progress = ScanProgress(
                    scan_id=scan_id,
                    status=scan_result.status,
                    progress_percentage=0.0,
                    files_scanned=0,
                    total_files=0,
                    issues_found=len(scan_result.issues)
                )
        
        return progress
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Error retrieving scan progress",
            scan_id=scan_id,
            error=str(e),
            error_type=type(e).__name__
        )
        raise HTTPException(
            status_code=500,
            detail=f"Failed to retrieve scan progress: {str(e)}"
        )


@router.delete("/scan/{scan_id}")
async def cancel_scan(
    scan_id: str,
    user: Dict[str, Any] = Depends(verify_api_key)
):
    """
    Cancel a running scan.
    
    Args:
        scan_id: Unique scan identifier
        user: Authenticated user information
        
    Returns:
        Success message
    """
    try:
        if scan_id not in active_scans:
            raise HTTPException(
                status_code=404,
                detail=f"Scan {scan_id} not found"
            )
        
        scan_result = active_scans[scan_id]
        
        # Verify user has access
        if scan_result.user_id != user["user_id"]:
            raise HTTPException(
                status_code=403,
                detail="Access denied to this scan"
            )
        
        # Cancel the scan
        cancelled = await scanner.cancel_scan(scan_id)
        
        if cancelled:
            active_scans[scan_id].status = "cancelled"
            active_scans[scan_id].completed_at = datetime.now(timezone.utc)
            
            logger.info(
                "Scan cancelled",
                scan_id=scan_id,
                user_id=user["user_id"]
            )
            
            return {"message": f"Scan {scan_id} cancelled successfully"}
        else:
            raise HTTPException(
                status_code=400,
                detail=f"Cannot cancel scan {scan_id} (may already be completed)"
            )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Error cancelling scan",
            scan_id=scan_id,
            error=str(e),
            error_type=type(e).__name__
        )
        raise HTTPException(
            status_code=500,
            detail=f"Failed to cancel scan: {str(e)}"
        )


@router.get("/scans", response_model=List[ScanResponse])
async def list_user_scans(
    user: Dict[str, Any] = Depends(verify_api_key),
    limit: int = 50,
    offset: int = 0
):
    """
    List scans for the authenticated user.
    
    Args:
        user: Authenticated user information
        limit: Maximum number of scans to return
        offset: Number of scans to skip
        
    Returns:
        List of user's scans
    """
    try:
        user_id = user["user_id"]
        
        # Filter scans by user
        user_scans = [
            scan for scan in active_scans.values()
            if scan.user_id == user_id
        ]
        
        # Sort by creation time (newest first)
        user_scans.sort(key=lambda x: x.created_at, reverse=True)
        
        # Apply pagination
        paginated_scans = user_scans[offset:offset + limit]
        
        logger.info(
            "User scans listed",
            user_id=user_id,
            total_scans=len(user_scans),
            returned_scans=len(paginated_scans)
        )
        
        return paginated_scans
        
    except Exception as e:
        logger.error(
            "Error listing user scans",
            user_id=user.get("user_id", "unknown"),
            error=str(e),
            error_type=type(e).__name__
        )
        raise HTTPException(
            status_code=500,
            detail=f"Failed to list scans: {str(e)}"
        )


@router.get("/health", response_model=HealthResponse)
async def health_check():
    """
    Health check endpoint for the scanner service.
    
    Returns:
        HealthResponse: Service health information
    """
    try:
        # Check scanner components
        components = {
            "scanner_core": True,  # Scanner is always available
            "detectors": True,     # Detectors are always available
            "file_system": True,   # File system access
        }
        
        # Determine overall status
        status = "healthy" if all(components.values()) else "unhealthy"
        
        health_response = HealthResponse(
            status=status,
            version="1.0.0",
            timestamp=datetime.now(timezone.utc),
            components=components
        )
        
        if status == "unhealthy":
            return JSONResponse(
                status_code=503,
                content=health_response.dict()
            )
        
        return health_response
        
    except Exception as e:
        logger.error(
            "Error in health check",
            error=str(e),
            error_type=type(e).__name__
        )
        
        return JSONResponse(
            status_code=503,
            content={
                "status": "unhealthy",
                "version": "1.0.0",
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "components": {"scanner_core": False},
                "error": str(e)
            }
        )


@router.get("/stats")
async def get_scanner_stats(
    user: Dict[str, Any] = Depends(verify_api_key)
):
    """
    Get scanner statistics.
    
    Args:
        user: Authenticated user information
        
    Returns:
        Scanner statistics
    """
    try:
        user_id = user["user_id"]
        
        # Calculate statistics
        user_scans = [scan for scan in active_scans.values() if scan.user_id == user_id]
        
        total_scans = len(user_scans)
        completed_scans = len([scan for scan in user_scans if scan.status == "completed"])
        failed_scans = len([scan for scan in user_scans if scan.status == "failed"])
        running_scans = len([scan for scan in user_scans if scan.status in ["initializing", "running"]])
        
        total_issues = sum(len(scan.issues) for scan in user_scans)
        
        # Issue statistics by type and severity
        issues_by_type = {}
        issues_by_severity = {}
        
        for scan in user_scans:
            for issue in scan.issues:
                issues_by_type[issue.type] = issues_by_type.get(issue.type, 0) + 1
                issues_by_severity[issue.severity] = issues_by_severity.get(issue.severity, 0) + 1
        
        stats = {
            "user_id": user_id,
            "total_scans": total_scans,
            "completed_scans": completed_scans,
            "failed_scans": failed_scans,
            "running_scans": running_scans,
            "total_issues_found": total_issues,
            "issues_by_type": issues_by_type,
            "issues_by_severity": issues_by_severity,
            "average_issues_per_scan": total_issues / max(completed_scans, 1)
        }
        
        return stats
        
    except Exception as e:
        logger.error(
            "Error retrieving scanner stats",
            user_id=user.get("user_id", "unknown"),
            error=str(e),
            error_type=type(e).__name__
        )
        raise HTTPException(
            status_code=500,
            detail=f"Failed to retrieve scanner statistics: {str(e)}"
        )