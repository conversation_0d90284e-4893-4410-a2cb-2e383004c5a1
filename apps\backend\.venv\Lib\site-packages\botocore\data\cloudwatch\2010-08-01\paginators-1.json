{"pagination": {"DescribeAlarmHistory": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxRecords", "result_key": "AlarmHistoryItems"}, "DescribeAlarms": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxRecords", "result_key": ["MetricAlarms", "CompositeAlarms"]}, "ListDashboards": {"input_token": "NextToken", "output_token": "NextToken", "result_key": "DashboardEntries"}, "ListMetrics": {"input_token": "NextToken", "output_token": "NextToken", "result_key": ["Metrics", "OwningAccounts"]}, "GetMetricData": {"input_token": "NextToken", "limit_key": "MaxDatapoints", "output_token": "NextToken", "result_key": ["MetricDataResults", "Messages"]}, "DescribeAnomalyDetectors": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "AnomalyDetectors"}}}