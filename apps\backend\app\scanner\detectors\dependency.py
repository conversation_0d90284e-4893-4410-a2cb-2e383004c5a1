"""
Dependency Detector for the Project Scanner system.

This module analyzes project dependencies for vulnerabilities, outdated packages,
license issues, and unused dependencies.
"""

import json
import re
import subprocess
import requests
from pathlib import Path
from typing import Any, Dict, List, Optional, Set, Tuple
from datetime import datetime, timedelta

from ..base_detector import BaseDetector
from ..models import (
    DetectorResult,
    Issue,
    IssueSeverity,
    IssueType,
    RemediationSuggestion
)


class DependencyDetector(BaseDetector):
    """
    Detects dependency-related issues including:
    - Outdated packages with known vulnerabilities
    - Unused dependencies
    - License compatibility issues
    - Insecure dependency sources
    - Dependency version conflicts
    """
    
    def __init__(self, config):
        super().__init__("Dependency", config)
        self.vulnerability_db = {}
        self.package_info_cache = {}
        self._load_vulnerability_patterns()
    
    def get_supported_file_types(self) -> Set[str]:
        """Get supported dependency file extensions."""
        return {
            # Python
            '.txt',  # requirements.txt
            '.in',   # requirements.in
            
            # Node.js
            '.json', # package.json
            '.lock', # package-lock.json, yarn.lock
            
            # Other formats
            '.toml', # pyproject.toml, Pipfile
            '.cfg',  # setup.cfg
            '.ini',  # tox.ini
            '.yaml', # conda.yml
            '.yml'   # conda.yml
        }
    
    def _load_vulnerability_patterns(self):
        """Load known vulnerable package patterns."""
        # This would normally be loaded from a vulnerability database
        self.known_vulnerabilities = {
            # Python packages with known issues
            'python': {
                'django': {
                    'vulnerable_versions': ['<3.2.16', '<4.0.8', '<4.1.3'],
                    'severity': IssueSeverity.HIGH,
                    'cve_ids': ['CVE-2022-34265', 'CVE-2022-36359'],
                    'description': 'Multiple security vulnerabilities in Django'
                },
                'flask': {
                    'vulnerable_versions': ['<2.2.2'],
                    'severity': IssueSeverity.MEDIUM,
                    'cve_ids': ['CVE-2022-XXXX'],
                    'description': 'Security vulnerabilities in Flask'
                },
                'pillow': {
                    'vulnerable_versions': ['<8.3.2'],
                    'severity': IssueSeverity.HIGH,
                    'cve_ids': ['CVE-2021-34552'],
                    'description': 'Buffer overflow in Pillow'
                },
                'pyyaml': {
                    'vulnerable_versions': ['<5.4'],
                    'severity': IssueSeverity.HIGH,
                    'cve_ids': ['CVE-2020-14343'],
                    'description': 'Arbitrary code execution in PyYAML'
                }
            },
            # Node.js packages with known issues
            'javascript': {
                'lodash': {
                    'vulnerable_versions': ['<4.17.19'],
                    'severity': IssueSeverity.HIGH,
                    'cve_ids': ['CVE-2020-8203'],
                    'description': 'Prototype pollution in lodash'
                },
                'axios': {
                    'vulnerable_versions': ['<0.21.2'],
                    'severity': IssueSeverity.MEDIUM,
                    'cve_ids': ['CVE-2021-3749'],
                    'description': 'Regular expression denial of service in axios'
                },
                'node-fetch': {
                    'vulnerable_versions': ['<2.6.7', '>=3.0.0 <3.2.4'],
                    'severity': IssueSeverity.HIGH,
                    'cve_ids': ['CVE-2022-0235'],
                    'description': 'Exposure of sensitive information in node-fetch'
                }
            }
        }
        
        # Packages with problematic licenses
        self.license_issues = {
            'gpl-licensed-package': IssueSeverity.MEDIUM,
            'copyleft-package': IssueSeverity.LOW,
            'commercial-only': IssueSeverity.HIGH
        }
    
    def detect(self, project_path: Path) -> DetectorResult:
        """Detect dependency issues."""
        self.start_detection()
        
        try:
            dependency_files = self._find_dependency_files(project_path)
            
            for dep_file in dependency_files:
                self._analyze_dependency_file(dep_file, project_path)
                self.files_processed += 1
            
            # Additional checks
            self._check_for_unused_dependencies(project_path)
            self._check_dependency_conflicts(project_path)
            
            return self.finish_detection()
            
        except Exception as e:
            return self.finish_detection(success=False, error_message=str(e))
    
    def _find_dependency_files(self, project_path: Path) -> List[Path]:
        """Find all dependency-related files in the project."""
        dependency_files = []
        
        # Common dependency files
        common_files = [
            'requirements.txt', 'requirements.in', 'dev-requirements.txt',
            'package.json', 'package-lock.json', 'yarn.lock',
            'pyproject.toml', 'Pipfile', 'Pipfile.lock',
            'setup.py', 'setup.cfg', 'environment.yml', 'conda.yml'
        ]
        
        for file_name in common_files:
            file_path = project_path / file_name
            if file_path.exists():
                dependency_files.append(file_path)
        
        # Search for additional requirements files
        for req_file in project_path.rglob('*requirements*.txt'):
            if req_file not in dependency_files:
                dependency_files.append(req_file)
        
        return dependency_files
    
    def _analyze_dependency_file(self, file_path: Path, project_path: Path) -> None:
        """Analyze a specific dependency file."""
        relative_path = str(file_path.relative_to(project_path))
        content = self.read_file_safely(file_path)
        
        if not content:
            return
        
        file_name = file_path.name.lower()
        
        if file_name in ['requirements.txt', 'requirements.in'] or 'requirements' in file_name:
            self._analyze_python_requirements(relative_path, content)
        elif file_name == 'package.json':
            self._analyze_package_json(relative_path, content)
        elif file_name == 'pyproject.toml':
            self._analyze_pyproject_toml(relative_path, content)
        elif file_name in ['pipfile', 'pipfile.lock']:
            self._analyze_pipfile(relative_path, content)
        elif file_name == 'setup.py':
            self._analyze_setup_py(relative_path, content)
    
    def _analyze_python_requirements(self, file_path: str, content: str) -> None:
        """Analyze Python requirements.txt file."""
        lines = content.splitlines()
        
        for line_num, line in enumerate(lines, 1):
            line = line.strip()
            
            # Skip comments and empty lines
            if not line or line.startswith('#'):
                continue
            
            # Skip -e (editable) and -r (recursive) options
            if line.startswith(('-e', '-r', '-f', '-i', '--')):
                continue
            
            # Parse package specification
            package_info = self._parse_python_package_spec(line)
            if package_info:
                self._check_python_package(package_info, file_path, line_num)
    
    def _parse_python_package_spec(self, spec: str) -> Optional[Dict[str, Any]]:
        """Parse a Python package specification."""
        # Handle different formats: package==1.0, package>=1.0,<2.0, package[extra]==1.0
        spec = spec.strip()
        
        # Remove comments
        if '#' in spec:
            spec = spec.split('#')[0].strip()
        
        # Extract package name and version constraints
        match = re.match(r'^([a-zA-Z0-9\-_.]+)(?:\[[^\]]+\])?([<>=!~\s].*)?\s*$', spec)
        if not match:
            return None
        
        package_name = match.group(1).lower()
        version_spec = match.group(2) or ''
        
        return {
            'name': package_name,
            'version_spec': version_spec.strip(),
            'raw_spec': spec
        }
    
    def _check_python_package(self, package_info: Dict[str, Any], file_path: str, line_num: int) -> None:
        """Check a Python package for issues."""
        package_name = package_info['name']
        version_spec = package_info['version_spec']
        
        # Check for known vulnerabilities
        if package_name in self.known_vulnerabilities['python']:
            vuln_info = self.known_vulnerabilities['python'][package_name]
            
            # Check if version spec might include vulnerable versions
            if self._is_potentially_vulnerable(version_spec, vuln_info['vulnerable_versions']):
                self.add_issue(self.create_issue(
                    IssueType.VULNERABLE_DEPENDENCY,
                    vuln_info['severity'],
                    f"Vulnerable package: {package_name}",
                    f"Package '{package_name}' has known security vulnerabilities. "
                    f"{vuln_info['description']}. CVE IDs: {', '.join(vuln_info['cve_ids'])}",
                    file_path,
                    line_number=line_num,
                    rule_id=f'vulnerable_python_package_{package_name}',
                    impact_score=80,
                    likelihood_score=70,
                    cwe_id='CWE-1035',
                    remediation=RemediationSuggestion(
                        title=f"Update {package_name} to latest version",
                        description=f"Update {package_name} to a version that fixes the security vulnerabilities.",
                        effort_estimate="15 minutes",
                        references=[f"https://pypi.org/project/{package_name}/"]
                    )
                ))
        
        # Check for outdated version constraints
        self._check_outdated_version_constraints(package_info, file_path, line_num)
        
        # Check for insecure version pinning
        self._check_insecure_version_pinning(package_info, file_path, line_num)
    
    def _analyze_package_json(self, file_path: str, content: str) -> None:
        """Analyze Node.js package.json file."""
        try:
            package_data = json.loads(content)
            
            # Check dependencies
            deps = package_data.get('dependencies', {})
            dev_deps = package_data.get('devDependencies', {})
            
            for dep_name, version in {**deps, **dev_deps}.items():
                self._check_javascript_package(dep_name, version, file_path)
            
            # Check for security issues in scripts
            scripts = package_data.get('scripts', {})
            for script_name, script_cmd in scripts.items():
                if any(dangerous in script_cmd for dangerous in ['rm -rf', 'curl |', 'wget |']):
                    self.add_issue(self.create_issue(
                        IssueType.SECURITY_VULNERABILITY,
                        IssueSeverity.HIGH,
                        f"Dangerous script: {script_name}",
                        f"Script '{script_name}' contains potentially dangerous commands: {script_cmd}",
                        file_path,
                        rule_id='dangerous_npm_script',
                        impact_score=75,
                        likelihood_score=60
                    ))
        
        except json.JSONDecodeError:
            self.add_issue(self.create_issue(
                IssueType.INVALID_CONFIG,
                IssueSeverity.MEDIUM,
                "Invalid package.json",
                "package.json file contains invalid JSON syntax.",
                file_path,
                rule_id='invalid_package_json',
                impact_score=40,
                likelihood_score=90
            ))
    
    def _check_javascript_package(self, package_name: str, version: str, file_path: str) -> None:
        """Check a JavaScript package for vulnerabilities."""
        if package_name in self.known_vulnerabilities['javascript']:
            vuln_info = self.known_vulnerabilities['javascript'][package_name]
            
            if self._is_potentially_vulnerable(version, vuln_info['vulnerable_versions']):
                self.add_issue(self.create_issue(
                    IssueType.VULNERABLE_DEPENDENCY,
                    vuln_info['severity'],
                    f"Vulnerable package: {package_name}",
                    f"Package '{package_name}' has known security vulnerabilities. "
                    f"{vuln_info['description']}. CVE IDs: {', '.join(vuln_info['cve_ids'])}",
                    file_path,
                    rule_id=f'vulnerable_js_package_{package_name}',
                    impact_score=80,
                    likelihood_score=70,
                    cwe_id='CWE-1035',
                    remediation=RemediationSuggestion(
                        title=f"Update {package_name} to latest version",
                        description=f"Update {package_name} to a version that fixes the security vulnerabilities.",
                        effort_estimate="15 minutes"
                    )
                ))
    
    def _analyze_pyproject_toml(self, file_path: str, content: str) -> None:
        """Analyze pyproject.toml file."""
        # Basic parsing without full TOML parser
        lines = content.splitlines()
        in_dependencies = False
        
        for line_num, line in enumerate(lines, 1):
            line = line.strip()
            
            if line == '[tool.poetry.dependencies]' or line == '[project.dependencies]':
                in_dependencies = True
                continue
            elif line.startswith('[') and in_dependencies:
                in_dependencies = False
                continue
            
            if in_dependencies and '=' in line:
                # Parse dependency line
                if '"' in line or "'" in line:
                    parts = line.split('=', 1)
                    if len(parts) == 2:
                        package_name = parts[0].strip()
                        version_info = parts[1].strip().strip('"\'')
                        
                        package_info = {
                            'name': package_name.lower(),
                            'version_spec': version_info,
                            'raw_spec': line
                        }
                        self._check_python_package(package_info, file_path, line_num)
    
    def _analyze_pipfile(self, file_path: str, content: str) -> None:
        """Analyze Pipfile."""
        # Basic TOML-like parsing for Pipfile
        lines = content.splitlines()
        in_packages = False
        in_dev_packages = False
        
        for line_num, line in enumerate(lines, 1):
            line = line.strip()
            
            if line == '[packages]':
                in_packages = True
                in_dev_packages = False
                continue
            elif line == '[dev-packages]':
                in_dev_packages = True
                in_packages = False
                continue
            elif line.startswith('['):
                in_packages = in_dev_packages = False
                continue
            
            if (in_packages or in_dev_packages) and '=' in line:
                parts = line.split('=', 1)
                if len(parts) == 2:
                    package_name = parts[0].strip()
                    version_info = parts[1].strip().strip('"\'')
                    
                    package_info = {
                        'name': package_name.lower(),
                        'version_spec': version_info,
                        'raw_spec': line
                    }
                    self._check_python_package(package_info, file_path, line_num)
    
    def _analyze_setup_py(self, file_path: str, content: str) -> None:
        """Analyze setup.py file for dependency issues."""
        # Look for install_requires and similar
        install_requires_match = re.search(r'install_requires\s*=\s*\[(.*?)\]', content, re.DOTALL)
        if install_requires_match:
            requirements_text = install_requires_match.group(1)
            
            # Extract individual requirements
            for req_match in re.finditer(r'["\']([^"\']+)["\']', requirements_text):
                req_text = req_match.group(1)
                package_info = self._parse_python_package_spec(req_text)
                if package_info:
                    # Find line number (approximate)
                    line_num = content[:req_match.start()].count('\n') + 1
                    self._check_python_package(package_info, file_path, line_num)
    
    def _is_potentially_vulnerable(self, version_spec: str, vulnerable_versions: List[str]) -> bool:
        """Check if a version specification might include vulnerable versions."""
        # This is a simplified check - in production, you'd use proper version comparison
        if not version_spec:
            return True  # No version specified means potentially vulnerable
        
        # If exact version is specified (==), check if it's in vulnerable list
        if '==' in version_spec:
            exact_version = version_spec.replace('==', '').strip()
            return any(self._version_matches_constraint(exact_version, vuln) for vuln in vulnerable_versions)
        
        # For other constraints, assume potentially vulnerable for now
        # In production, implement proper semantic versioning comparison
        return True
    
    def _version_matches_constraint(self, version: str, constraint: str) -> bool:
        """Check if a version matches a constraint (simplified)."""
        # This is a very basic implementation - use packaging.version in production
        if constraint.startswith('<'):
            constraint_version = constraint[1:].strip()
            return version < constraint_version
        elif constraint.startswith('>='):
            constraint_version = constraint[2:].strip()
            return version >= constraint_version
        elif constraint.startswith('>'):
            constraint_version = constraint[1:].strip()
            return version > constraint_version
        
        return False
    
    def _check_outdated_version_constraints(self, package_info: Dict[str, Any], file_path: str, line_num: int) -> None:
        """Check for outdated version constraints."""
        version_spec = package_info['version_spec']
        package_name = package_info['name']
        
        # Check for very old version constraints
        if '==' in version_spec:
            version = version_spec.replace('==', '').strip()
            # Look for obviously old versions (this is simplified)
            if any(old_pattern in version for old_pattern in ['0.', '1.0', '1.1', '1.2']):
                if package_name in ['django', 'flask', 'requests', 'numpy']:  # Common packages that update frequently
                    self.add_issue(self.create_issue(
                        IssueType.OUTDATED_DEPENDENCY,
                        IssueSeverity.MEDIUM,
                        f"Potentially outdated package: {package_name}",
                        f"Package '{package_name}' is pinned to version {version}, which may be outdated.",
                        file_path,
                        line_number=line_num,
                        rule_id=f'outdated_package_{package_name}',
                        impact_score=40,
                        likelihood_score=70,
                        remediation=RemediationSuggestion(
                            title=f"Check for newer version of {package_name}",
                            description=f"Verify if a newer, more secure version of {package_name} is available.",
                            effort_estimate="10 minutes"
                        )
                    ))
    
    def _check_insecure_version_pinning(self, package_info: Dict[str, Any], file_path: str, line_num: int) -> None:
        """Check for insecure version pinning practices."""
        version_spec = package_info['version_spec']
        
        # Check for wildcards or very loose constraints
        if '*' in version_spec or not version_spec:
            self.add_issue(self.create_issue(
                IssueType.INSECURE_DEPENDENCY,
                IssueSeverity.LOW,
                f"Loose version constraint: {package_info['name']}",
                f"Package '{package_info['name']}' has loose or no version constraints, "
                "which could lead to unexpected version upgrades.",
                file_path,
                line_number=line_num,
                rule_id='loose_version_constraint',
                impact_score=20,
                likelihood_score=50,
                remediation=RemediationSuggestion(
                    title="Pin to specific version ranges",
                    description="Use specific version constraints to ensure reproducible builds.",
                    code_example=f"# Instead of: {package_info['name']}\n# Use: {package_info['name']}>=1.0,<2.0",
                    effort_estimate="5 minutes"
                )
            ))
    
    def _check_for_unused_dependencies(self, project_path: Path) -> None:
        """Check for potentially unused dependencies (simplified check)."""
        # This would require more sophisticated analysis in production
        # For now, we'll just flag some common patterns
        
        dependency_files = self._find_dependency_files(project_path)
        for dep_file in dependency_files:
            if dep_file.name == 'requirements.txt':
                content = self.read_file_safely(dep_file)
                if content:
                    self._check_python_unused_deps(content, str(dep_file.relative_to(project_path)), project_path)
    
    def _check_python_unused_deps(self, requirements_content: str, file_path: str, project_path: Path) -> None:
        """Check for unused Python dependencies (basic check)."""
        lines = requirements_content.splitlines()
        
        for line_num, line in enumerate(lines, 1):
            line = line.strip()
            if not line or line.startswith('#'):
                continue
            
            package_info = self._parse_python_package_spec(line)
            if not package_info:
                continue
            
            package_name = package_info['name']
            
            # Check if package is imported anywhere in the codebase
            if not self._is_package_used(package_name, project_path):
                self.add_issue(self.create_issue(
                    IssueType.UNUSED_DEPENDENCY,
                    IssueSeverity.LOW,
                    f"Potentially unused dependency: {package_name}",
                    f"Package '{package_name}' is listed as a dependency but may not be used in the codebase.",
                    file_path,
                    line_number=line_num,
                    rule_id=f'unused_dependency_{package_name}',
                    impact_score=15,
                    likelihood_score=60,
                    remediation=RemediationSuggestion(
                        title="Remove unused dependency",
                        description="If this package is truly unused, consider removing it to reduce security surface.",
                        effort_estimate="5 minutes"
                    )
                ))
    
    def _is_package_used(self, package_name: str, project_path: Path) -> bool:
        """Check if a package is used in the codebase (simplified)."""
        # This is a very basic check - in production, use proper AST analysis
        python_files = list(project_path.rglob('*.py'))
        
        for py_file in python_files[:20]:  # Limit to avoid performance issues
            try:
                content = self.read_file_safely(py_file)
                if content:
                    # Look for import statements
                    if f'import {package_name}' in content or f'from {package_name}' in content:
                        return True
                    
                    # Check for common package aliases
                    common_aliases = {
                        'pillow': 'PIL',
                        'beautifulsoup4': 'bs4',
                        'pyyaml': 'yaml'
                    }
                    
                    if package_name in common_aliases:
                        alias = common_aliases[package_name]
                        if f'import {alias}' in content or f'from {alias}' in content:
                            return True
            except:
                continue
        
        return False
    
    def _check_dependency_conflicts(self, project_path: Path) -> None:
        """Check for potential dependency conflicts."""
        # This would require sophisticated dependency resolution in production
        # For now, just check for common conflict patterns
        
        dependency_files = self._find_dependency_files(project_path)
        all_dependencies = {}
        
        for dep_file in dependency_files:
            if dep_file.name in ['requirements.txt', 'requirements.in']:
                content = self.read_file_safely(dep_file)
                if content:
                    deps = self._extract_python_dependencies(content)
                    for dep_name, version_spec in deps.items():
                        if dep_name in all_dependencies:
                            # Potential conflict
                            existing_spec = all_dependencies[dep_name]
                            if existing_spec != version_spec:
                                self.add_issue(self.create_issue(
                                    IssueType.ARCHITECTURE_VIOLATION,
                                    IssueSeverity.MEDIUM,
                                    f"Dependency conflict: {dep_name}",
                                    f"Package '{dep_name}' has conflicting version specifications: "
                                    f"'{existing_spec}' and '{version_spec}'",
                                    str(dep_file.relative_to(project_path)),
                                    rule_id=f'dependency_conflict_{dep_name}',
                                    impact_score=60,
                                    likelihood_score=80
                                ))
                        else:
                            all_dependencies[dep_name] = version_spec
    
    def _extract_python_dependencies(self, content: str) -> Dict[str, str]:
        """Extract Python dependencies from requirements content."""
        dependencies = {}
        
        for line in content.splitlines():
            line = line.strip()
            if not line or line.startswith('#'):
                continue
            
            package_info = self._parse_python_package_spec(line)
            if package_info:
                dependencies[package_info['name']] = package_info['version_spec']
        
        return dependencies