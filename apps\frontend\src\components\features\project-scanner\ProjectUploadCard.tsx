'use client';

import { Card } from '@/components/common/Card';
import { Button } from '@/components/common/Button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { 
  Upload, 
  Github, 
  FolderOpen, 
  X, 
  FileText, 
  Settings,
  Info
} from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { useState, useRef, useCallback } from 'react';
import type { ProjectScanRequest } from '@/lib/types';

interface ProjectUploadCardProps {
  onStartScan?: (request: Omit<ProjectScanRequest, 'id' | 'createdAt' | 'status'>) => Promise<void>;
  loading?: boolean;
  className?: string;
}

interface UploadedFile {
  name: string;
  size: number;
  type: string;
}

export function ProjectUploadCard({ onStartScan, loading, className }: ProjectUploadCardProps) {
  const [projectName, setProjectName] = useState('');
  const [scanType, setScanType] = useState<ProjectScanRequest['scanType']>('comprehensive');
  const [repositoryUrl, setRepositoryUrl] = useState('');
  const [uploadMode, setUploadMode] = useState<'upload' | 'github' | 'local'>('upload');
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);
  const [isDragging, setIsDragging] = useState(false);
  const [showAdvanced, setShowAdvanced] = useState(false);
  
  // Advanced options
  const [includeTests, setIncludeTests] = useState(true);
  const [includeDependencies, setIncludeDependencies] = useState(true);
  const [maxDepth, setMaxDepth] = useState(10);
  const [excludePatterns, setExcludePatterns] = useState<string[]>(['node_modules', '.git', 'dist', 'build']);
  
  const fileInputRef = useRef<HTMLInputElement>(null);
  
  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(true);
  }, []);
  
  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
  }, []);
  
  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
    
    const files = Array.from(e.dataTransfer.files);
    const fileInfo = files.map(file => ({
      name: file.name,
      size: file.size,
      type: file.type || 'application/octet-stream'
    }));
    
    setUploadedFiles(prev => [...prev, ...fileInfo]);
    
    // Auto-set project name if not set
    if (!projectName && files.length > 0) {
      const firstFile = files[0];
      const name = firstFile.name.replace(/\.(zip|tar\.gz|tgz)$/, '');
      setProjectName(name);
    }
  }, [projectName]);
  
  const handleFileSelect = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    const fileInfo = files.map(file => ({
      name: file.name,
      size: file.size,
      type: file.type || 'application/octet-stream'
    }));
    
    setUploadedFiles(prev => [...prev, ...fileInfo]);
    
    // Auto-set project name if not set
    if (!projectName && files.length > 0) {
      const firstFile = files[0];
      const name = firstFile.name.replace(/\.(zip|tar\.gz|tgz)$/, '');
      setProjectName(name);
    }
  }, [projectName]);
  
  const handleRemoveFile = (index: number) => {
    setUploadedFiles(prev => prev.filter((_, i) => i !== index));
  };
  
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };
  
  const handleStartScan = async () => {
    if (!projectName.trim()) return;
    
    const request: Omit<ProjectScanRequest, 'id' | 'createdAt' | 'status'> = {
      projectName: projectName.trim(),
      scanType,
      repositoryUrl: uploadMode === 'github' ? repositoryUrl : undefined,
      options: {
        includeTests,
        includeDependencies,
        maxDepth,
        excludePatterns
      }
    };
    
    if (onStartScan) {
      await onStartScan(request);
    }
    
    // Reset form
    setProjectName('');
    setRepositoryUrl('');
    setUploadedFiles([]);
  };
  
  const isFormValid = () => {
    if (!projectName.trim()) return false;
    
    if (uploadMode === 'upload' && uploadedFiles.length === 0) return false;
    if (uploadMode === 'github' && !repositoryUrl.trim()) return false;
    
    return true;
  };
  
  return (
    <Card className={`p-6 ${className}`}>
      <div className="space-y-6">
        <div>
          <h3 className="text-lg font-semibold mb-4">Start New Scan</h3>
          
          {/* Upload Mode Selection */}
          <div className="flex gap-2 mb-4">
            <Button
              variant={uploadMode === 'upload' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setUploadMode('upload')}
            >
              <Upload className="h-4 w-4 mr-2" />
              Upload Files
            </Button>
            <Button
              variant={uploadMode === 'github' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setUploadMode('github')}
            >
              <Github className="h-4 w-4 mr-2" />
              GitHub Repository
            </Button>
            <Button
              variant={uploadMode === 'local' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setUploadMode('local')}
            >
              <FolderOpen className="h-4 w-4 mr-2" />
              Local Folder
            </Button>
          </div>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Project Configuration */}
          <div className="space-y-4">
            <div>
              <Label htmlFor="projectName">Project Name *</Label>
              <Input
                id="projectName"
                placeholder="Enter project name"
                value={projectName}
                onChange={(e) => setProjectName(e.target.value)}
              />
            </div>
            
            <div>
              <Label htmlFor="scanType">Scan Type</Label>
              <Select value={scanType} onValueChange={(value) => setScanType(value as ProjectScanRequest['scanType'])}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="security">Security Scan</SelectItem>
                  <SelectItem value="quality">Code Quality</SelectItem>
                  <SelectItem value="performance">Performance Analysis</SelectItem>
                  <SelectItem value="dependency">Dependency Audit</SelectItem>
                  <SelectItem value="comprehensive">Comprehensive Scan</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            {/* Advanced Options Toggle */}
            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowAdvanced(!showAdvanced)}
              >
                <Settings className="h-4 w-4 mr-2" />
                Advanced Options
              </Button>
              <Badge variant={showAdvanced ? 'default' : 'outline'}>
                {showAdvanced ? 'Expanded' : 'Collapsed'}
              </Badge>
            </div>
          </div>
          
          {/* File Upload Area */}
          <div className="space-y-4">
            {uploadMode === 'upload' && (
              <div>
                <Label>Project Files</Label>
                <div
                  className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
                    isDragging 
                      ? 'border-primary bg-primary/5' 
                      : 'border-gray-300 dark:border-gray-600'
                  }`}
                  onDragOver={handleDragOver}
                  onDragLeave={handleDragLeave}
                  onDrop={handleDrop}
                >
                  <Upload className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    Drop files here or{' '}
                    <button
                      type="button"
                      className="text-primary hover:underline"
                      onClick={() => fileInputRef.current?.click()}
                    >
                      click to browse
                    </button>
                  </p>
                  <p className="text-xs text-gray-500 mt-2">
                    Supports: .zip, .tar.gz, or individual files
                  </p>
                  
                  <input
                    ref={fileInputRef}
                    type="file"
                    multiple
                    accept=".zip,.tar.gz,.tgz,*"
                    onChange={handleFileSelect}
                    className="hidden"
                  />
                </div>
              </div>
            )}
            
            {uploadMode === 'github' && (
              <div>
                <Label htmlFor="repositoryUrl">Repository URL *</Label>
                <div className="flex gap-2">
                  <Input
                    id="repositoryUrl"
                    placeholder="https://github.com/user/repo"
                    value={repositoryUrl}
                    onChange={(e) => setRepositoryUrl(e.target.value)}
                  />
                  <Button variant="outline" size="icon">
                    <Github className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            )}
            
            {uploadMode === 'local' && (
              <div className="text-center p-8 border-2 border-dashed border-gray-300 rounded-lg">
                <FolderOpen className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-sm text-gray-600">
                  Local folder scanning available in desktop version
                </p>
              </div>
            )}
          </div>
        </div>
        
        {/* Uploaded Files List */}
        <AnimatePresence>
          {uploadedFiles.length > 0 && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              className="space-y-2"
            >
              <Label>Uploaded Files ({uploadedFiles.length})</Label>
              <div className="space-y-2 max-h-32 overflow-y-auto">
                {uploadedFiles.map((file, index) => (
                  <div
                    key={index}
                    className="flex items-center justify-between p-2 bg-gray-50 dark:bg-gray-800 rounded"
                  >
                    <div className="flex items-center gap-2">
                      <FileText className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm truncate">{file.name}</span>
                      <span className="text-xs text-muted-foreground">
                        {formatFileSize(file.size)}
                      </span>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleRemoveFile(index)}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
              </div>
            </motion.div>
          )}
        </AnimatePresence>
        
        {/* Advanced Options */}
        <AnimatePresence>
          {showAdvanced && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              className="space-y-4 border-t pt-4"
            >
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-3">
                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="includeTests"
                      checked={includeTests}
                      onChange={(e) => setIncludeTests(e.target.checked)}
                      className="rounded"
                    />
                    <Label htmlFor="includeTests">Include test files</Label>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="includeDependencies"
                      checked={includeDependencies}
                      onChange={(e) => setIncludeDependencies(e.target.checked)}
                      className="rounded"
                    />
                    <Label htmlFor="includeDependencies">Analyze dependencies</Label>
                  </div>
                  
                  <div>
                    <Label htmlFor="maxDepth">Max directory depth</Label>
                    <Input
                      id="maxDepth"
                      type="number"
                      min="1"
                      max="50"
                      value={maxDepth}
                      onChange={(e) => setMaxDepth(parseInt(e.target.value) || 10)}
                    />
                  </div>
                </div>
                
                <div>
                  <Label>Exclude patterns</Label>
                  <div className="space-y-2">
                    <div className="flex flex-wrap gap-1">
                      {excludePatterns.map((pattern, index) => (
                        <Badge key={index} variant="secondary" className="text-xs">
                          {pattern}
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-auto p-0 ml-1"
                            onClick={() => {
                              setExcludePatterns(prev => prev.filter((_, i) => i !== index));
                            }}
                          >
                            <X className="h-3 w-3" />
                          </Button>
                        </Badge>
                      ))}
                    </div>
                    <Input
                      placeholder="Add exclude pattern"
                      onKeyDown={(e) => {
                        if (e.key === 'Enter') {
                          const value = e.currentTarget.value.trim();
                          if (value && !excludePatterns.includes(value)) {
                            setExcludePatterns(prev => [...prev, value]);
                            e.currentTarget.value = '';
                          }
                        }
                      }}
                    />
                  </div>
                </div>
              </div>
              
              <div className="flex items-start gap-2 p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded">
                <Info className="h-4 w-4 text-blue-600 mt-0.5 flex-shrink-0" />
                <div className="text-sm text-blue-800 dark:text-blue-200">
                  <p className="font-medium mb-1">Advanced Options</p>
                  <ul className="text-xs space-y-1">
                    <li>• Test files: Include or exclude test directories and files</li>
                    <li>• Dependencies: Analyze package.json, requirements.txt, etc.</li>
                    <li>• Directory depth: Limit how deep the scanner goes</li>
                    <li>• Exclude patterns: Skip specific files or directories</li>
                  </ul>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
        
        {/* Action Buttons */}
        <div className="flex justify-end gap-3">
          <Button variant="outline">
            Save as Template
          </Button>
          <Button
            onClick={handleStartScan}
            disabled={!isFormValid() || loading}
            gradient
          >
            {loading ? (
              <>
                <motion.div
                  className="w-4 h-4 border-2 border-white border-t-transparent rounded-full mr-2"
                  animate={{ rotate: 360 }}
                  transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}
                />
                Starting Scan...
              </>
            ) : (
              <>
                <Upload className="h-4 w-4 mr-2" />
                Start Scan
              </>
            )}
          </Button>
        </div>
      </div>
    </Card>
  );
}