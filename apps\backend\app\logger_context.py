"""Context manager for structured logging with correlation IDs."""
from contextvars import Context<PERSON><PERSON>
from typing import Optional
import uuid

# Context variables for request correlation
correlation_id_context: ContextVar[Optional[str]] = ContextVar("correlation_id", default=None)
request_id_context: ContextVar[Optional[str]] = ContextVar("request_id", default=None)
user_id_context: ContextVar[Optional[str]] = ContextVar("user_id", default=None)


class LoggerContext:
    """Context manager for structured logging with correlation IDs."""

    def __init__(self, correlation_id: Optional[str] = None, request_id: Optional[str] = None, user_id: Optional[str] = None):
        self.correlation_id = correlation_id or str(uuid.uuid4())
        self.request_id = request_id or str(uuid.uuid4())
        self.user_id = user_id

        # Store previous values
        self._prev_correlation_id = None
        self._prev_request_id = None
        self._prev_user_id = None

    def __enter__(self):
        # Store previous values
        self._prev_correlation_id = correlation_id_context.get()
        self._prev_request_id = request_id_context.get()
        self._prev_user_id = user_id_context.get()

        # Set new values
        correlation_id_context.set(self.correlation_id)
        request_id_context.set(self.request_id)
        if self.user_id:
            user_id_context.set(self.user_id)

        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        # Restore previous values
        correlation_id_context.set(self._prev_correlation_id)
        request_id_context.set(self._prev_request_id)
        user_id_context.set(self._prev_user_id)
