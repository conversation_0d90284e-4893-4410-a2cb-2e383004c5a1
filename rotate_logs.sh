#!/bin/bash
# Log rotation script for LONI platform development tools

# Set variables
PROJECT_ROOT="/mnt/e/Projects/lonors/loni"
LOGS_DIR="$PROJECT_ROOT/apps/data/logs"

# Function to log messages
log_message() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1"
}

# Function to rotate logs
rotate_logs() {
    log_message "Starting log rotation"
    
    # Create logs directory if it doesn't exist
    mkdir -p "$LOGS_DIR"
    
    # Find all log files older than 7 days and compress them
    find "$LOGS_DIR" -name "*.log" -type f -mtime +7 -exec gzip {} \; 2>/dev/null
    
    # Delete compressed logs older than 30 days
    find "$LOGS_DIR" -name "*.log.gz" -type f -mtime +30 -delete 2>/dev/null
    
    log_message "Log rotation completed"
}

# Main execution
main() {
    rotate_logs
}

# Run main function
main