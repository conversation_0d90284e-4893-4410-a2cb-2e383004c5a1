"""
Backend Module System - n8n-like workflow node architecture.

This module provides a modular, plugin-based architecture where each business
logic component is a self-contained "node" with clear interfaces, configuration,
and dependencies.
"""

from typing import Dict, Any, Protocol, TypeVar, Generic
from abc import ABC, abstractmethod
from dataclasses import dataclass
from enum import Enum


class ModuleStatus(Enum):
    """Status of a module/node."""
    INITIALIZING = "initializing"
    READY = "ready"
    RUNNING = "running"
    ERROR = "error"
    STOPPED = "stopped"


class ModuleType(Enum):
    """Types of modules/nodes available in the system."""
    MODEL_MANAGER = "model_manager"
    TRANSCRIPTION = "transcription"
    SCANNER = "scanner"
    API_CLIENT = "api_client"
    CACHE = "cache"
    STORAGE = "storage"
    NOTIFICATION = "notification"
    METRICS = "metrics"


@dataclass
class ModuleConfig:
    """Configuration for a module/node."""
    name: str
    type: ModuleType
    enabled: bool = True
    priority: int = 100
    dependencies: list[str] = None
    settings: Dict[str, Any] = None

    def __post_init__(self):
        if self.dependencies is None:
            self.dependencies = []
        if self.settings is None:
            self.settings = {}


@dataclass
class ModuleInput:
    """Input data for a module/node."""
    data: Dict[str, Any]
    metadata: Dict[str, Any] = None
    context: Dict[str, Any] = None

    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}
        if self.context is None:
            self.context = {}


@dataclass
class ModuleOutput:
    """Output data from a module/node."""
    success: bool
    data: Dict[str, Any] = None
    error: str = None
    metadata: Dict[str, Any] = None

    def __post_init__(self):
        if self.data is None:
            self.data = {}
        if self.metadata is None:
            self.metadata = {}


class ModuleInterface(Protocol):
    """Protocol defining the interface for all modules/nodes."""

    @property
    def name(self) -> str:
        """Get module name."""
        ...

    @property
    def type(self) -> ModuleType:
        """Get module type."""
        ...

    @property
    def status(self) -> ModuleStatus:
        """Get current module status."""
        ...

    async def initialize(self, config: ModuleConfig) -> bool:
        """Initialize the module with configuration."""
        ...

    async def execute(self, input_data: ModuleInput) -> ModuleOutput:
        """Execute the module's main functionality."""
        ...

    async def validate(self, input_data: ModuleInput) -> bool:
        """Validate input data for the module."""
        ...

    async def cleanup(self) -> None:
        """Cleanup module resources."""
        ...

    def get_dependencies(self) -> list[str]:
        """Get list of module dependencies."""
        ...


T = TypeVar('T')


class BaseModule(ABC, Generic[T]):
    """Base class for all modules/nodes providing common functionality."""

    def __init__(self, name: str, module_type: ModuleType):
        self._name = name
        self._type = module_type
        self._status = ModuleStatus.INITIALIZING
        self._config: ModuleConfig | None = None
        self._dependencies: Dict[str, 'BaseModule'] = {}

    @property
    def name(self) -> str:
        return self._name

    @property
    def type(self) -> ModuleType:
        return self._type

    @property
    def status(self) -> ModuleStatus:
        return self._status

    @property
    def config(self) -> ModuleConfig | None:
        return self._config

    async def initialize(self, config: ModuleConfig) -> bool:
        """Initialize the module with configuration."""
        try:
            self._config = config
            self._status = ModuleStatus.READY
            return True
        except Exception as e:
            self._status = ModuleStatus.ERROR
            return False

    async def validate(self, input_data: ModuleInput) -> bool:
        """Validate input data. Override in subclasses for specific validation."""
        return True

    def get_dependencies(self) -> list[str]:
        """Get list of module dependencies."""
        return self._config.dependencies if self._config else []

    def set_dependency(self, name: str, module: 'BaseModule') -> None:
        """Set a dependency module."""
        self._dependencies[name] = module

    def get_dependency(self, name: str) -> 'BaseModule':
        """Get a dependency module."""
        if name not in self._dependencies:
            raise ValueError(f"Dependency '{name}' not found")
        return self._dependencies[name]

    async def cleanup(self) -> None:
        """Cleanup module resources."""
        self._status = ModuleStatus.STOPPED
        self._dependencies.clear()

    @abstractmethod
    async def execute(self, input_data: ModuleInput) -> ModuleOutput:
        """Execute the module's main functionality."""
        pass


class ModuleRegistry:
    """Registry for managing all modules/nodes in the system."""

    def __init__(self):
        self._modules: Dict[str, BaseModule] = {}
        self._configs: Dict[str, ModuleConfig] = {}

    def register(self, module: BaseModule) -> None:
        """Register a module."""
        self._modules[module.name] = module

    def unregister(self, name: str) -> None:
        """Unregister a module."""
        if name in self._modules:
            del self._modules[name]

    def get(self, name: str) -> BaseModule | None:
        """Get a module by name."""
        return self._modules.get(name)

    def list_by_type(self, module_type: ModuleType) -> list[BaseModule]:
        """List all modules of a specific type."""
        return [m for m in self._modules.values() if m.type == module_type]

    def list_all(self) -> list[BaseModule]:
        """List all registered modules."""
        return list(self._modules.values())

    def set_config(self, name: str, config: ModuleConfig) -> None:
        """Set configuration for a module."""
        self._configs[name] = config

    def get_config(self, name: str) -> ModuleConfig | None:
        """Get configuration for a module."""
        return self._configs.get(name)

    async def initialize_all(self) -> Dict[str, bool]:
        """Initialize all registered modules."""
        results = {}
        # Sort by priority for proper initialization order
        sorted_modules = sorted(
            self._modules.items(),
            key=lambda x: self._configs.get(x[0], ModuleConfig(x[0], ModuleType.METRICS)).priority
        )

        for name, module in sorted_modules:
            config = self._configs.get(name)
            if config:
                results[name] = await module.initialize(config)
            else:
                results[name] = False

        return results

    async def cleanup_all(self) -> None:
        """Cleanup all registered modules."""
        for module in self._modules.values():
            await module.cleanup()


# Global module registry instance
registry = ModuleRegistry()


class ModuleFactory:
    """Factory for creating module instances."""

    @staticmethod
    def create_module(module_type: ModuleType, name: str, **kwargs) -> BaseModule:
        """Create a module instance based on type."""
        from . import model_manager, transcription, scanner

        if module_type == ModuleType.MODEL_MANAGER:
            return model_manager.OllamaModelModule(name, **kwargs)
        elif module_type == ModuleType.TRANSCRIPTION:
            return transcription.WhisperTranscriptionModule(name, **kwargs)
        elif module_type == ModuleType.SCANNER:
            return scanner.ProjectScannerModule(name, **kwargs)
        else:
            raise ValueError(f"Unknown module type: {module_type}")

    @staticmethod
    def create_config(name: str, module_type: ModuleType, **settings) -> ModuleConfig:
        """Create a module configuration."""
        return ModuleConfig(
            name=name,
            type=module_type,
            settings=settings
        )
