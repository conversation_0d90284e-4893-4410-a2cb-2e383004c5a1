#!/usr/bin/env python3
"""
Pre-commit hook for TDD workflow enforcement
Ensures test-first development practices
"""

import os
import sys
import subprocess
from pathlib import Path


def get_staged_files():
    """Get list of staged files"""
    try:
        result = subprocess.run([
            'git', 'diff', '--cached', '--name-only'
        ], capture_output=True, text=True, check=True)
        
        return [f.strip() for f in result.stdout.split('\n') if f.strip()]
    except subprocess.CalledProcessError:
        return []


def is_implementation_file(file_path):
    """Check if file is an implementation file (not a test)"""
    if not os.path.exists(file_path):
        return False
        
    # Skip non-code files
    if not file_path.endswith(('.py', '.ts', '.tsx', '.js', '.jsx')):
        return False
        
    # Skip test files
    test_markers = ['test_', '.test.', '.spec.', '_test.py', '__tests__/', '/tests/']
    if any(marker in file_path for marker in test_markers):
        return False
        
    # Skip config files, migrations, etc.
    skip_patterns = [
        'conftest.py', 'settings.py', 'config.py', 'main.py',
        'migrations/', 'alembic/', 'node_modules/',
        '.config.', 'vite.config', 'next.config'
    ]
    if any(pattern in file_path for pattern in skip_patterns):
        return False
        
    return True


def run_tdd_validation(file_path):
    """Run TDD validation for a specific file"""
    script_dir = Path(__file__).parent.parent
    tdd_script = script_dir / 'tdd-workflow.py'
    
    try:
        result = subprocess.run([
            sys.executable, str(tdd_script),
            '--enforce-test-first',
            '--file', file_path
        ], capture_output=True, text=True)
        
        return result.returncode == 0, result.stdout + result.stderr
        
    except Exception as e:
        return False, f"TDD validation failed: {str(e)}"


def run_quick_tests():
    """Run quick test suite to ensure existing tests still pass"""
    print("🧪 Running quick test validation...")
    
    # Backend quick tests
    backend_path = Path.cwd() / 'apps' / 'backend'
    if backend_path.exists():
        try:
            os.chdir(backend_path)
            result = subprocess.run([
                'uv', 'run', 'python', '-m', 'pytest',
                'tests/', '-v', '--tb=short', '-x'  # Stop on first failure
            ], capture_output=True, text=True, timeout=60)
            
            if result.returncode != 0:
                print("❌ Backend tests failed:")
                print(result.stdout[-500:])
                return False
        except subprocess.TimeoutExpired:
            print("❌ Backend tests timed out")
            return False
        except Exception as e:
            print(f"❌ Backend test execution failed: {e}")
            return False
    
    # Frontend quick tests
    frontend_path = Path.cwd() / 'apps' / 'frontend'  
    if frontend_path.exists():
        try:
            os.chdir(frontend_path)
            # Check if we have test files first
            test_files = list(frontend_path.rglob('*.test.*')) + list(frontend_path.rglob('*.spec.*'))
            if test_files:
                result = subprocess.run([
                    'bun', 'test'
                ], capture_output=True, text=True, timeout=60)
                
                if result.returncode != 0:
                    print("❌ Frontend tests failed:")
                    print(result.stdout[-500:])
                    return False
        except subprocess.TimeoutExpired:
            print("❌ Frontend tests timed out")
            return False
        except Exception as e:
            print(f"❌ Frontend test execution failed: {e}")
            return False
    
    print("✅ Quick tests passed")
    return True


def main():
    """Main pre-commit hook execution"""
    print("🔍 Running TDD pre-commit validation...")
    
    # Get staged files
    staged_files = get_staged_files()
    
    if not staged_files:
        print("📝 No staged files found")
        return 0
    
    # Check implementation files for test-first compliance
    implementation_files = [f for f in staged_files if is_implementation_file(f)]
    
    if not implementation_files:
        print("📝 No implementation files found in staged changes")
        return 0
    
    print(f"🔍 Checking {len(implementation_files)} implementation files...")
    
    violations = []
    
    for file_path in implementation_files:
        print(f"   Checking {file_path}...")
        
        valid, message = run_tdd_validation(file_path)
        if not valid:
            violations.append((file_path, message))
    
    if violations:
        print("\n❌ TDD Violations Found:")
        for file_path, message in violations:
            print(f"\n🚨 {file_path}:")
            print(f"   {message}")
        
        print("\n💡 To fix these violations:")
        print("   1. Write tests first before implementing functionality")
        print("   2. Ensure test files exist for all implementation files")
        print("   3. Tests should cover the functionality being implemented")
        
        return 1
    
    # Run quick tests to ensure nothing is broken
    if not run_quick_tests():
        print("\n❌ Existing tests are failing - please fix before committing")
        return 1
    
    print("✅ All TDD validations passed!")
    return 0


if __name__ == "__main__":
    sys.exit(main())