"""
Scanner Module - Modular project scanning and analysis.

This module provides self-contained project scanning functionality
that can be easily composed with other modules in a workflow.
"""

from typing import Dict, List, Any, Optional
import uuid
from datetime import datetime
from pathlib import Path
import concurrent.futures
import threading
import time

from . import (
    BaseModule,
    ModuleInput,
    ModuleOutput,
    ModuleType,
    ModuleConfig,
    ModuleStatus
)
from ..logger import get_logger
from ..scanner.models import (
    DetectorResult,
    Issue,
    IssueSeverity,
    IssueType,
    ProjectMetadata,
    ScanConfiguration,
    ScanMetrics,
    ScanRequest,
    ScanResponse,
    ScanSummary
)
from ..scanner.detectors import (
    CodeQualityDetector,
    SecurityDetector,
    DependencyDetector,
    StructureDetector,
    PerformanceDetector
)


logger = get_logger("modules.scanner")


class ProjectScannerModule(BaseModule):
    """Modular project scanner with self-contained functionality."""

    def __init__(self, name: str):
        super().__init__(name, ModuleType.SCANNER)
        self.detectors: Dict[str, Any] = {}
        self._scan_lock = threading.Lock()

    async def initialize(self, config: ModuleConfig) -> bool:
        """Initialize the scanner module."""
        try:
            self._status = ModuleStatus.INITIALIZING

            # Get settings from config
            settings = config.settings or {}

            # Set default scan configuration
            self._default_config = ScanConfiguration(
                enable_code_quality=settings.get('enable_code_quality', True),
                enable_security=settings.get('enable_security', True),
                enable_dependencies=settings.get('enable_dependencies', True),
                enable_structure=settings.get('enable_structure', True),
                enable_performance=settings.get('enable_performance', True),
                min_severity=settings.get('min_severity', IssueSeverity.INFO),
                max_issues_per_type=settings.get('max_issues_per_type', 100),
                timeout_per_detector=settings.get('timeout_per_detector', 300)
            )

            self._status = ModuleStatus.READY
            logger.info(f"Scanner module '{self.name}' initialized successfully")
            return True

        except Exception as e:
            self._status = ModuleStatus.ERROR
            logger.error(f"Failed to initialize scanner module '{self.name}': {e}")
            return False

    async def validate(self, input_data: ModuleInput) -> bool:
        """Validate input data for scanning."""
        project_path = input_data.data.get('project_path')
        if not project_path:
            logger.error("No project_path specified in input data")
            return False

        if not Path(project_path).exists():
            logger.error(f"Project path not found: {project_path}")
            return False

        return True

    async def execute(self, input_data: ModuleInput) -> ModuleOutput:
        """Execute project scanning operation."""
        if not await self.validate(input_data):
            return ModuleOutput(
                success=False,
                error="Invalid input data"
            )

        project_path = input_data.data.get('project_path')
        scan_config = input_data.data.get('scan_config', self._default_config)
        scan_id = input_data.data.get('scan_id', str(uuid.uuid4()))

        self._status = ModuleStatus.RUNNING

        try:
            start_time = time.time()

            # Create scan request
            scan_request = ScanRequest(
                scan_id=scan_id,
                project_path=project_path,
                configuration=scan_config,
                created_at=datetime.now()
            )

            # Perform scan
            scan_response = await self._perform_scan(scan_request)

            duration = time.time() - start_time

            return ModuleOutput(
                success=scan_response.success,
                data={
                    'scan_id': scan_response.scan_id,
                    'project_path': scan_response.project_path,
                    'scan_start': scan_response.scan_start.isoformat(),
                    'scan_end': scan_response.scan_end.isoformat(),
                    'issues': [issue.dict() for issue in scan_response.issues],
                    'summary': scan_response.summary.dict() if scan_response.summary else None,
                    'metrics': scan_response.metrics.dict() if scan_response.metrics else None,
                    'errors': scan_response.errors
                },
                error=scan_response.errors[0] if scan_response.errors else None,
                metadata={
                    'scan_id': scan_id,
                    'project_path': project_path,
                    'duration': duration,
                    'total_issues': len(scan_response.issues),
                    'module': self.name
                }
            )

        except Exception as e:
            logger.error(f"Error scanning project {project_path}: {e}")
            return ModuleOutput(
                success=False,
                error=str(e),
                metadata={'project_path': project_path, 'module': self.name}
            )
        finally:
            self._status = ModuleStatus.READY

    async def _perform_scan(self, request: ScanRequest) -> ScanResponse:
        """Perform the actual scanning process."""
        scan_start = datetime.now()

        try:
            # Initialize detectors
            self._initialize_detectors(request.configuration)

            # Collect project metadata
            metadata = await self._collect_project_metadata(Path(request.project_path))

            # Run detectors
            detector_results = await self._run_detectors(Path(request.project_path), request.configuration)

            # Aggregate results
            all_issues = []
            total_files_processed = 0

            for result in detector_results:
                if result.success:
                    all_issues.extend(result.issues)
                    total_files_processed += result.files_processed

            # Apply scoring and prioritization
            scored_issues = await self._score_and_prioritize_issues(all_issues, metadata)

            # Filter issues based on configuration
            filtered_issues = await self._filter_issues(scored_issues, request.configuration)

            # Generate summary and metrics
            summary = await self._generate_summary(filtered_issues)
            metrics = await self._generate_metrics(detector_results, total_files_processed)

            scan_end = datetime.now()

            return ScanResponse(
                scan_id=request.scan_id,
                project_path=request.project_path,
                scan_start=scan_start,
                scan_end=scan_end,
                issues=filtered_issues,
                summary=summary,
                metrics=metrics,
                success=True
            )

        except Exception as e:
            scan_end = datetime.now()
            return ScanResponse(
                scan_id=request.scan_id,
                project_path=request.project_path,
                scan_start=scan_start,
                scan_end=scan_end,
                issues=[],
                summary=ScanSummary(),
                metrics=ScanMetrics(),
                success=False,
                errors=[str(e)]
            )

    def _initialize_detectors(self, config: ScanConfiguration) -> None:
        """Initialize enabled detectors based on configuration."""
        self.detectors.clear()

        if config.enable_code_quality:
            self.detectors['code_quality'] = CodeQualityDetector(config)

        if config.enable_security:
            self.detectors['security'] = SecurityDetector(config)

        if config.enable_dependencies:
            self.detectors['dependency'] = DependencyDetector(config)

        if config.enable_structure:
            self.detectors['structure'] = StructureDetector(config)

        if config.enable_performance:
            self.detectors['performance'] = PerformanceDetector(config)

    async def _collect_project_metadata(self, project_path: Path) -> ProjectMetadata:
        """Collect metadata about the project."""
        metadata = ProjectMetadata()

        try:
            # Basic project info
            metadata.project_name = project_path.name

            # Detect languages and frameworks
            languages = set()
            frameworks = set()
            total_files = 0
            total_size = 0

            for file_path in project_path.rglob('*'):
                if file_path.is_file():
                    total_files += 1
                    try:
                        file_size = file_path.stat().st_size
                        total_size += file_size

                        # Language detection based on file extension
                        ext = file_path.suffix.lower()
                        if ext == '.py':
                            languages.add('python')
                        elif ext in {'.js', '.jsx', '.ts', '.tsx'}:
                            languages.add('javascript')
                        elif ext in {'.html', '.htm'}:
                            languages.add('html')
                        elif ext == '.css':
                            languages.add('css')
                        elif ext in {'.sql', '.sqlite'}:
                            languages.add('sql')
                    except OSError:
                        continue

            # Framework detection for package.json
            if (project_path / 'package.json').exists():
                metadata.package_managers.append('npm')
                try:
                    import json
                    with open(project_path / 'package.json', 'r') as f:
                        package_data = json.load(f)
                        deps = {**package_data.get('dependencies', {}), **package_data.get('devDependencies', {})}

                        # Detect JavaScript frameworks
                        if 'react' in deps:
                            frameworks.add('react')
                        if 'vue' in deps:
                            frameworks.add('vue')
                        if 'next' in deps or 'nextjs' in deps:
                            frameworks.add('nextjs')
                except (json.JSONDecodeError, OSError):
                    pass

            # Python framework detection
            if (project_path / 'requirements.txt').exists() or (project_path / 'pyproject.toml').exists():
                metadata.package_managers.append('pip')

            # Git info
            if (project_path / '.git').exists():
                metadata.git_info = {'has_git': True}

            # Project type detection
            if 'python' in languages:
                frameworks.add('python')
                metadata.project_type = 'python'
            elif 'javascript' in languages:
                frameworks.add('javascript')
                metadata.project_type = 'javascript'

            metadata.languages = list(languages)
            metadata.frameworks = list(frameworks)
            metadata.total_files = total_files
            metadata.total_size = total_size

        except Exception:
            pass  # If metadata collection fails, continue with empty metadata

        return metadata

    async def _run_detectors(self, project_path: Path, config: ScanConfiguration) -> List[DetectorResult]:
        """Run all enabled detectors."""
        results = []

        # Run detectors in parallel for better performance
        with concurrent.futures.ThreadPoolExecutor(max_workers=len(self.detectors)) as executor:
            future_to_detector = {}

            for detector_name, detector in self.detectors.items():
                future = executor.submit(detector.detect, project_path)
                future_to_detector[future] = detector_name

            for future in concurrent.futures.as_completed(future_to_detector):
                detector_name = future_to_detector[future]
                try:
                    result = future.result(timeout=config.timeout_per_detector)
                    results.append(result)
                except concurrent.futures.TimeoutError:
                    # Create timeout error result
                    results.append(DetectorResult(
                        detector_name=detector_name,
                        issues=[],
                        execution_time=config.timeout_per_detector,
                        files_processed=0,
                        success=False,
                        error_message=f"Detector {detector_name} timed out after {config.timeout_per_detector} seconds"
                    ))
                except Exception as e:
                    # Create error result
                    results.append(DetectorResult(
                        detector_name=detector_name,
                        issues=[],
                        execution_time=0.0,
                        files_processed=0,
                        success=False,
                        error_message=str(e)
                    ))

        return results

    async def _score_and_prioritize_issues(self, issues: List[Issue], metadata: ProjectMetadata) -> List[Issue]:
        """Apply advanced scoring and prioritization to issues."""
        for issue in issues:
            # Adjust scores based on project context
            adjusted_impact = self._adjust_impact_score(issue, metadata)
            adjusted_likelihood = self._adjust_likelihood_score(issue, metadata)

            # Recalculate risk score
            issue.impact_score = adjusted_impact
            issue.likelihood_score = adjusted_likelihood
            issue.risk_score = round((adjusted_impact * adjusted_likelihood) / 100, 2)

        # Sort by risk score (highest first)
        return sorted(issues, key=lambda x: x.risk_score, reverse=True)

    def _adjust_impact_score(self, issue: Issue, metadata: ProjectMetadata) -> int:
        """Adjust impact score based on project context."""
        base_score = issue.impact_score

        # Adjust based on project type
        if metadata.project_type in ['javascript', 'react', 'nodejs']:
            # Web applications - security issues are more critical
            if issue.type in [IssueType.SECURITY_VULNERABILITY, IssueType.HARDCODED_SECRET]:
                base_score = min(100, base_score + 10)

        # Adjust based on file location
        if 'test' in issue.location.file_path.lower():
            # Issues in test files are less critical
            base_score = max(10, base_score - 15)
        elif any(pattern in issue.location.file_path.lower() for pattern in ['main', 'index', 'app', 'server']):
            # Issues in main files are more critical
            base_score = min(100, base_score + 10)

        # Adjust based on project size
        if metadata.total_files > 1000:
            # Large projects - maintainability issues are more critical
            if issue.type in [IssueType.CODE_COMPLEXITY, IssueType.MAINTAINABILITY]:
                base_score = min(100, base_score + 5)

        return base_score

    def _adjust_likelihood_score(self, issue: Issue, metadata: ProjectMetadata) -> int:
        """Adjust likelihood score based on project context."""
        base_score = issue.likelihood_score

        # Adjust based on frameworks used
        if 'react' in metadata.frameworks:
            # React projects - XSS issues are more likely
            if 'xss' in issue.title.lower():
                base_score = min(100, base_score + 10)

        return base_score

    async def _filter_issues(self, issues: List[Issue], config: ScanConfiguration) -> List[Issue]:
        """Filter issues based on configuration."""
        filtered = []

        severity_order = {
            IssueSeverity.INFO: 0,
            IssueSeverity.LOW: 1,
            IssueSeverity.MEDIUM: 2,
            IssueSeverity.HIGH: 3,
            IssueSeverity.CRITICAL: 4
        }

        min_severity_level = severity_order[config.min_severity]
        issue_counts_by_type = {}

        for issue in issues:
            # Filter by severity
            if severity_order[issue.severity] < min_severity_level:
                continue

            # Filter by max issues per type
            issue_type = issue.type
            if issue_type not in issue_counts_by_type:
                issue_counts_by_type[issue_type] = 0

            if issue_counts_by_type[issue_type] >= config.max_issues_per_type:
                continue

            issue_counts_by_type[issue_type] += 1
            filtered.append(issue)

        return filtered

    async def _generate_summary(self, issues: List[Issue]) -> ScanSummary:
        """Generate summary statistics for the scan."""
        summary = ScanSummary()

        summary.total_issues = len(issues)

        # Count by severity
        for issue in issues:
            if issue.severity not in summary.issues_by_severity:
                summary.issues_by_severity[issue.severity] = 0
            summary.issues_by_severity[issue.severity] += 1

        # Count by type
        for issue in issues:
            if issue.type not in summary.issues_by_type:
                summary.issues_by_type[issue.type] = 0
            summary.issues_by_type[issue.type] += 1

        # Top files by issues
        file_issue_counts = {}
        for issue in issues:
            file_path = issue.location.file_path
            if file_path not in file_issue_counts:
                file_issue_counts[file_path] = 0
            file_issue_counts[file_path] += 1

        summary.top_files_by_issues = [
            {"file": file_path, "issues": count}
            for file_path, count in sorted(file_issue_counts.items(), key=lambda x: x[1], reverse=True)[:10]
        ]

        # Risk distribution
        risk_ranges = {
            "low": 0,      # 0-30
            "medium": 0,   # 31-60
            "high": 0,     # 61-80
            "critical": 0  # 81-100
        }

        total_risk = 0
        for issue in issues:
            risk = issue.risk_score
            total_risk += risk

            if risk <= 30:
                risk_ranges["low"] += 1
            elif risk <= 60:
                risk_ranges["medium"] += 1
            elif risk <= 80:
                risk_ranges["high"] += 1
            else:
                risk_ranges["critical"] += 1

        summary.risk_distribution = risk_ranges
        summary.average_risk_score = round(total_risk / max(len(issues), 1), 2)

        return summary

    async def _generate_metrics(self, detector_results: List[DetectorResult], total_files: int) -> ScanMetrics:
        """Generate scan metrics."""
        metrics = ScanMetrics()

        metrics.files_processed = total_files
        total_execution_time = 0

        for result in detector_results:
            total_execution_time += result.execution_time

            # Store per-detector metrics
            metrics.detector_metrics[result.detector_name] = {
                'execution_time': result.execution_time,
                'files_processed': result.files_processed,
                'issues_found': len(result.issues),
                'success': result.success,
                'error_message': result.error_message
            }

        metrics.scan_duration = total_execution_time

        return metrics

    def get_detector_info(self) -> Dict[str, Any]:
        """Get information about available detectors."""
        return {
            'available_detectors': {
                'code_quality': {
                    'name': 'Code Quality Detector',
                    'description': 'Detects code complexity, duplication, and quality issues',
                    'supported_files': ['.py', '.js', '.ts', '.jsx', '.tsx', '.java', '.c', '.cpp']
                },
                'security': {
                    'name': 'Security Detector',
                    'description': 'Detects security vulnerabilities and OWASP Top 10 issues',
                    'supported_files': ['.py', '.js', '.ts', '.json', '.yaml', '.yml', '.env', '.ini']
                },
                'dependency': {
                    'name': 'Dependency Detector',
                    'description': 'Analyzes dependencies for vulnerabilities and issues',
                    'supported_files': ['requirements.txt', 'package.json', 'pyproject.toml', 'Pipfile']
                },
                'structure': {
                    'name': 'Structure Detector',
                    'description': 'Detects architectural and structural issues',
                    'supported_files': ['.py', '.js', '.ts', '.json', '.yaml', '.md']
                },
                'performance': {
                    'name': 'Performance Detector',
                    'description': 'Identifies performance bottlenecks and inefficiencies',
                    'supported_files': ['.py', '.js', '.ts', '.sql', '.json']
                }
            },
            'total_detectors': 5,
            'version': '1.0.0'
        }

    async def cleanup(self) -> None:
        """Cleanup module resources."""
        try:
            self.detectors.clear()
            await super().cleanup()
            logger.info(f"Scanner module '{self.name}' cleaned up")
        except Exception as e:
            logger.error(f"Error cleaning up scanner module: {e}")
