"""
Database configuration settings for the LONI backend application.
"""

from pydantic import Field
from pydantic_settings import BaseSettings


class DatabaseSettings(BaseSettings):
    """Database configuration settings."""
    
    # PostgreSQL settings
    POSTGRES_SERVER: str = Field(env="POSTGRES_SERVER", default="localhost")
    POSTGRES_USER: str = Field(env="POSTGRES_USER", default="loni_user")
    POSTGRES_PASSWORD: str = Field(env="POSTGRES_PASSWORD", default="loni_password")
    POSTGRES_DB: str = Field(env="POSTGRES_DB", default="loni_db")
    POSTGRES_PORT: int = Field(env="POSTGRES_PORT", default=5432)
    
    # Connection pool settings
    DATABASE_POOL_SIZE: int = Field(env="DATABASE_POOL_SIZE", default=5)
    DATABASE_MAX_OVERFLOW: int = Field(env="DATABASE_MAX_OVERFLOW", default=10)
    DATABASE_POOL_TIMEOUT: int = Field(env="DATABASE_POOL_TIMEOUT", default=30)
    
    # Dangerous reset guard: must be explicitly true to allow destructive ops
    ENABLE_DB_RESET: bool = Field(False, env="ENABLE_DB_RESET")
    
    @property
    def database_url(self) -> str:
        """Construct async database URL from components."""
        return (
            f"postgresql+asyncpg://{self.POSTGRES_USER}:{self.POSTGRES_PASSWORD}"
            f"@{self.POSTGRES_SERVER}:{self.POSTGRES_PORT}/{self.POSTGRES_DB}"
        )
    
    @property
    def sync_database_url(self) -> str:
        """Construct synchronous database URL for migrations."""
        return (
            f"postgresql://{self.POSTGRES_USER}:{self.POSTGRES_PASSWORD}"
            f"@{self.POSTGRES_SERVER}:{self.POSTGRES_PORT}/{self.POSTGRES_DB}"
        )
    
    class Config:
        env_file = ".env"
        case_sensitive = True