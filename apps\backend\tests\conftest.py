"""
Test configuration and fixtures for the LONI backend.
"""

import pytest
from typing import Generator, AsyncGenerator
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker
from app.core.settings import settings
from app.core.database import Base
from app.models.user import User
from app.models.canvas import <PERSON><PERSON>, Node, Connection


# Test database URL - use in-memory SQLite for testing
TEST_DATABASE_URL = "sqlite+aiosqlite:///:memory:"


@pytest.fixture(scope="session")
def anyio_backend():
    """AnyIO backend for async tests."""
    return "asyncio"


@pytest.fixture(scope="session")
async def engine():
    """Create test database engine."""
    engine = create_async_engine(TEST_DATABASE_URL, echo=False)
    yield engine
    await engine.dispose()


@pytest.fixture(scope="session")
async def create_tables(engine):
    """Create all tables in the test database."""
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)
    yield
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.drop_all)


@pytest.fixture
async def db_session(engine, create_tables) -> AsyncGenerator[AsyncSession, None]:
    """Create a database session for testing."""
    async_session_factory = sessionmaker(
        engine, class_=AsyncSession, expire_on_commit=False
    )
    async with async_session_factory() as session:
        yield session
        await session.rollback()


@pytest.fixture
async def test_user(db_session: AsyncSession) -> User:
    """Create a test user."""
    user = User(
        email="<EMAIL>",
        username="testuser",
        hashed_password="hashed_password",
        is_active=True,
        is_verified=True,
        is_superuser=False,
        full_name="Test User"
    )
    db_session.add(user)
    await db_session.commit()
    await db_session.refresh(user)
    return user


@pytest.fixture
async def test_canvas(db_session: AsyncSession, test_user: User) -> Canvas:
    """Create a test canvas."""
    canvas = Canvas(
        name="Test Canvas",
        description="A test canvas",
        is_public=False,
        tags=["test"],
        owner_id=test_user.id
    )
    db_session.add(canvas)
    await db_session.commit()
    await db_session.refresh(canvas)
    return canvas


@pytest.fixture
async def test_node(db_session: AsyncSession, test_canvas: Canvas) -> Node:
    """Create a test node."""
    node = Node(
        type="ui",
        title="Test Node",
        description="A test node",
        position={"x": 100, "y": 100},
        size={"width": 150, "height": 100},
        canvas_id=test_canvas.id
    )
    db_session.add(node)
    await db_session.commit()
    await db_session.refresh(node)
    return node


@pytest.fixture
async def test_connection(db_session: AsyncSession, test_canvas: Canvas, test_node: Node) -> Connection:
    """Create a test connection."""
    # Create a second node for the connection
    node2 = Node(
        type="logic",
        title="Test Node 2",
        description="Another test node",
        position={"x": 300, "y": 100},
        size={"width": 150, "height": 100},
        canvas_id=test_canvas.id
    )
    db_session.add(node2)
    await db_session.commit()
    await db_session.refresh(node2)
    
    connection = Connection(
        connection_type="default",
        source_node_id=test_node.id,
        target_node_id=node2.id,
        canvas_id=test_canvas.id
    )
    db_session.add(connection)
    await db_session.commit()
    await db_session.refresh(connection)
    return connection