#!/usr/bin/env python3
import os
import re

def count_classes_per_file():
    violations = []
    for root, dirs, files in os.walk("apps/backend/app"):
        for file in files:
            if file.endswith(".py"):
                filepath = os.path.join(root, file)
                try:
                    with open(filepath, 'r', encoding='utf-8') as f:
                        content = f.read()

                    # Count class definitions
                    class_count = len(re.findall(r'^class\s+\w+', content, re.MULTILINE))

                    if class_count > 1:
                        violations.append((filepath, class_count))
                except Exception as e:
                    print(f"Error reading {filepath}: {e}")

    return violations

if __name__ == "__main__":
    violations = count_classes_per_file()
    for filepath, count in violations:
        print(f"{filepath}: {count} classes")
