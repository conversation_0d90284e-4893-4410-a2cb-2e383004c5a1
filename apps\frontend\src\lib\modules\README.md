# Frontend Modular Architecture

## Overview

The frontend has been transformed into a modular, React-based architecture inspired by n8n's node system. Each UI component and data provider is a self-contained module with clear interfaces, configuration, and composability. This architecture enables:

- **Self-contained**: Each module has clear props, state, and dependencies
- **Reusable**: Modules can be used in different contexts and applications
- **Composable**: Modules can easily connect and work together like workflow nodes
- **Configurable**: Each module accepts configuration parameters instead of hardcoded values
- **Testable**: Each module can be independently tested with clear interfaces

## Architecture Components

### 1. Core Module System (`__init__.ts`)

#### Base Module Classes
The foundation for all modules providing common functionality:

```typescript
abstract class BaseModule<TInput = any, TOutput = any> {
  name: string;
  type: ModuleType;
  status: ModuleStatus;
  config: ModuleConfig;

  async initialize(config: ModuleConfig): Promise<boolean>;
  abstract execute(input: ModuleInput<TInput>): Promise<ModuleOutput<TOutput>>;
  validate(input: ModuleInput<TInput>): boolean;
  destroy(): Promise<void>;
  getDependencies(): string[];
}
```

#### Module Types
- `UI_COMPONENT`: Reusable React components
- `DATA_PROVIDER`: Data fetching and management
- `STATE_MANAGER`: State management and persistence
- `API_CLIENT`: HTTP API communication
- `UTILITY`: Helper functions and utilities

#### Data Flow
```typescript
interface ModuleInput<T = any> {
  data: T;
  metadata?: Record<string, any>;
  context?: Record<string, any>;
}

interface ModuleOutput<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  metadata?: Record<string, any>;
}
```

### 2. UI Component Modules (`ui-components.tsx`)

#### Purpose
Provides reusable React components as modular nodes.

#### Available Components

**Button Module**
```typescript
const button = new ButtonModule('main-button');

// Usage
const result = await button.execute({
  data: {
    children: 'Click me',
    variant: 'primary',
    onClick: () => console.log('Clicked!')
  }
});
```

**Card Module**
```typescript
const card = new CardModule('info-card');

const result = await card.execute({
  data: {
    title: 'Information',
    description: 'Important details',
    children: <p>Card content</p>
  }
});
```

**Chart Module**
```typescript
const chart = new ChartModule('analytics-chart');

const result = await chart.execute({
  data: {
    type: 'bar',
    data: [
      { label: 'Jan', value: 65 },
      { label: 'Feb', value: 78 }
    ],
    title: 'Monthly Sales'
  }
});
```

### 3. Data Provider Modules (`data-providers.tsx`)

#### Purpose
Manages data fetching, caching, and state management.

#### API Client Module
```typescript
const apiClient = new APIClientModule('api-client');

await apiClient.initialize(ModuleConfig.createConfig('api-client', ModuleType.API_CLIENT, {
  baseURL: '/api/v1',
  cacheTTL: 300000
}));

const result = await apiClient.execute({
  data: { method: 'GET', endpoint: '/models' }
});
```

#### State Manager Module
```typescript
const stateManager = new StateManagerModule('app-state', {
  isLoading: false,
  user: null
});

const result = await stateManager.execute({
  data: { action: 'set', payload: { isLoading: true } }
});
```

#### Cache Manager Module
```typescript
const cacheManager = new CacheManagerModule('cache-manager');

await cacheManager.execute({
  data: { action: 'set', key: 'user-profile', data: userData }
});

const cached = await cacheManager.execute({
  data: { action: 'get', key: 'user-profile' }
});
```

## React Integration

### 1. Module Registry and Hooks

```typescript
// Using the module registry
const apiClient = registry.get<APIClientModule>('api-client');

// Using React hooks
function useModule<T extends BaseModule>(name: string): T | undefined;
function useModuleState<T extends BaseModule>(name: string);
function useModuleExecutor<T extends BaseModule>(name: string, operation: string);
```

### 2. Component Integration

```tsx
function MyComponent() {
  const buttonModule = useModule<ButtonModule>('main-button');
  const { execute } = useModuleExecutor('api-client', 'fetch');

  const handleClick = async () => {
    const result = await execute({
      data: { method: 'GET', endpoint: '/data' }
    });

    if (result.success) {
      console.log(result.data);
    }
  };

  return (
    <div>
      {buttonModule?.render({
        children: 'Load Data',
        onClick: handleClick,
        variant: 'primary'
      })}
    </div>
  );
}
```

### 3. State Management

```tsx
function useAppState() {
  const { state, setState, updateState } = useStateManager<AppState>('app-state');

  return {
    isLoading: state?.isLoading ?? false,
    user: state?.user,
    setLoading: (loading: boolean) => updateState({ isLoading: loading }),
    setUser: (user: User) => updateState({ user })
  };
}
```

## Module Registration and Initialization

### 1. Setting up the Application

```tsx
// src/app/layout.tsx or src/app/page.tsx
import { initializeModularApp } from '@/lib/modules/app';

export default function RootLayout({ children }: { children: ReactNode }) {
  useEffect(() => {
    initializeModularApp().then((results) => {
      console.log('Modules initialized:', results);
    });
  }, []);

  return (
    <html lang="en">
      <body>{children}</body>
    </html>
  );
}
```

### 2. Module Configuration

```tsx
// Configure modules before initialization
const configs = [
  ModuleFactory.createConfig('main-button', ModuleType.UI_COMPONENT, {
    defaultVariant: 'primary',
    defaultSize: 'md'
  }, { priority: 10 }),

  ModuleFactory.createConfig('api-client', ModuleType.API_CLIENT, {
    baseURL: '/api/v1',
    headers: { 'Content-Type': 'application/json' },
    cacheTTL: 300000
  }, { priority: 20 }),

  ModuleFactory.createConfig('app-state', ModuleType.STATE_MANAGER, {
    persist: true,
    storageKey: 'modular-app-state'
  }, { priority: 30 })
];

// Set configurations
configs.forEach(config => {
  registry.setConfig(config.name, config);
});
```

### 3. Dependency Injection

```tsx
// Set up dependencies between modules
dataProvider.setDependency('apiClient', apiClient);

// Dependencies are automatically resolved
const dependencies = module.getDependencies();
// Returns: ['apiClient']
```

## Complete Example Application

### 1. App Structure (`app.tsx`)

```tsx
export function ModularDashboard() {
  const { isInitialized } = useModularApp();

  if (!isInitialized) {
    return <LoadingSpinner />;
  }

  return (
    <div className="min-h-screen bg-background">
      <Header />
      <Navigation />
      <MainContent />
    </div>
  );
}
```

### 2. Component Modules

```tsx
// Header component as a module
export class HeaderModule extends UIComponentModule<HeaderProps> {
  render(props) {
    return (
      <header className="bg-card border-b p-4">
        <h1 className="text-2xl font-bold">{props.title}</h1>
      </header>
    );
  }
}

// Navigation as a module
export class NavigationModule extends UIComponentModule<NavigationProps> {
  render(props) {
    return (
      <nav className="bg-card border-b">
        {props.items.map(item => (
          <button key={item.id} onClick={() => props.onSelect(item.id)}>
            {item.label}
          </button>
        ))}
      </nav>
    );
  }
}
```

### 3. Data Flow

```tsx
// Data provider for fetching models
export class ModelsProviderModule extends BaseModule {
  private apiClient: APIClientModule | null = null;

  async initialize(config: ModuleConfig): Promise<boolean> {
    await super.initialize(config);
    this.apiClient = this.getDependency<APIClientModule>('apiClient');
    return true;
  }

  async execute(input: ModuleInput): Promise<ModuleOutput> {
    if (!this.apiClient) {
      return { success: false, error: 'API client not available' };
    }

    const result = await this.apiClient.execute({
      data: { method: 'GET', endpoint: '/models' }
    });

    return result;
  }
}
```

## Benefits of the Modular Architecture

### 1. Component Reusability
Each component is a self-contained module:
```tsx
// Use the same button module in different contexts
const primaryButton = buttonModule.render({
  children: 'Save',
  variant: 'primary'
});

const secondaryButton = buttonModule.render({
  children: 'Cancel',
  variant: 'secondary'
});
```

### 2. Easy Testing
Test components in isolation:
```tsx
describe('ButtonModule', () => {
  it('renders with correct props', () => {
    const button = new ButtonModule('test-button');
    const element = button.render({ children: 'Test' });

    expect(element.props.children).toBe('Test');
  });
});
```

### 3. Configuration-Driven Development
No hardcoded values:
```tsx
// Different configurations for different environments
const devConfig = ModuleConfig.createConfig('api-client', ModuleType.API_CLIENT, {
  baseURL: 'http://localhost:3001/api/v1'
});

const prodConfig = ModuleConfig.createConfig('api-client', ModuleType.API_CLIENT, {
  baseURL: 'https://api.loni.app/v1'
});
```

### 4. Type Safety
Full TypeScript support:
```tsx
interface ButtonProps {
  children: ReactNode;
  variant?: 'primary' | 'secondary' | 'outline';
  size?: 'sm' | 'md' | 'lg';
  disabled?: boolean;
  onClick?: () => void;
}
```

### 5. Performance Optimization
Built-in caching and optimization:
```tsx
// API responses are automatically cached
const cachedResult = await apiClient.execute({
  data: { method: 'GET', endpoint: '/models' }
});
```

## Best Practices

### 1. Component Design
- Keep components focused on a single responsibility
- Use clear, descriptive prop interfaces
- Document component behavior and usage
- Handle edge cases gracefully

### 2. Module Configuration
- Never hardcode values in component implementations
- Use configuration objects for all settings
- Validate props at runtime
- Support theme and styling customization

### 3. State Management
- Use state manager modules for complex state
- Implement proper state persistence
- Handle state synchronization between modules
- Use optimistic updates for better UX

### 4. Error Handling
- Implement error boundaries for component modules
- Provide meaningful error messages
- Log errors with context information
- Graceful degradation on failures

### 5. Performance
- Implement React.memo for expensive components
- Use proper key props for dynamic lists
- Optimize re-renders with useMemo and useCallback
- Implement virtualization for large lists

## Migration Guide

### From Traditional React to Modular

1. **Identify Components**: Find reusable components in your application
2. **Define Interfaces**: Create clear prop interfaces for each component
3. **Extract Modules**: Convert components to module classes
4. **Configure Dependencies**: Set up dependencies between modules
5. **Update Usage**: Replace component usage with module execution
6. **Test Integration**: Ensure all modules work together correctly

### Example Migration

**Before (Traditional Component)**:
```tsx
function Button({ children, variant = 'primary', ...props }) {
  return (
    <button
      className={`btn btn-${variant}`}
      {...props}
    >
      {children}
    </button>
  );
}
```

**After (Modular Component)**:
```tsx
class ButtonModule extends UIComponentModule<ButtonProps> {
  render(props) {
    const variants = {
      primary: 'bg-primary text-primary-foreground',
      secondary: 'bg-secondary text-secondary-foreground'
    };

    return (
      <button
        className={`${variants[props.variant || 'primary']} px-4 py-2 rounded`}
        onClick={props.onClick}
      >
        {props.children}
      </button>
    );
  }
}
```

## Advanced Features

### 1. Dynamic Module Loading
Load modules on demand:
```tsx
const loadModule = async (moduleName: string) => {
  const module = await import(`./modules/${moduleName}`);
  return new module.default();
};
```

### 2. Module Marketplace
Share and discover modules:
```tsx
// Module registry with external sources
const marketplace = new ModuleMarketplace();
await marketplace.install('user/analytics-chart');
```

### 3. A/B Testing
Test different module versions:
```tsx
const abTester = new ABTestManager();
const variant = await abTester.getVariant('button-module');
const module = variant === 'A' ? new ButtonModuleA() : new ButtonModuleB();
```

### 4. Real-time Collaboration
Multi-user module editing:
```tsx
const collaboration = new ModuleCollaboration();
await collaboration.joinSession('project-123');
```

This modular architecture provides a solid foundation for building scalable, maintainable, and extensible React applications using the n8n-like component-based approach.
