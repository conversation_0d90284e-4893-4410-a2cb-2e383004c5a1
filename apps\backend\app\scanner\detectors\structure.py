"""
Structure Detector for the Project Scanner system.

This module detects structural and architectural issues including circular dependencies,
unused files, missing files, and architecture violations.
"""

import ast
import re
from collections import defaultdict, deque
from pathlib import Path
from typing import Any, Dict, List, Optional, Set, Tuple

from ..base_detector import BaseDetector
from ..models import (
    DetectorResult,
    Issue,
    IssueSeverity,
    IssueType,
    RemediationSuggestion
)


class StructureDetector(BaseDetector):
    """
    Detects structural and architectural issues including:
    - Circular dependencies between modules
    - Unused files and dead code
    - Missing configuration files
    - Architecture violations
    - Project structure problems
    """
    
    def __init__(self, config):
        super().__init__("Structure", config)
        self.import_graph = defaultdict(set)
        self.file_references = defaultdict(set)
    
    def get_supported_file_types(self) -> Set[str]:
        """Get supported file extensions."""
        return {'.py', '.js', '.ts', '.jsx', '.tsx', '.json', '.yaml', '.yml', '.md', '.txt'}
    
    def detect(self, project_path: Path) -> DetectorResult:
        """Detect structural issues."""
        self.start_detection()
        
        try:
            # Build dependency graph
            self._build_dependency_graph(project_path)
            
            # Detect issues
            self._detect_circular_dependencies(project_path)
            self._detect_unused_files(project_path)
            self._detect_missing_files(project_path)
            self._detect_architecture_violations(project_path)
            self._detect_structure_problems(project_path)
            
            return self.finish_detection()
            
        except Exception as e:
            return self.finish_detection(success=False, error_message=str(e))
    
    def _build_dependency_graph(self, project_path: Path) -> None:
        """Build dependency graph by analyzing imports."""
        python_files = list(project_path.rglob('*.py'))
        
        for py_file in python_files:
            try:
                content = self.read_file_safely(py_file)
                if not content:
                    continue
                
                relative_path = str(py_file.relative_to(project_path))
                imports = self._extract_imports(content, py_file, project_path)
                
                for imported_module in imports:
                    self.import_graph[relative_path].add(imported_module)
                    self.file_references[imported_module].add(relative_path)
                
                self.files_processed += 1
                
            except Exception:
                continue
    
    def _extract_imports(self, content: str, file_path: Path, project_path: Path) -> Set[str]:
        """Extract import statements from Python file."""
        imports = set()
        
        try:
            tree = ast.parse(content)
            
            for node in ast.walk(tree):
                if isinstance(node, ast.Import):
                    for alias in node.names:
                        imports.add(alias.name)
                
                elif isinstance(node, ast.ImportFrom):
                    if node.module:
                        # Convert relative imports to absolute paths
                        if node.level > 0:  # Relative import
                            current_package = str(file_path.parent.relative_to(project_path)).replace('/', '.')
                            if node.module:
                                imports.add(f"{current_package}.{node.module}")
                            else:
                                imports.add(current_package)
                        else:
                            imports.add(node.module)
        
        except SyntaxError:
            pass  # Skip files with syntax errors
        
        return imports
    
    def _detect_circular_dependencies(self, project_path: Path) -> None:
        """Detect circular dependencies in the import graph."""
        def has_cycle_from(start: str, visited: Set[str], rec_stack: Set[str]) -> Optional[List[str]]:
            """DFS to detect cycles."""
            visited.add(start)
            rec_stack.add(start)
            
            for neighbor in self.import_graph[start]:
                if neighbor not in visited:
                    cycle = has_cycle_from(neighbor, visited, rec_stack)
                    if cycle:
                        return [start] + cycle
                elif neighbor in rec_stack:
                    return [start, neighbor]
            
            rec_stack.remove(start)
            return None
        
        visited = set()
        
        for file_path in self.import_graph:
            if file_path not in visited:
                cycle = has_cycle_from(file_path, visited, set())
                if cycle:
                    cycle_description = " → ".join(cycle)
                    
                    self.add_issue(self.create_issue(
                        IssueType.CIRCULAR_DEPENDENCY,
                        IssueSeverity.HIGH,
                        f"Circular dependency detected",
                        f"Circular dependency found: {cycle_description}. "
                        "This can cause import errors and makes code harder to understand.",
                        cycle[0],
                        rule_id='circular_dependency',
                        impact_score=70,
                        likelihood_score=90,
                        remediation=RemediationSuggestion(
                            title="Break circular dependency",
                            description="Refactor code to eliminate circular imports. Consider using dependency injection or moving shared code to a separate module.",
                            effort_estimate="1-2 hours",
                            references=["https://docs.python.org/3/faq/programming.html#how-do-i-share-global-variables-across-modules"]
                        )
                    ))
    
    def _detect_unused_files(self, project_path: Path) -> None:
        """Detect potentially unused files."""
        all_python_files = set(str(f.relative_to(project_path)) for f in project_path.rglob('*.py'))
        referenced_files = set()
        
        # Files that are referenced by imports
        for file_path in self.import_graph:
            referenced_files.add(file_path)
            for imported in self.import_graph[file_path]:
                # Convert module names back to file paths (simplified)
                potential_file = imported.replace('.', '/') + '.py'
                if potential_file in all_python_files:
                    referenced_files.add(potential_file)
        
        # Special files that are always considered used
        special_files = {
            '__init__.py', 'setup.py', 'conftest.py', 'manage.py',
            'wsgi.py', 'asgi.py', 'settings.py', 'urls.py'
        }
        
        # Entry point files
        entry_points = set()
        for py_file in project_path.rglob('*.py'):
            relative_path = str(py_file.relative_to(project_path))
            
            # Check if file has if __name__ == "__main__"
            content = self.read_file_safely(py_file)
            if content and '__main__' in content:
                entry_points.add(relative_path)
        
        # Find unused files
        for py_file_path in all_python_files:
            file_name = Path(py_file_path).name
            
            # Skip special files and entry points
            if (file_name in special_files or 
                py_file_path in entry_points or
                py_file_path in referenced_files):
                continue
            
            # Skip test files (they might not be imported directly)
            if any(test_pattern in py_file_path for test_pattern in ['test_', '_test', 'tests/']):
                continue
            
            self.add_issue(self.create_issue(
                IssueType.UNUSED_FILE,
                IssueSeverity.LOW,
                f"Potentially unused file: {file_name}",
                f"File '{py_file_path}' appears to be unused. It's not imported by any other files.",
                py_file_path,
                rule_id='unused_file',
                impact_score=20,
                likelihood_score=60,
                remediation=RemediationSuggestion(
                    title="Review and remove unused file",
                    description="If this file is truly unused, consider removing it to reduce codebase complexity.",
                    effort_estimate="10 minutes"
                )
            ))
    
    def _detect_missing_files(self, project_path: Path) -> None:
        """Detect missing important files."""
        important_files = {
            'README.md': {
                'severity': IssueSeverity.MEDIUM,
                'description': 'Project documentation file'
            },
            'requirements.txt': {
                'severity': IssueSeverity.LOW,
                'description': 'Python dependencies file'
            },
            '.gitignore': {
                'severity': IssueSeverity.LOW,
                'description': 'Git ignore patterns file'
            },
            'setup.py': {
                'severity': IssueSeverity.LOW,
                'description': 'Python package setup file'
            },
            'LICENSE': {
                'severity': IssueSeverity.MEDIUM,
                'description': 'Project license file'
            }
        }
        
        # Check for Python project indicators
        has_python_files = any(project_path.rglob('*.py'))
        has_js_files = any(project_path.rglob('*.js')) or any(project_path.rglob('*.ts'))
        
        for file_name, config in important_files.items():
            file_path = project_path / file_name
            
            # Skip Python-specific files if not a Python project
            if file_name in ['requirements.txt', 'setup.py'] and not has_python_files:
                continue
            
            if not file_path.exists():
                # Check for variants
                variants = []
                if file_name == 'README.md':
                    variants = ['README.rst', 'README.txt', 'readme.md']
                elif file_name == 'LICENSE':
                    variants = ['LICENSE.txt', 'LICENSE.md', 'license']
                
                found_variant = any((project_path / variant).exists() for variant in variants)
                
                if not found_variant:
                    self.add_issue(self.create_issue(
                        IssueType.MISSING_FILE,
                        config['severity'],
                        f"Missing {config['description']}",
                        f"Important file '{file_name}' is missing from the project.",
                        ".",  # Project root
                        rule_id=f'missing_{file_name.lower().replace(".", "_")}',
                        impact_score=30 if config['severity'] == IssueSeverity.MEDIUM else 15,
                        likelihood_score=70,
                        remediation=RemediationSuggestion(
                            title=f"Create {file_name}",
                            description=f"Add {file_name} to provide {config['description']}.",
                            effort_estimate="15 minutes"
                        )
                    ))
    
    def _detect_architecture_violations(self, project_path: Path) -> None:
        """Detect common architecture violations."""
        # Check for imports from higher-level modules to lower-level ones
        self._check_layered_architecture_violations(project_path)
        
        # Check for business logic in view/controller files
        self._check_business_logic_violations(project_path)
        
        # Check for database access in presentation layer
        self._check_data_access_violations(project_path)
    
    def _check_layered_architecture_violations(self, project_path: Path) -> None:
        """Check for violations of layered architecture."""
        # Define typical layers (can be customized)
        layer_hierarchy = {
            'views': 0,      # Highest level
            'controllers': 1,
            'services': 2,
            'models': 3,     # Lowest level
            'utils': 4
        }
        
        for file_path, imports in self.import_graph.items():
            current_layer = self._identify_layer(file_path, layer_hierarchy)
            if current_layer is None:
                continue
            
            for imported_module in imports:
                imported_layer = self._identify_layer(imported_module, layer_hierarchy)
                if imported_layer is None:
                    continue
                
                # Check if importing from a higher layer (violation)
                if layer_hierarchy[imported_layer] < layer_hierarchy[current_layer]:
                    self.add_issue(self.create_issue(
                        IssueType.ARCHITECTURE_VIOLATION,
                        IssueSeverity.MEDIUM,
                        f"Architecture violation: {current_layer} importing from {imported_layer}",
                        f"File in '{current_layer}' layer is importing from higher-level '{imported_layer}' layer. "
                        "This violates layered architecture principles.",
                        file_path,
                        rule_id='layered_architecture_violation',
                        impact_score=50,
                        likelihood_score=70,
                        remediation=RemediationSuggestion(
                            title="Refactor to respect layer boundaries",
                            description="Move shared functionality to a lower layer or use dependency injection.",
                            effort_estimate="1-2 hours"
                        )
                    ))
    
    def _identify_layer(self, file_path: str, layer_hierarchy: Dict[str, int]) -> Optional[str]:
        """Identify which architectural layer a file belongs to."""
        file_path_lower = file_path.lower()
        
        for layer in layer_hierarchy:
            if layer in file_path_lower:
                return layer
        
        return None
    
    def _check_business_logic_violations(self, project_path: Path) -> None:
        """Check for business logic in presentation layer."""
        view_files = [f for f in self.import_graph.keys() 
                     if any(pattern in f.lower() for pattern in ['view', 'controller', 'handler'])]
        
        business_logic_patterns = [
            r'def calculate_',
            r'def process_',
            r'def validate_\w+_business',
            r'complex_calculation',
            r'business_rule'
        ]
        
        for view_file in view_files:
            full_path = project_path / view_file
            content = self.read_file_safely(full_path)
            
            if content:
                for pattern in business_logic_patterns:
                    if re.search(pattern, content):
                        self.add_issue(self.create_issue(
                            IssueType.ARCHITECTURE_VIOLATION,
                            IssueSeverity.LOW,
                            "Business logic in presentation layer",
                            f"File '{view_file}' appears to contain business logic, which should be in service layer.",
                            view_file,
                            rule_id='business_logic_in_views',
                            impact_score=30,
                            likelihood_score=60
                        ))
                        break
    
    def _check_data_access_violations(self, project_path: Path) -> None:
        """Check for direct database access in presentation layer."""
        presentation_files = [f for f in self.import_graph.keys() 
                            if any(pattern in f.lower() for pattern in ['view', 'controller', 'template'])]
        
        data_access_patterns = [
            r'\.execute\(',
            r'\.query\(',
            r'SELECT\s+',
            r'INSERT\s+INTO',
            r'UPDATE\s+',
            r'DELETE\s+FROM'
        ]
        
        for pres_file in presentation_files:
            full_path = project_path / pres_file
            content = self.read_file_safely(full_path)
            
            if content:
                for pattern in data_access_patterns:
                    if re.search(pattern, content, re.IGNORECASE):
                        self.add_issue(self.create_issue(
                            IssueType.ARCHITECTURE_VIOLATION,
                            IssueSeverity.MEDIUM,
                            "Database access in presentation layer",
                            f"File '{pres_file}' contains direct database access. "
                            "Database operations should be handled by data access layer.",
                            pres_file,
                            rule_id='database_access_in_presentation',
                            impact_score=40,
                            likelihood_score=70
                        ))
                        break
    
    def _detect_structure_problems(self, project_path: Path) -> None:
        """Detect general project structure problems."""
        # Check for deeply nested directories
        self._check_deep_nesting(project_path)
        
        # Check for too many files in single directory
        self._check_directory_size(project_path)
        
        # Check for inconsistent naming
        self._check_naming_consistency(project_path)
    
    def _check_deep_nesting(self, project_path: Path) -> None:
        """Check for deeply nested directory structures."""
        max_depth = 6  # Configurable threshold
        
        for path in project_path.rglob('*'):
            if path.is_file():
                relative_path = path.relative_to(project_path)
                depth = len(relative_path.parts)
                
                if depth > max_depth:
                    self.add_issue(self.create_issue(
                        IssueType.ARCHITECTURE_VIOLATION,
                        IssueSeverity.LOW,
                        f"Deep directory nesting ({depth} levels)",
                        f"File '{relative_path}' is nested {depth} levels deep. "
                        "Consider flattening the directory structure.",
                        str(relative_path),
                        rule_id='deep_directory_nesting',
                        impact_score=20,
                        likelihood_score=50
                    ))
    
    def _check_directory_size(self, project_path: Path) -> None:
        """Check for directories with too many files."""
        max_files_per_dir = 20  # Configurable threshold
        
        for directory in project_path.rglob('*'):
            if not directory.is_dir():
                continue
            
            files = [f for f in directory.iterdir() if f.is_file()]
            if len(files) > max_files_per_dir:
                relative_dir = directory.relative_to(project_path)
                
                self.add_issue(self.create_issue(
                    IssueType.ARCHITECTURE_VIOLATION,
                    IssueSeverity.LOW,
                    f"Directory has too many files ({len(files)})",
                    f"Directory '{relative_dir}' contains {len(files)} files. "
                    "Consider organizing files into subdirectories.",
                    str(relative_dir),
                    rule_id='large_directory',
                    impact_score=25,
                    likelihood_score=60
                ))
    
    def _check_naming_consistency(self, project_path: Path) -> None:
        """Check for inconsistent file naming patterns."""
        python_files = list(project_path.rglob('*.py'))
        
        naming_patterns = {
            'snake_case': re.compile(r'^[a-z][a-z0-9_]*\.py$'),
            'PascalCase': re.compile(r'^[A-Z][a-zA-Z0-9]*\.py$'),
            'camelCase': re.compile(r'^[a-z][a-zA-Z0-9]*\.py$')
        }
        
        pattern_counts = {pattern: 0 for pattern in naming_patterns}
        
        # Count patterns
        for py_file in python_files:
            file_name = py_file.name
            
            # Skip special files
            if file_name.startswith('__') or file_name in ['setup.py', 'manage.py']:
                continue
            
            for pattern_name, pattern_regex in naming_patterns.items():
                if pattern_regex.match(file_name):
                    pattern_counts[pattern_name] += 1
                    break
        
        # Find dominant pattern
        dominant_pattern = max(pattern_counts, key=pattern_counts.get)
        total_files = sum(pattern_counts.values())
        
        if total_files > 5:  # Only check if there are enough files
            dominant_percentage = pattern_counts[dominant_pattern] / total_files
            
            if dominant_percentage < 0.8:  # Less than 80% consistency
                self.add_issue(self.create_issue(
                    IssueType.STYLE_VIOLATION,
                    IssueSeverity.LOW,
                    "Inconsistent file naming patterns",
                    f"Project uses mixed naming conventions. Dominant pattern is {dominant_pattern} "
                    f"({pattern_counts[dominant_pattern]}/{total_files} files).",
                    ".",
                    rule_id='inconsistent_naming',
                    impact_score=15,
                    likelihood_score=70,
                    remediation=RemediationSuggestion(
                        title="Standardize naming convention",
                        description=f"Consider renaming files to follow the {dominant_pattern} convention consistently.",
                        effort_estimate="30 minutes"
                    )
                ))