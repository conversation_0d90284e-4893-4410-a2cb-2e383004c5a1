"""
Tests for the Models page component.
"""

import React from 'react';
import { render, screen, fireEvent, waitFor, act } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest';
import ModelsPage from '../page';
import { modelService, Model, AvailableModel, InstallProgress, HealthCheckResult } from '@/shared/services/model-service';
import { useToast } from '@/shared/ui/providers';

// Mock dependencies
vi.mock('@/shared/services/model-service');
vi.mock('@/shared/ui/providers');
vi.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
  },
  AnimatePresence: ({ children }: any) => children,
}));

// Mock implementations
const mockModelService = vi.mocked(modelService);
const mockAddToast = vi.fn();
const mockUseToast = vi.mocked(useToast);

mockUseToast.mockReturnValue({
  addToast: mockAddToast,
});

// Sample data
const sampleInstalledModels: Model[] = [
  {
    id: {
      provider: 'WHISPER',
      name: 'tiny',
      version: '1.0',
    },
    status: 'installed',
    metadata: {
      size_on_disk_bytes: 40960000,
      installed_at: '2024-01-01T12:00:00Z',
      source: 'whisper_sdk',
      capabilities: ['transcription', 'translation'],
      requirements: {
        memory_gb: 1,
        disk_gb: 1,
        gpu_required: false,
      },
      extra: {
        catalog_version: '1.0.0',
      },
    },
  },
  {
    id: {
      provider: 'OLLAMA',
      name: 'llama3',
      version: '8b',
    },
    status: 'installed',
    metadata: {
      size_on_disk_bytes: **********,
      installed_at: '2024-01-01T13:00:00Z',
      source: 'ollama_cli',
      capabilities: ['text-generation', 'chat'],
      requirements: {
        memory_gb: 8,
        disk_gb: 5,
        gpu_required: true,
      },
      extra: {},
    },
  },
];

const sampleWhisperModels: AvailableModel[] = [
  {
    provider: 'WHISPER',
    name: 'base',
    version: '1.0',
    metadata: {
      size_bytes: 74000000,
      description: 'Base Whisper model for speech recognition',
      capabilities: ['transcription'],
      requirements: {
        memory_gb: 2,
        disk_gb: 1,
        gpu_required: false,
      },
      popularity_score: 85,
      last_updated: '2024-01-01T00:00:00Z',
    },
  },
  {
    provider: 'WHISPER',
    name: 'large',
    version: '1.0',
    metadata: {
      size_bytes: **********,
      description: 'Large Whisper model with best accuracy',
      capabilities: ['transcription', 'translation'],
      requirements: {
        memory_gb: 4,
        disk_gb: 2,
        gpu_required: true,
      },
      popularity_score: 95,
      last_updated: '2024-01-01T00:00:00Z',
    },
  },
];

const sampleOllamaModels: AvailableModel[] = [
  {
    provider: 'OLLAMA',
    name: 'mistral',
    version: '7b',
    metadata: {
      size_bytes: **********,
      description: 'Mistral 7B language model',
      capabilities: ['text-generation', 'chat'],
      requirements: {
        memory_gb: 8,
        disk_gb: 4,
        gpu_required: true,
      },
      popularity_score: 90,
      last_updated: '2024-01-01T00:00:00Z',
    },
  },
];

const sampleHealthResult: HealthCheckResult = {
  status: 'healthy',
  details: {
    model_loaded: true,
    memory_usage: 512,
    response_time_ms: 150,
    last_request: '2024-01-01T12:00:00Z',
  },
  timestamp: '2024-01-01T12:00:00Z',
};

const sampleInstallProgress: InstallProgress = {
  status: 'downloading',
  progress: 45,
  downloaded_bytes: 450000000,
  total_bytes: **********,
  current_step: 'Downloading model weights',
};

describe('ModelsPage', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    
    // Setup default mock implementations
    mockModelService.getInstalledModels.mockResolvedValue({
      items: sampleInstalledModels,
      total: 2,
      page: 1,
      page_size: 50,
    });
    
    mockModelService.getAvailableModels.mockImplementation((provider) => {
      if (provider === 'whisper') {
        return Promise.resolve({
          items: sampleWhisperModels,
          total: 2,
          page: 1,
          page_size: 50,
        });
      } else if (provider === 'ollama') {
        return Promise.resolve({
          items: sampleOllamaModels,
          total: 1,
          page: 1,
          page_size: 50,
        });
      }
      return Promise.resolve({ items: [], total: 0, page: 1, page_size: 50 });
    });
    
    mockModelService.checkModelHealth.mockResolvedValue(sampleHealthResult);
    mockModelService.subscribeToInstallProgress.mockReturnValue(() => {});
  });
  
  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('Initial Loading', () => {
    it('should render loading state initially', async () => {
      // Make the service calls hang to test loading state
      mockModelService.getInstalledModels.mockReturnValue(new Promise(() => {}));
      
      render(<ModelsPage />);
      
      expect(screen.getByText('Loading models...')).toBeInTheDocument();
      expect(screen.getByTestId('loading-spinner') || screen.getByRole('progressbar')).toBeInTheDocument();
    });
    
    it('should load and display models after initial load', async () => {
      render(<ModelsPage />);
      
      await waitFor(() => {
        expect(screen.getByText('Model Management')).toBeInTheDocument();
      });
      
      // Should show installed models by default
      expect(screen.getByText('tiny')).toBeInTheDocument();
      expect(screen.getByText('llama3')).toBeInTheDocument();
      expect(screen.getByText('WHISPER')).toBeInTheDocument();
      expect(screen.getByText('OLLAMA')).toBeInTheDocument();
    });
    
    it('should handle loading errors gracefully', async () => {
      const error = new Error('Failed to fetch models');
      mockModelService.getInstalledModels.mockRejectedValue(error);
      
      render(<ModelsPage />);
      
      await waitFor(() => {
        expect(screen.getByText('Failed to Load Models')).toBeInTheDocument();
        expect(screen.getByText('Failed to fetch models')).toBeInTheDocument();
      });
      
      expect(mockAddToast).toHaveBeenCalledWith({
        title: 'Error',
        description: 'Failed to fetch models',
        variant: 'error',
      });
    });
  });

  describe('Tab Navigation', () => {
    it('should switch between tabs correctly', async () => {
      render(<ModelsPage />);
      
      await waitFor(() => {
        expect(screen.getByText('tiny')).toBeInTheDocument();
      });
      
      // Switch to Whisper models tab
      const whisperTab = screen.getByRole('tab', { name: /whisper models/i });
      await userEvent.click(whisperTab);
      
      await waitFor(() => {
        expect(screen.getByText('base')).toBeInTheDocument();
        expect(screen.getByText('large')).toBeInTheDocument();
      });
      
      // Switch to Ollama models tab
      const ollamaTab = screen.getByRole('tab', { name: /ollama models/i });
      await userEvent.click(ollamaTab);
      
      await waitFor(() => {
        expect(screen.getByText('mistral')).toBeInTheDocument();
      });
    });
    
    it('should maintain search term across tabs', async () => {
      render(<ModelsPage />);
      
      await waitFor(() => {
        expect(screen.getByPlaceholderText('Search models...')).toBeInTheDocument();
      });
      
      const searchInput = screen.getByPlaceholderText('Search models...');
      await userEvent.type(searchInput, 'llama');
      
      // Switch tabs
      const whisperTab = screen.getByRole('tab', { name: /whisper models/i });
      await userEvent.click(whisperTab);
      
      // Search term should be maintained
      expect(searchInput).toHaveValue('llama');
    });
  });

  describe('Search Functionality', () => {
    it('should filter installed models by search term', async () => {
      render(<ModelsPage />);
      
      await waitFor(() => {
        expect(screen.getByText('tiny')).toBeInTheDocument();
        expect(screen.getByText('llama3')).toBeInTheDocument();
      });
      
      const searchInput = screen.getByPlaceholderText('Search models...');
      await userEvent.type(searchInput, 'tiny');
      
      // Should only show models matching search
      expect(screen.getByText('tiny')).toBeInTheDocument();
      expect(screen.queryByText('llama3')).not.toBeInTheDocument();
    });
    
    it('should filter available models by search term', async () => {
      render(<ModelsPage />);
      
      // Switch to Whisper tab
      const whisperTab = screen.getByRole('tab', { name: /whisper models/i });
      await userEvent.click(whisperTab);
      
      await waitFor(() => {
        expect(screen.getByText('base')).toBeInTheDocument();
        expect(screen.getByText('large')).toBeInTheDocument();
      });
      
      const searchInput = screen.getByPlaceholderText('Search models...');
      await userEvent.type(searchInput, 'large');
      
      // Should only show models matching search
      expect(screen.getByText('large')).toBeInTheDocument();
      expect(screen.queryByText('base')).not.toBeInTheDocument();
    });
    
    it('should show no results message when search yields no matches', async () => {
      render(<ModelsPage />);
      
      await waitFor(() => {
        expect(screen.getByText('tiny')).toBeInTheDocument();
      });
      
      const searchInput = screen.getByPlaceholderText('Search models...');
      await userEvent.type(searchInput, 'nonexistent');
      
      expect(screen.getByText('No installed models found.')).toBeInTheDocument();
    });
  });

  describe('Model Installation', () => {
    it('should install a model successfully', async () => {
      mockModelService.installModel.mockResolvedValue({ task_id: 'task-123' });
      
      const mockProgressCallback = vi.fn();
      mockModelService.subscribeToInstallProgress.mockImplementation((taskId, onProgress) => {
        // Simulate progress updates
        setTimeout(() => {
          onProgress({ status: 'downloading', progress: 50 });
        }, 100);
        
        setTimeout(() => {
          onProgress({ status: 'complete', progress: 100 });
        }, 200);
        
        return () => {};
      });
      
      render(<ModelsPage />);
      
      // Switch to Whisper tab
      const whisperTab = screen.getByRole('tab', { name: /whisper models/i });
      await userEvent.click(whisperTab);
      
      await waitFor(() => {
        expect(screen.getByText('base')).toBeInTheDocument();
      });
      
      // Click install button
      const installButtons = screen.getAllByText('Install');
      await userEvent.click(installButtons[0]);
      
      expect(mockModelService.installModel).toHaveBeenCalledWith('WHISPER', 'base', '1.0');
      
      await waitFor(() => {
        expect(mockAddToast).toHaveBeenCalledWith({
          title: 'Installation Started',
          description: 'Installing base...',
          variant: 'info',
        });
      });
    });
    
    it('should handle installation errors', async () => {
      mockModelService.installModel.mockRejectedValue(new Error('Installation failed'));
      
      render(<ModelsPage />);
      
      // Switch to Whisper tab
      const whisperTab = screen.getByRole('tab', { name: /whisper models/i });
      await userEvent.click(whisperTab);
      
      await waitFor(() => {
        expect(screen.getByText('base')).toBeInTheDocument();
      });
      
      // Click install button
      const installButtons = screen.getAllByText('Install');
      await userEvent.click(installButtons[0]);
      
      await waitFor(() => {
        expect(mockAddToast).toHaveBeenCalledWith({
          title: 'Error',
          description: 'Installation failed',
          variant: 'error',
        });
      });
    });
    
    it('should show installation progress', async () => {
      mockModelService.installModel.mockResolvedValue({ task_id: 'task-123' });
      
      mockModelService.subscribeToInstallProgress.mockImplementation((taskId, onProgress) => {
        setTimeout(() => {
          onProgress({
            status: 'downloading',
            progress: 75,
            downloaded_bytes: 750000000,
            total_bytes: **********,
            current_step: 'Downloading model weights',
          });
        }, 100);
        
        return () => {};
      });
      
      render(<ModelsPage />);
      
      // Switch to Whisper tab and install a model
      const whisperTab = screen.getByRole('tab', { name: /whisper models/i });
      await userEvent.click(whisperTab);
      
      await waitFor(() => {
        expect(screen.getByText('base')).toBeInTheDocument();
      });
      
      const installButtons = screen.getAllByText('Install');
      await userEvent.click(installButtons[0]);
      
      // Switch back to installed tab to see progress
      const installedTab = screen.getByRole('tab', { name: /installed models/i });
      await userEvent.click(installedTab);
      
      await waitFor(() => {
        expect(screen.getByText('75%')).toBeInTheDocument();
        expect(screen.getByText('Downloading model weights')).toBeInTheDocument();
      });
    });
    
    it('should allow canceling installation', async () => {
      mockModelService.installModel.mockResolvedValue({ task_id: 'task-123' });
      mockModelService.cancelInstall.mockResolvedValue(undefined);
      
      let progressCallback: ((progress: InstallProgress) => void) | null = null;
      
      mockModelService.subscribeToInstallProgress.mockImplementation((taskId, onProgress) => {
        progressCallback = onProgress;
        
        setTimeout(() => {
          onProgress({
            status: 'downloading',
            progress: 25,
            current_step: 'Downloading...',
          });
        }, 100);
        
        return () => {};
      });
      
      render(<ModelsPage />);
      
      // Install a model
      const whisperTab = screen.getByRole('tab', { name: /whisper models/i });
      await userEvent.click(whisperTab);
      
      await waitFor(() => {
        expect(screen.getByText('base')).toBeInTheDocument();
      });
      
      const installButtons = screen.getAllByText('Install');
      await userEvent.click(installButtons[0]);
      
      // Switch to installed tab to see progress card
      const installedTab = screen.getByRole('tab', { name: /installed models/i });
      await userEvent.click(installedTab);
      
      await waitFor(() => {
        expect(screen.getByText('Cancel Installation')).toBeInTheDocument();
      });
      
      // Cancel installation
      const cancelButton = screen.getByText('Cancel Installation');
      await userEvent.click(cancelButton);
      
      expect(mockModelService.cancelInstall).toHaveBeenCalledWith('task-123');
    });
  });

  describe('Model Configuration', () => {
    it('should open configuration modal', async () => {
      render(<ModelsPage />);
      
      await waitFor(() => {
        expect(screen.getByText('tiny')).toBeInTheDocument();
      });
      
      // Click configure button
      const configureButtons = screen.getAllByText('Configure');
      await userEvent.click(configureButtons[0]);
      
      await waitFor(() => {
        expect(screen.getByText('Configure Model')).toBeInTheDocument();
        expect(screen.getByText('tiny (WHISPER) v1.0')).toBeInTheDocument();
      });
    });
    
    it('should validate configuration JSON', async () => {
      render(<ModelsPage />);
      
      await waitFor(() => {
        expect(screen.getByText('tiny')).toBeInTheDocument();
      });
      
      // Open configuration modal
      const configureButtons = screen.getAllByText('Configure');
      await userEvent.click(configureButtons[0]);
      
      await waitFor(() => {
        expect(screen.getByRole('textbox')).toBeInTheDocument();
      });
      
      // Enter invalid JSON
      const configTextarea = screen.getByRole('textbox');
      await userEvent.clear(configTextarea);
      await userEvent.type(configTextarea, '{"invalid": json}');
      
      await waitFor(() => {
        expect(screen.getByText('Invalid JSON format')).toBeInTheDocument();
      });
    });
    
    it('should save valid configuration', async () => {
      mockModelService.configureModel.mockResolvedValue(undefined);
      
      render(<ModelsPage />);
      
      await waitFor(() => {
        expect(screen.getByText('tiny')).toBeInTheDocument();
      });
      
      // Open configuration modal
      const configureButtons = screen.getAllByText('Configure');
      await userEvent.click(configureButtons[0]);
      
      await waitFor(() => {
        expect(screen.getByRole('textbox')).toBeInTheDocument();
      });
      
      // Enter valid configuration
      const configTextarea = screen.getByRole('textbox');
      await userEvent.clear(configTextarea);
      await userEvent.type(configTextarea, '{"temperature": 0.7, "language": "en"}');
      
      // Save configuration
      const saveButton = screen.getByText('Save Configuration');
      await userEvent.click(saveButton);
      
      expect(mockModelService.configureModel).toHaveBeenCalledWith(
        'WHISPER',
        'tiny',
        { temperature: 0.7, language: 'en' }
      );
      
      await waitFor(() => {
        expect(mockAddToast).toHaveBeenCalledWith({
          title: 'Success',
          description: 'Model configured successfully',
          variant: 'success',
        });
      });
    });
  });

  describe('Model Deletion', () => {
    it('should delete a model with confirmation', async () => {
      mockModelService.deleteModel.mockResolvedValue(undefined);
      
      // Mock window.confirm
      const originalConfirm = window.confirm;
      window.confirm = vi.fn().mockReturnValue(true);
      
      render(<ModelsPage />);
      
      await waitFor(() => {
        expect(screen.getByText('tiny')).toBeInTheDocument();
      });
      
      // Click delete button (trash icon)
      const deleteButtons = screen.getAllByTitle('Delete Model');
      await userEvent.click(deleteButtons[0]);
      
      expect(window.confirm).toHaveBeenCalledWith(
        'Are you sure you want to delete model "tiny"? This action cannot be undone.'
      );
      
      expect(mockModelService.deleteModel).toHaveBeenCalledWith('WHISPER', 'tiny');
      
      await waitFor(() => {
        expect(mockAddToast).toHaveBeenCalledWith({
          title: 'Success',
          description: 'Model tiny deleted successfully',
          variant: 'success',
        });
      });
      
      // Restore original confirm
      window.confirm = originalConfirm;
    });
    
    it('should not delete when user cancels confirmation', async () => {
      // Mock window.confirm to return false
      const originalConfirm = window.confirm;
      window.confirm = vi.fn().mockReturnValue(false);
      
      render(<ModelsPage />);
      
      await waitFor(() => {
        expect(screen.getByText('tiny')).toBeInTheDocument();
      });
      
      // Click delete button
      const deleteButtons = screen.getAllByTitle('Delete Model');
      await userEvent.click(deleteButtons[0]);
      
      expect(window.confirm).toHaveBeenCalled();
      expect(mockModelService.deleteModel).not.toHaveBeenCalled();
      
      // Restore original confirm
      window.confirm = originalConfirm;
    });
    
    it('should handle deletion errors', async () => {
      mockModelService.deleteModel.mockRejectedValue(new Error('Delete failed'));
      
      const originalConfirm = window.confirm;
      window.confirm = vi.fn().mockReturnValue(true);
      
      render(<ModelsPage />);
      
      await waitFor(() => {
        expect(screen.getByText('tiny')).toBeInTheDocument();
      });
      
      const deleteButtons = screen.getAllByTitle('Delete Model');
      await userEvent.click(deleteButtons[0]);
      
      await waitFor(() => {
        expect(mockAddToast).toHaveBeenCalledWith({
          title: 'Error',
          description: 'Delete failed',
          variant: 'error',
        });
      });
      
      window.confirm = originalConfirm;
    });
  });

  describe('Health Monitoring', () => {
    it('should perform batch health checks on load', async () => {
      render(<ModelsPage />);
      
      await waitFor(() => {
        expect(screen.getByText('tiny')).toBeInTheDocument();
      });
      
      // Should call health check for all installed models
      await waitFor(() => {
        expect(mockModelService.checkModelHealth).toHaveBeenCalledWith('WHISPER', 'tiny');
        expect(mockModelService.checkModelHealth).toHaveBeenCalledWith('OLLAMA', 'llama3');
      });
    });
    
    it('should run manual health check', async () => {
      mockModelService.runHealthCheck.mockResolvedValue(sampleHealthResult);
      
      render(<ModelsPage />);
      
      await waitFor(() => {
        expect(screen.getByText('tiny')).toBeInTheDocument();
      });
      
      // Click health check button (activity icon)
      const healthButtons = screen.getAllByTitle('Run Health Check');
      await userEvent.click(healthButtons[0]);
      
      expect(mockModelService.runHealthCheck).toHaveBeenCalledWith('WHISPER', 'tiny');
      
      await waitFor(() => {
        expect(mockAddToast).toHaveBeenCalledWith({
          title: 'Health Check Complete',
          description: 'Model is healthy and responding',
          variant: 'success',
        });
      });
    });
    
    it('should handle unhealthy models', async () => {
      const unhealthyResult: HealthCheckResult = {
        status: 'unhealthy',
        details: {
          model_loaded: false,
          error_count: 5,
        },
        timestamp: '2024-01-01T12:00:00Z',
      };
      
      mockModelService.runHealthCheck.mockResolvedValue(unhealthyResult);
      
      render(<ModelsPage />);
      
      await waitFor(() => {
        expect(screen.getByText('tiny')).toBeInTheDocument();
      });
      
      const healthButtons = screen.getAllByTitle('Run Health Check');
      await userEvent.click(healthButtons[0]);
      
      await waitFor(() => {
        expect(mockAddToast).toHaveBeenCalledWith({
          title: 'Health Check Complete',
          description: expect.stringContaining('failed'),
          variant: 'warning',
        });
      });
    });
  });

  describe('Log Viewing', () => {
    it('should open logs in new window', async () => {
      const mockLogs = [
        {
          timestamp: '2024-01-01T12:00:00Z',
          level: 'INFO',
          message: 'Model loaded successfully',
        },
        {
          timestamp: '2024-01-01T12:01:00Z',
          level: 'DEBUG',
          message: 'Processing request',
        },
      ];
      
      mockModelService.getModelLogs.mockResolvedValue(mockLogs);
      
      // Mock window.open
      const mockWindow = {
        document: {
          write: vi.fn(),
          close: vi.fn(),
        },
      };
      const originalOpen = window.open;
      window.open = vi.fn().mockReturnValue(mockWindow);
      
      render(<ModelsPage />);
      
      await waitFor(() => {
        expect(screen.getByText('tiny')).toBeInTheDocument();
      });
      
      // Click view logs button (eye icon)
      const logButtons = screen.getAllByTitle('View Logs');
      await userEvent.click(logButtons[0]);
      
      expect(mockModelService.getModelLogs).toHaveBeenCalledWith('WHISPER', 'tiny');
      
      await waitFor(() => {
        expect(window.open).toHaveBeenCalledWith('', '_blank', 'width=800,height=600');
        expect(mockWindow.document.write).toHaveBeenCalled();
      });
      
      // Restore window.open
      window.open = originalOpen;
    });
    
    it('should handle log viewing errors', async () => {
      mockModelService.getModelLogs.mockRejectedValue(new Error('Log fetch failed'));
      
      render(<ModelsPage />);
      
      await waitFor(() => {
        expect(screen.getByText('tiny')).toBeInTheDocument();
      });
      
      const logButtons = screen.getAllByTitle('View Logs');
      await userEvent.click(logButtons[0]);
      
      await waitFor(() => {
        expect(mockAddToast).toHaveBeenCalledWith({
          title: 'Error',
          description: 'Failed to fetch model logs',
          variant: 'error',
        });
      });
    });
  });

  describe('Error Boundaries', () => {
    it('should display error boundary when component crashes', () => {
      // Mock console.error to prevent error output in tests
      const originalError = console.error;
      console.error = vi.fn();
      
      // Force an error by mocking a component that throws
      const ThrowError = () => {
        throw new Error('Component crashed');
      };
      
      // This would normally be tested with a separate error boundary test,
      // but we can verify the fallback UI exists
      render(<ModelsPage />);
      
      // The error boundary fallback should be rendered in case of errors
      // We can't easily trigger this in tests, but we can verify structure
      expect(screen.getByText('Model Management')).toBeInTheDocument();
      
      console.error = originalError;
    });
  });

  describe('Accessibility', () => {
    it('should have proper ARIA labels and roles', async () => {
      render(<ModelsPage />);
      
      await waitFor(() => {
        expect(screen.getByText('Model Management')).toBeInTheDocument();
      });
      
      // Check for proper tab roles
      expect(screen.getByRole('tablist')).toBeInTheDocument();
      expect(screen.getAllByRole('tab')).toHaveLength(3);
      
      // Check for proper button labels
      expect(screen.getAllByTitle('Run Health Check')).toHaveLength(2);
      expect(screen.getAllByTitle('View Logs')).toHaveLength(2);
      expect(screen.getAllByTitle('Delete Model')).toHaveLength(2);
    });
    
    it('should support keyboard navigation', async () => {
      render(<ModelsPage />);
      
      await waitFor(() => {
        expect(screen.getByText('Model Management')).toBeInTheDocument();
      });
      
      // Test tab navigation
      const firstTab = screen.getByRole('tab', { name: /installed models/i });
      firstTab.focus();
      
      // Simulate arrow key navigation
      await userEvent.keyboard('{ArrowRight}');
      
      const secondTab = screen.getByRole('tab', { name: /whisper models/i });
      expect(secondTab).toHaveFocus();
    });
  });

  describe('Performance', () => {
    it('should handle large numbers of models efficiently', async () => {
      // Create a large number of models
      const manyModels = Array.from({ length: 100 }, (_, i) => ({
        id: {
          provider: 'OLLAMA',
          name: `model-${i}`,
          version: '1.0',
        },
        status: 'installed' as const,
        metadata: {
          size_on_disk_bytes: 1000000,
          installed_at: '2024-01-01T12:00:00Z',
          source: 'test',
          extra: {},
        },
      }));
      
      mockModelService.getInstalledModels.mockResolvedValue({
        items: manyModels,
        total: 100,
        page: 1,
        page_size: 50,
      });
      
      const startTime = performance.now();
      
      render(<ModelsPage />);
      
      await waitFor(() => {
        expect(screen.getByText('model-0')).toBeInTheDocument();
      });
      
      const endTime = performance.now();
      const renderTime = endTime - startTime;
      
      // Should render within reasonable time (adjust threshold as needed)
      expect(renderTime).toBeLessThan(5000); // 5 seconds
    });
    
    it('should debounce search input', async () => {
      render(<ModelsPage />);
      
      await waitFor(() => {
        expect(screen.getByPlaceholderText('Search models...')).toBeInTheDocument();
      });
      
      const searchInput = screen.getByPlaceholderText('Search models...');
      
      // Type quickly
      await userEvent.type(searchInput, 'search term', { delay: 10 });
      
      // Should not trigger search for every character
      // This is more of an implementation detail test
      expect(searchInput).toHaveValue('search term');
    });
  });
});
