# Project Scanner Module

A comprehensive project scanning engine for detecting various types of issues in codebases, built following the existing LONI project patterns with FastAPI and Pydantic.

## Overview

The Project Scanner is designed to analyze codebases and identify issues across multiple categories:

- **Code Quality**: Complexity, duplicates, style violations, naming conventions
- **Security**: Hard-coded secrets, SQL injection, XSS vulnerabilities, insecure functions
- **Dependencies**: Outdated packages, known vulnerabilities, license issues
- **Structure**: Circular imports, dead code, file organization problems
- **Performance**: Large files, inefficient patterns, memory leaks

## Architecture

The scanner follows a modular architecture with clear separation of concerns:

```
app/scanner/
├── __init__.py           # Module exports
├── models.py             # Pydantic data models
├── scanner.py            # Core scanner engine
├── api.py               # FastAPI endpoints
├── storage.py           # Result persistence
├── detectors/           # Issue detection algorithms
│   ├── __init__.py
│   ├── base.py          # Base detector class
│   ├── code_quality.py  # Code quality detection
│   ├── security.py      # Security vulnerability detection
│   ├── dependency.py    # Dependency issue detection
│   ├── structure.py     # Structural problem detection
│   └── performance.py   # Performance issue detection
└── README.md           # This file
```

## Features

### Core Scanning Engine

- **File Discovery**: Intelligent file traversal with include/exclude patterns
- **Parallel Processing**: Configurable parallel workers for faster scanning
- **Progress Tracking**: Real-time scan progress monitoring
- **Error Handling**: Comprehensive error recovery and reporting
- **Extensible**: Easy to add new detectors and issue types

### Issue Detection

#### Code Quality Detector
- Function/class length analysis
- Cyclomatic complexity calculation
- Duplicate code detection
- Naming convention validation
- Dead code identification
- TODO/FIXME comment tracking

#### Security Detector
- Hard-coded secrets (API keys, passwords, tokens)
- SQL injection vulnerability patterns
- XSS vulnerability detection
- Insecure function usage
- Weak cryptographic practices
- Path traversal vulnerabilities
- Insecure HTTP usage

#### Dependency Detector
- Package vulnerability scanning
- Outdated dependency detection
- License compatibility checking
- Unpinned version identification
- Deprecated package warnings

#### Structure Detector
- Circular import detection
- File organization analysis
- Naming convention enforcement
- Mixed concern identification
- Import organization validation

#### Performance Detector
- Large file identification
- Inefficient code patterns
- Memory leak indicators
- Expensive operation detection
- Nested loop analysis

### API Endpoints

The scanner provides a RESTful API with the following endpoints:

- `POST /api/v1/scanner/scan` - Start a new scan
- `GET /api/v1/scanner/scan/{scan_id}` - Get scan results
- `GET /api/v1/scanner/scan/{scan_id}/progress` - Get scan progress
- `DELETE /api/v1/scanner/scan/{scan_id}` - Cancel a running scan
- `GET /api/v1/scanner/scans` - List user scans
- `GET /api/v1/scanner/health` - Health check
- `GET /api/v1/scanner/stats` - Scanner statistics

### Data Models

All data structures use Pydantic for validation and serialization:

- `ScanRequest`: Scan configuration and target specification
- `ScanResponse`: Complete scan results with issues and metadata
- `Issue`: Individual issue with severity, location, and suggestions
- `ScanProgress`: Real-time progress tracking
- `ScanSummary`: Aggregated statistics and metrics

## Usage

### Basic Scanning

```python
from app.scanner import ProjectScanner
from app.scanner.models import ScanRequest, ScanTarget, ScanConfig, IssueType

# Create scanner instance
scanner = ProjectScanner()

# Configure scan request
request = ScanRequest(
    target=ScanTarget(
        project_path="/path/to/project",
        include_patterns=["**/*.py", "**/*.js"],
        exclude_patterns=["**/node_modules/**", "**/venv/**"]
    ),
    config=ScanConfig(
        scan_types=[IssueType.SECURITY, IssueType.CODE_QUALITY],
        severity_threshold=IssueSeverity.MEDIUM,
        parallel_workers=4
    ),
    user_id="user123",
    request_id="scan_001"
)

# Perform scan
result = await scanner.scan_project(request)

# Access results
print(f"Found {len(result.issues)} issues")
for issue in result.issues:
    print(f"{issue.severity}: {issue.title} in {issue.file_path}")
```

### API Usage

```bash
# Start a scan
curl -X POST "http://localhost:8000/api/v1/scanner/scan" \
  -H "Authorization: Bearer your-api-key" \
  -H "Content-Type: application/json" \
  -d '{
    "target": {
      "project_path": "/path/to/project",
      "include_patterns": ["**/*.py"],
      "exclude_patterns": ["**/venv/**"]
    },
    "config": {
      "scan_types": ["code_quality", "security"]
    },
    "user_id": "user123",
    "request_id": "scan_001"
  }'

# Check scan progress
curl -H "Authorization: Bearer your-api-key" \
  "http://localhost:8000/api/v1/scanner/scan/{scan_id}/progress"

# Get scan results
curl -H "Authorization: Bearer your-api-key" \
  "http://localhost:8000/api/v1/scanner/scan/{scan_id}"
```

## Configuration

### Scan Configuration

```python
ScanConfig(
    scan_types=[IssueType.CODE_QUALITY, IssueType.SECURITY],
    severity_threshold=IssueSeverity.LOW,
    max_issues_per_file=50,
    parallel_workers=4,
    timeout_seconds=300
)
```

### Target Configuration

```python
ScanTarget(
    project_path="/path/to/project",
    include_patterns=["**/*.py", "**/*.js", "**/*.ts"],
    exclude_patterns=[
        "**/node_modules/**",
        "**/venv/**", 
        "**/.git/**",
        "**/__pycache__/**"
    ],
    max_file_size=10_000_000  # 10MB
)
```

## Storage

Scan results are persisted using SQLite (configurable to other databases):

- **Scans Table**: Scan metadata and configuration
- **Issues Table**: Individual issues with full details
- **Analytics Table**: Performance metrics and statistics
- **User Preferences**: User-specific settings

## Testing

Comprehensive test suite covering:

- Unit tests for each detector
- Integration tests for the complete workflow
- API endpoint testing
- Storage functionality validation
- Error handling scenarios

Run tests with:

```bash
pytest tests/test_scanner.py -v
```

## Performance

The scanner is optimized for performance:

- **Parallel Processing**: Multiple workers scan files concurrently
- **Smart File Discovery**: Efficient pattern matching and filtering
- **Incremental Analysis**: Only analyze changed files (future feature)
- **Memory Efficient**: Streaming file processing for large codebases
- **Caching**: Results caching for repeated scans (future feature)

## Extensibility

### Adding New Detectors

1. Create a new detector class inheriting from `BaseDetector`:

```python
from .base import BaseDetector
from ..models import Issue, IssueType, IssueSeverity

class CustomDetector(BaseDetector):
    def __init__(self):
        super().__init__(IssueType.CUSTOM)
    
    def detect_issues(self, file_path: str, content: str) -> List[Issue]:
        issues = []
        # Your detection logic here
        return issues
```

2. Register the detector in the scanner:

```python
self.detectors[IssueType.CUSTOM] = CustomDetector()
```

### Adding New Issue Types

1. Extend the `IssueType` enum in `models.py`
2. Create corresponding detector class
3. Update configuration options

## Integration

The scanner integrates seamlessly with the existing LONI project:

- **FastAPI**: Uses the same patterns as other API modules
- **Pydantic**: Consistent data validation and serialization
- **Logging**: Structured logging following project conventions
- **Error Handling**: Comprehensive error handling and recovery
- **Configuration**: Uses the same settings system

## Security

Security considerations:

- **API Authentication**: Bearer token authentication for all endpoints
- **Input Validation**: Comprehensive input validation using Pydantic
- **Path Traversal Protection**: Safe file path handling
- **Resource Limits**: Configurable limits on file sizes and scan duration
- **Error Information**: Sensitive information not exposed in error messages

## Monitoring

Built-in monitoring and observability:

- **Health Checks**: Service health monitoring
- **Metrics**: Performance and usage statistics
- **Logging**: Structured logging for debugging and analysis
- **Progress Tracking**: Real-time scan progress updates

## Future Enhancements

Planned improvements:

- **Incremental Scanning**: Only scan changed files
- **Custom Rules**: User-defined detection rules
- **Plugin System**: Third-party detector plugins
- **Advanced Analytics**: Trend analysis and reporting
- **Integration**: Git hooks and CI/CD integration
- **Machine Learning**: AI-powered issue detection
- **Distributed Scanning**: Multi-node scanning for large projects

## Contributing

When contributing to the scanner module:

1. Follow existing code patterns and conventions
2. Add comprehensive tests for new features
3. Update documentation for API changes
4. Ensure backward compatibility
5. Add appropriate logging and error handling

## License

This module is part of the LONI project and follows the same MIT license.