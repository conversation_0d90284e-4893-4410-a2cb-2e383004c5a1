#!/usr/bin/env python3
import os
import re
from collections import defaultdict, deque

def build_import_graph():
    """Build a graph of imports between modules."""
    graph = defaultdict(set)
    reverse_graph = defaultdict(set)

    for root, dirs, files in os.walk("apps/backend/app"):
        for file in files:
            if file.endswith(".py"):
                filepath = os.path.join(root, file)
                module_name = filepath.replace("\\", ".").replace("/", ".").replace("apps.backend.app.", "").replace(".py", "")

                try:
                    with open(filepath, 'r', encoding='utf-8') as f:
                        content = f.read()

                    # Find import statements
                    imports = re.findall(r'^from\s+([^\s]+)\s+import', content, re.MULTILINE)
                    imports.extend(re.findall(r'^import\s+([^\s]+)', content, re.MULTILINE))

                    for imp in imports:
                        # Clean up import path
                        imp = imp.split('.')[0]  # Get the root module
                        if imp.startswith('app.'):
                            graph[module_name].add(imp)
                            reverse_graph[imp].add(module_name)

                except Exception as e:
                    continue

    return graph, reverse_graph

def detect_cycles(graph, reverse_graph):
    """Detect circular dependencies using DFS."""
    visited = set()
    rec_stack = set()
    cycles = []

    def dfs(node, path):
        visited.add(node)
        rec_stack.add(node)
        path.append(node)

        for neighbor in graph[node]:
            if neighbor not in visited:
                if dfs(neighbor, path):
                    return True
            elif neighbor in rec_stack:
                # Found a cycle
                cycle_start = path.index(neighbor)
                cycle = path[cycle_start:] + [neighbor]
                cycles.append(cycle)
                return True

        path.pop()
        rec_stack.remove(node)
        return False

    for node in graph:
        if node not in visited:
            dfs(node, [])

    return cycles

if __name__ == "__main__":
    graph, reverse_graph = build_import_graph()
    cycles = detect_cycles(graph, reverse_graph)

    if cycles:
        print("Circular dependencies detected:")
        for cycle in cycles:
            print(" -> ".join(cycle))
    else:
        print("No circular dependencies detected.")
