'use client';

import { Card } from '@/components/common/Card';
import { Button } from '@/components/common/Button';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Pause, Play, Square, Clock, FileText } from 'lucide-react';
import { motion } from 'framer-motion';
import type { ScanProgress } from '@/lib/types';

interface ScanProgressCardProps {
  progress: ScanProgress;
  onPause?: () => void;
  onResume?: () => void;
  onCancel?: () => void;
  className?: string;
}

export function ScanProgressCard({ 
  progress, 
  onPause, 
  onResume, 
  onCancel,
  className 
}: ScanProgressCardProps) {
  const isActive = progress.status === 'scanning' || progress.status === 'analyzing';
  const isPaused = progress.status === 'pending';
  
  const formatTimeRemaining = (seconds: number) => {
    if (seconds < 60) return `${seconds}s`;
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}m ${remainingSeconds}s`;
  };

  const getStatusColor = (status: ScanProgress['status']) => {
    switch (status) {
      case 'scanning':
      case 'analyzing':
        return 'default';
      case 'pending':
        return 'secondary';
      case 'completed':
        return 'secondary';
      case 'failed':
        return 'destructive';
      case 'cancelled':
        return 'outline';
      default:
        return 'outline';
    }
  };

  return (
    <Card className={`p-6 ${className}`}>
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-3">
          <div className="relative">
            {isActive && (
              <motion.div
                className="absolute inset-0 bg-primary/20 rounded-full"
                animate={{ scale: [1, 1.2, 1] }}
                transition={{ repeat: Infinity, duration: 2 }}
              />
            )}
            <div className="relative bg-primary/10 p-2 rounded-full">
              <FileText className="h-5 w-5 text-primary" />
            </div>
          </div>
          
          <div>
            <h3 className="text-lg font-semibold">Scan in Progress</h3>
            <p className="text-sm text-muted-foreground">{progress.currentPhase}</p>
          </div>
        </div>

        <div className="flex items-center gap-2">
          <Badge variant={getStatusColor(progress.status)}>
            {progress.status.charAt(0).toUpperCase() + progress.status.slice(1)}
          </Badge>
          
          <div className="flex gap-1">
            {isActive && onPause && (
              <Button variant="outline" size="sm" onClick={onPause}>
                <Pause className="h-4 w-4" />
              </Button>
            )}
            
            {isPaused && onResume && (
              <Button variant="outline" size="sm" onClick={onResume}>
                <Play className="h-4 w-4" />
              </Button>
            )}
            
            {onCancel && (
              <Button variant="outline" size="sm" onClick={onCancel}>
                <Square className="h-4 w-4" />
              </Button>
            )}
          </div>
        </div>
      </div>
      
      <div className="space-y-4">
        <div>
          <div className="flex justify-between items-center mb-2">
            <span className="text-sm text-muted-foreground">Progress</span>
            <span className="text-sm font-medium">{progress.progress}%</span>
          </div>
          <Progress value={progress.progress} className="w-full" />
        </div>
        
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
          <div>
            <p className="text-muted-foreground">Files Scanned</p>
            <p className="font-medium">
              {progress.filesScanned.toLocaleString()} / {progress.totalFiles.toLocaleString()}
            </p>
          </div>
          
          <div>
            <p className="text-muted-foreground">Elapsed Time</p>
            <p className="font-medium">
              {Math.round((Date.now() - progress.startedAt.getTime()) / 1000 / 60)}m
            </p>
          </div>
          
          {progress.estimatedTimeRemaining && (
            <div>
              <p className="text-muted-foreground">Time Remaining</p>
              <p className="font-medium flex items-center gap-1">
                <Clock className="h-3 w-3" />
                {formatTimeRemaining(progress.estimatedTimeRemaining)}
              </p>
            </div>
          )}
          
          <div>
            <p className="text-muted-foreground">Started</p>
            <p className="font-medium">
              {progress.startedAt.toLocaleTimeString()}
            </p>
          </div>
        </div>
        
        {isActive && (
          <div className="pt-2 border-t">
            <div className="flex items-center gap-2 text-xs text-muted-foreground">
              <motion.div
                className="w-2 h-2 bg-primary rounded-full"
                animate={{ opacity: [1, 0.3, 1] }}
                transition={{ repeat: Infinity, duration: 1.5 }}
              />
              <span>Scanning files and analyzing code patterns...</span>
            </div>
          </div>
        )}
      </div>
    </Card>
  );
}