import { AppLayout } from "@/components/layout/AppLayout";
import { Card } from "@/components/common/Card";
import { Button } from "@/components/common/Button";
import { useApi } from "@/lib/hooks/useApi";
import { useEffect, useState } from "react";
import { toast } from "sonner";
import {
  Bot,
  Download,
  Play,
  Trash2,
  Plus,
  Search,
  Filter,
  CheckCircle,
  AlertCircle,
  Clock
} from "lucide-react";

interface ModelInfo {
  name: string;
  size: string;
  modified_at: string;
  status: 'available' | 'downloading' | 'installed' | 'error';
  description?: string;
}

export default function ModelsPage() {
  const [models, setModels] = useState<ModelInfo[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [filter, setFilter] = useState<'all' | 'installed' | 'available'>('all');

  const { execute: listModels, isLoading: isLoadingModels } = useApi();
  const { execute: pullModel, isLoading: isPulling } = useApi();

  useEffect(() => {
    loadModels();
  }, []);

  const loadModels = async () => {
    const response = await listModels('/api/v1/models', 'GET');
    if (response.success && response.data?.models) {
      const modelData = response.data.models.map((name: string) => ({
        name,
        size: 'Unknown',
        modified_at: 'Unknown',
        status: 'installed' as const,
        description: `Ollama model: ${name}`
      }));
      setModels(modelData);
    }
  };

  const handlePullModel = async (modelName: string) => {
    const response = await pullModel('/api/v1/models/pull', 'POST', { model_name: modelName });
    if (response.success) {
      toast.success(`Successfully pulled model: ${modelName}`);
      loadModels(); // Refresh the list
    } else {
      toast.error(`Failed to pull model: ${modelName}`);
    }
  };

  const filteredModels = models.filter(model => {
    const matchesSearch = model.name.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesFilter = filter === 'all' ||
      (filter === 'installed' && model.status === 'installed') ||
      (filter === 'available' && model.status === 'available');
    return matchesSearch && matchesFilter;
  });

  const getStatusIcon = (status: ModelInfo['status']) => {
    switch (status) {
      case 'installed':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'downloading':
        return <Clock className="h-4 w-4 text-blue-600" />;
      case 'error':
        return <AlertCircle className="h-4 w-4 text-red-600" />;
      default:
        return <Download className="h-4 w-4 text-gray-400" />;
    }
  };

  const getStatusText = (status: ModelInfo['status']) => {
    switch (status) {
      case 'installed':
        return 'Installed';
      case 'downloading':
        return 'Downloading';
      case 'error':
        return 'Error';
      default:
        return 'Available';
    }
  };

  return (
    <AppLayout>
      <div className="space-y-8">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold">Model Management</h1>
            <p className="text-muted-foreground">Manage and interact with Ollama models</p>
          </div>
          <Button gradient>
            <Plus className="h-4 w-4 mr-2" />
            Add Model
          </Button>
        </div>

        {/* Search and Filter */}
        <Card className="p-6">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <input
                  type="text"
                  placeholder="Search models..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-input bg-background rounded-md focus:outline-none focus:ring-2 focus:ring-ring"
                />
              </div>
            </div>
            <div className="flex gap-2">
              <Button
                variant={filter === 'all' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setFilter('all')}
              >
                All
              </Button>
              <Button
                variant={filter === 'installed' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setFilter('installed')}
              >
                Installed
              </Button>
              <Button
                variant={filter === 'available' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setFilter('available')}
              >
                Available
              </Button>
            </div>
          </div>
        </Card>

        {/* Models Grid */}
        {isLoadingModels ? (
          <div className="text-center py-8">
            <div className="animate-spin h-8 w-8 border-4 border-primary border-t-transparent rounded-full mx-auto mb-4"></div>
            <p className="text-muted-foreground">Loading models...</p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredModels.map((model) => (
              <Card key={model.name} className="p-6 hover:shadow-lg transition-shadow">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-primary/10 rounded-lg">
                      <Bot className="h-6 w-6 text-primary" />
                    </div>
                    <div>
                      <h3 className="font-semibold">{model.name}</h3>
                      <p className="text-sm text-muted-foreground">{model.size}</p>
                    </div>
                  </div>
                  {getStatusIcon(model.status)}
                </div>

                <p className="text-sm text-muted-foreground mb-4 line-clamp-2">
                  {model.description}
                </p>

                <div className="flex items-center justify-between text-xs text-muted-foreground mb-4">
                  <span>{getStatusText(model.status)}</span>
                  <span>{model.modified_at}</span>
                </div>

                <div className="flex gap-2">
                  {model.status === 'available' && (
                    <Button
                      size="sm"
                      onClick={() => handlePullModel(model.name)}
                      disabled={isPulling}
                      className="flex-1"
                    >
                      <Download className="h-3 w-3 mr-1" />
                      Pull
                    </Button>
                  )}
                  {model.status === 'installed' && (
                    <>
                      <Button size="sm" variant="outline" className="flex-1">
                        <Play className="h-3 w-3 mr-1" />
                        Use
                      </Button>
                      <Button size="sm" variant="outline">
                        <Trash2 className="h-3 w-3" />
                      </Button>
                    </>
                  )}
                </div>
              </Card>
            ))}
          </div>
        )}

        {filteredModels.length === 0 && !isLoadingModels && (
          <Card className="p-8 text-center">
            <Bot className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">No models found</h3>
            <p className="text-muted-foreground mb-4">
              {searchTerm
                ? "No models match your search criteria."
                : "No models are currently available."
              }
            </p>
            <Button variant="outline" onClick={() => setSearchTerm("")}>
              Clear Search
            </Button>
          </Card>
        )}
      </div>
    </AppLayout>
  );
}

