"use client";

import React, { useState, useEffect, useCallback, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/shared/ui/card';
import { But<PERSON> } from '@/shared/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/shared/ui/tabs';
import { Badge } from '@/shared/ui/badge';
import { Input } from '@/shared/ui/input';
import { Label } from '@/shared/ui/label';
import { Textarea } from '@/shared/ui/textarea';
import { LoadingSpinner } from '@/shared/ui/loading-spinner';
import { useToast } from '@/shared/ui/providers';
import ErrorBoundary from '@/shared/ui/error-boundary';
import { 
  Search, Download, Settings, Trash2, Play, Pause, RotateCcw, 
  CheckCircle, AlertCircle, XCircle, Heart, Clock, HardDrive, 
  Cpu, Zap, X, Eye, Activity, AlertTriangle, RefreshCw, 
  Monitor, Database, Wifi, WifiOff
} from 'lucide-react';
import { modelService, Model, AvailableModel, InstallProgress, HealthCheckResult } from '@/shared/services/model-service';

interface InstallTask {
  id: string;
  provider: string;
  name: string;
  version?: string;
  progress: InstallProgress;
  cleanup?: () => void;
}

interface ModelWithHealth extends Model {
  healthStatus?: HealthCheckResult;
  healthLoading?: boolean;
}

interface ConfigValidation {
  valid: boolean;
  errors?: string[];
  loading?: boolean;
}

interface ModelCardProps {
  model: ModelWithHealth;
  onConfigure: (model: Model) => void;
  onDelete: (provider: string, name: string) => void;
  onHealthCheck: (provider: string, name: string) => void;
  onViewLogs: (provider: string, name: string) => void;
  loading?: {
    configure?: boolean;
    delete?: boolean;
    health?: boolean;
  };
}

function ModelsPageContent() {
  const { addToast } = useToast();
  const [installedModels, setInstalledModels] = useState<ModelWithHealth[]>([]);
  const [availableWhisperModels, setAvailableWhisperModels] = useState<AvailableModel[]>([]);
  const [availableOllamaModels, setAvailableOllamaModels] = useState<AvailableModel[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [activeTab, setActiveTab] = useState('installed');
  const [selectedModel, setSelectedModel] = useState<Model | null>(null);
  const [config, setConfig] = useState('');
  const [configValidation, setConfigValidation] = useState<ConfigValidation>({valid: true});
  const [installTasks, setInstallTasks] = useState<Map<string, InstallTask>>(new Map());
  const [operationStates, setOperationStates] = useState<Map<string, {
    configuring?: boolean;
    deleting?: boolean;
    healthChecking?: boolean;
  }>>(new Map());
  const [retryCount, setRetryCount] = useState(0);
  const [lastError, setLastError] = useState<string | null>(null);
  const installedModelsRef = useRef<ModelWithHealth[]>([]);
  const healthCheckInterval = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    fetchModels();
    
    // Start periodic health checks for installed models
    healthCheckInterval.current = setInterval(() => {
      if (installedModelsRef.current.length > 0) {
        performBatchHealthCheck();
      }
    }, 30000); // Check every 30 seconds
    
    return () => {
      if (healthCheckInterval.current) {
        clearInterval(healthCheckInterval.current);
      }
      // Cleanup any active install streams
      installTasks.forEach(task => task.cleanup?.());
    };
  }, []);
  
  useEffect(() => {
    installedModelsRef.current = installedModels;
  }, [installedModels]);

  const fetchModels = async () => {
    try {
      setLoading(true);
      setLastError(null);
      
      const [installed, whisper, ollama] = await Promise.all([
        modelService.getInstalledModels(),
        modelService.getAvailableModels('whisper'),
        modelService.getAvailableModels('ollama')
      ]);
      
      const modelsWithHealth: ModelWithHealth[] = installed.items.map(model => ({
        ...model,
        healthStatus: undefined,
        healthLoading: false
      }));
      
      setInstalledModels(modelsWithHealth);
      setAvailableWhisperModels(whisper.items);
      setAvailableOllamaModels(ollama.items);
      setRetryCount(0);
      
      // Initial health check for all models
      setTimeout(() => performBatchHealthCheck(), 1000);
      
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Failed to fetch models";
      setLastError(errorMessage);
      
      addToast({
        title: "Error",
        description: errorMessage,
        variant: "error",
      });
      
      // Implement exponential backoff for retries
      if (retryCount < 3) {
        setTimeout(() => {
          setRetryCount(prev => prev + 1);
          fetchModels();
        }, Math.pow(2, retryCount) * 1000);
      }
    } finally {
      setLoading(false);
    }
  };
  
  const performBatchHealthCheck = async () => {
    const healthChecks = installedModelsRef.current.map(async (model) => {
      try {
        const health = await modelService.checkModelHealth(model.id.provider, model.id.name);
        return { modelKey: `${model.id.provider}-${model.id.name}`, health };
      } catch (error) {
        return { 
          modelKey: `${model.id.provider}-${model.id.name}`, 
          health: {
            status: 'unknown' as const,
            details: { model_loaded: false },
            timestamp: new Date().toISOString()
          }
        };
      }
    });
    
    const results = await Promise.allSettled(healthChecks);
    
    setInstalledModels(prevModels => 
      prevModels.map(model => {
        const modelKey = `${model.id.provider}-${model.id.name}`;
        const result = results.find(r => 
          r.status === 'fulfilled' && r.value.modelKey === modelKey
        );
        
        if (result && result.status === 'fulfilled') {
          return {
            ...model,
            healthStatus: result.value.health,
            healthLoading: false
          };
        }
        
        return model;
      })
    );
  };

  const handleInstall = async (provider: string, name: string, version?: string) => {
    try {
      const { task_id } = await modelService.installModel(provider, name, version);
      
      const installTask: InstallTask = {
        id: task_id,
        provider,
        name,
        version,
        progress: {
          status: 'pending',
          progress: 0
        }
      };
      
      // Subscribe to progress updates
      const cleanup = modelService.subscribeToInstallProgress(
        task_id,
        (progress) => {
          setInstallTasks(prev => {
            const updated = new Map(prev);
            const task = updated.get(task_id);
            if (task) {
              updated.set(task_id, { ...task, progress });
            }
            return updated;
          });
          
          if (progress.status === 'complete') {
            addToast({
              title: "Success",
              description: `Model ${name} installed successfully`,
              variant: "success",
            });
            
            setTimeout(() => {
              setInstallTasks(prev => {
                const updated = new Map(prev);
                updated.delete(task_id);
                return updated;
              });
              fetchModels();
            }, 2000);
          } else if (progress.status === 'error') {
            addToast({
              title: "Installation Failed",
              description: progress.error_message || `Failed to install model ${name}`,
              variant: "error",
            });
            
            setTimeout(() => {
              setInstallTasks(prev => {
                const updated = new Map(prev);
                updated.delete(task_id);
                return updated;
              });
            }, 5000);
          }
        },
        (error) => {
          addToast({
            title: "Installation Error",
            description: error.message,
            variant: "error",
          });
          
          setInstallTasks(prev => {
            const updated = new Map(prev);
            updated.delete(task_id);
            return updated;
          });
        }
      );
      
      installTask.cleanup = cleanup;
      
      setInstallTasks(prev => new Map(prev.set(task_id, installTask)));
      
      addToast({
        title: "Installation Started",
        description: `Installing ${name}...`,
        variant: "info",
      });
      
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Failed to start installation";
      addToast({
        title: "Error",
        description: errorMessage,
        variant: "error",
      });
    }
  };
  
  const handleCancelInstall = async (taskId: string) => {
    try {
      await modelService.cancelInstall(taskId);
      
      const task = installTasks.get(taskId);
      task?.cleanup?.();
      
      setInstallTasks(prev => {
        const updated = new Map(prev);
        updated.delete(taskId);
        return updated;
      });
      
      addToast({
        title: "Installation Cancelled",
        description: "Model installation was cancelled",
        variant: "info",
      });
    } catch (error) {
      addToast({
        title: "Error",
        description: "Failed to cancel installation",
        variant: "error",
      });
    }
  };

  const handleDelete = async (provider: string, name: string) => {
    if (!confirm(`Are you sure you want to delete model "${name}"? This action cannot be undone.`)) {
      return;
    }

    const modelKey = `${provider}-${name}`;
    
    try {
      // Set loading state
      setOperationStates(prev => new Map(prev.set(modelKey, {
        ...prev.get(modelKey),
        deleting: true
      })));
      
      await modelService.deleteModel(provider, name);
      
      addToast({
        title: "Success",
        description: `Model ${name} deleted successfully`,
        variant: "success",
      });
      
      // Remove from local state immediately for better UX
      setInstalledModels(prev => prev.filter(m => 
        !(m.id.provider === provider && m.id.name === name)
      ));
      
      setTimeout(() => fetchModels(), 1000); // Refresh after a delay
      
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Failed to delete model";
      addToast({
        title: "Error",
        description: errorMessage,
        variant: "error",
      });
    } finally {
      // Clear loading state
      setOperationStates(prev => {
        const updated = new Map(prev);
        const current = updated.get(modelKey);
        if (current) {
          updated.set(modelKey, { ...current, deleting: false });
        }
        return updated;
      });
    }
  };

  const handleConfigure = async () => {
    if (!selectedModel) return;
    
    const modelKey = `${selectedModel.id.provider}-${selectedModel.id.name}`;
    
    try {
      // Parse and validate configuration
      let parsedConfig;
      try {
        parsedConfig = JSON.parse(config);
      } catch (parseError) {
        addToast({
          title: "Configuration Error",
          description: "Invalid JSON configuration",
          variant: "error",
        });
        return;
      }
      
      // Set loading state
      setOperationStates(prev => new Map(prev.set(modelKey, {
        ...prev.get(modelKey),
        configuring: true
      })));
      
      await modelService.configureModel(
        selectedModel.id.provider, 
        selectedModel.id.name, 
        parsedConfig
      );
      
      addToast({
        title: "Success",
        description: "Model configured successfully",
        variant: "success",
      });
      
      setSelectedModel(null);
      fetchModels();
      
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Failed to configure model";
      addToast({
        title: "Error",
        description: errorMessage,
        variant: "error",
      });
    } finally {
      // Clear loading state
      setOperationStates(prev => {
        const updated = new Map(prev);
        const current = updated.get(modelKey);
        if (current) {
          updated.set(modelKey, { ...current, configuring: false });
        }
        return updated;
      });
    }
  };
  
  const validateConfig = useCallback(async (configText: string) => {
    if (!selectedModel || !configText.trim()) {
      setConfigValidation({ valid: true });
      return;
    }
    
    try {
      const parsedConfig = JSON.parse(configText);
      
      setConfigValidation({ valid: true, loading: true });
      
      const validation = await modelService.validateConfiguration(
        selectedModel.id.provider,
        selectedModel.id.name,
        parsedConfig
      );
      
      setConfigValidation(validation);
    } catch (parseError) {
      setConfigValidation({ 
        valid: false, 
        errors: ['Invalid JSON format'] 
      });
    }
  }, [selectedModel]);
  
  const handleHealthCheck = async (provider: string, name: string) => {
    const modelKey = `${provider}-${name}`;
    
    try {
      setOperationStates(prev => new Map(prev.set(modelKey, {
        ...prev.get(modelKey),
        healthChecking: true
      })));
      
      const health = await modelService.runHealthCheck(provider, name);
      
      setInstalledModels(prev => 
        prev.map(model => {
          if (model.id.provider === provider && model.id.name === name) {
            return { ...model, healthStatus: health };
          }
          return model;
        })
      );
      
      const statusMessage = health.status === 'healthy' ? 
        'Model is healthy and responding' : 
        `Model health check failed: ${health.details}`;
      
      addToast({
        title: "Health Check Complete",
        description: statusMessage,
        variant: health.status === 'healthy' ? "success" : "warning",
      });
      
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Health check failed";
      addToast({
        title: "Health Check Failed",
        description: errorMessage,
        variant: "error",
      });
    } finally {
      setOperationStates(prev => {
        const updated = new Map(prev);
        const current = updated.get(modelKey);
        if (current) {
          updated.set(modelKey, { ...current, healthChecking: false });
        }
        return updated;
      });
    }
  };
  
  const handleViewLogs = async (provider: string, name: string) => {
    try {
      const logs = await modelService.getModelLogs(provider, name);
      
      // Create a simple modal or new window to display logs
      const logWindow = window.open('', '_blank', 'width=800,height=600');
      if (logWindow) {
        logWindow.document.write(`
          <html>
            <head><title>Model Logs - ${name}</title></head>
            <body style="font-family: monospace; padding: 20px;">
              <h2>Logs for ${provider}/${name}</h2>
              <pre>${logs.map(log => `[${log.timestamp}] ${log.level}: ${log.message}`).join('\n')}</pre>
            </body>
          </html>
        `);
        logWindow.document.close();
      }
    } catch (error) {
      addToast({
        title: "Error",
        description: "Failed to fetch model logs",
        variant: "error",
      });
    }
  };
  
  // Debounce config validation
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      validateConfig(config);
    }, 500);
    
    return () => clearTimeout(timeoutId);
  }, [config, validateConfig]);

  const filteredInstalledModels = installedModels.filter(model => 
    model.id.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    model.id.provider.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const filteredWhisperModels = availableWhisperModels.filter(model => 
    model.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const filteredOllamaModels = availableOllamaModels.filter(model => 
    model.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const formatBytes = (bytes?: number) => {
    if (!bytes) return 'Unknown';
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    if (bytes === 0) return '0 Byte';
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      'installed': { variant: 'default' as const, icon: CheckCircle, color: 'text-green-600' },
      'downloading': { variant: 'secondary' as const, icon: Download, color: 'text-blue-600' },
      'installing': { variant: 'secondary' as const, icon: Play, color: 'text-blue-600' },
      'updating': { variant: 'secondary' as const, icon: RefreshCw, color: 'text-yellow-600' },
      'error': { variant: 'destructive' as const, icon: XCircle, color: 'text-red-600' },
      'deleting': { variant: 'outline' as const, icon: Trash2, color: 'text-orange-600' },
      'configuring': { variant: 'outline' as const, icon: Settings, color: 'text-purple-600' },
    };
    
    const config = statusConfig[status as keyof typeof statusConfig] || {
      variant: 'outline' as const,
      icon: AlertCircle,
      color: 'text-gray-600'
    };
    
    const Icon = config.icon;
    
    return (
      <Badge variant={config.variant} className="flex items-center gap-1">
        <Icon className={`h-3 w-3 ${config.color}`} />
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    );
  };
  
  const getHealthBadge = (health?: HealthCheckResult) => {
    if (!health) {
      return (
        <Badge variant="outline" className="flex items-center gap-1">
          <Monitor className="h-3 w-3 text-gray-500" />
          Unknown
        </Badge>
      );
    }
    
    const healthConfig = {
      'healthy': { variant: 'default' as const, icon: Wifi, color: 'text-green-600' },
      'unhealthy': { variant: 'destructive' as const, icon: WifiOff, color: 'text-red-600' },
      'unknown': { variant: 'outline' as const, icon: Monitor, color: 'text-gray-500' },
    };
    
    const config = healthConfig[health.status];
    const Icon = config.icon;
    
    return (
      <Badge variant={config.variant} className="flex items-center gap-1">
        <Icon className={`h-3 w-3 ${config.color}`} />
        {health.status.charAt(0).toUpperCase() + health.status.slice(1)}
      </Badge>
    );
  };
  
  const formatRequirements = (requirements?: any) => {
    if (!requirements) return null;
    
    const reqs = [];
    if (requirements.memory_gb) reqs.push(`${requirements.memory_gb}GB RAM`);
    if (requirements.disk_gb) reqs.push(`${requirements.disk_gb}GB Disk`);
    if (requirements.gpu_required) reqs.push('GPU Required');
    
    return reqs.length > 0 ? reqs.join(', ') : null;
  };

  return (
    <div className="container mx-auto py-8">
      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold">Model Management</h1>
          <p className="text-muted-foreground">Manage your AI models for visualization and analysis</p>
        </div>
      </div>

      <Card className="mb-8">
        <CardHeader>
          <CardTitle>Search Models</CardTitle>
          <CardDescription>
            Find models by name or provider
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="relative">
            <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search models..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
        </CardContent>
      </Card>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="installed">Installed Models</TabsTrigger>
          <TabsTrigger value="whisper">Whisper Models</TabsTrigger>
          <TabsTrigger value="ollama">Ollama Models</TabsTrigger>
        </TabsList>
        
        <TabsContent value="installed" className="mt-6">
          {loading ? (
            <div className="flex flex-col items-center justify-center py-12 space-y-4">
              <LoadingSpinner size="lg" />
              <p className="text-muted-foreground">Loading models...</p>
              {retryCount > 0 && (
                <p className="text-sm text-yellow-600">Retry attempt {retryCount}/3</p>
              )}
            </div>
          ) : lastError ? (
            <Card className="col-span-full">
              <CardContent className="flex flex-col items-center justify-center py-12 space-y-4">
                <AlertTriangle className="h-12 w-12 text-red-500" />
                <div className="text-center">
                  <h3 className="text-lg font-semibold text-red-700">Failed to Load Models</h3>
                  <p className="text-muted-foreground mt-2">{lastError}</p>
                </div>
                <Button onClick={() => fetchModels()} variant="outline">
                  <RefreshCw className="mr-2 h-4 w-4" />
                  Retry
                </Button>
              </CardContent>
            </Card>
          ) : (
            <motion.div 
              className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
            >
              {filteredInstalledModels.length === 0 ? (
                <Card className="col-span-full text-center py-12">
                  <CardContent>
                    <p className="text-muted-foreground">No installed models found.</p>
                  </CardContent>
                </Card>
              ) : (
                <>
                {filteredInstalledModels.map((model) => {
                  const modelKey = `${model.id.provider}-${model.id.name}`;
                  const operationState = operationStates.get(modelKey);
                  
                  return (
                    <ModelCard
                      key={modelKey}
                      model={model}
                      onConfigure={(m) => {
                        setSelectedModel(m);
                        setConfig(JSON.stringify(m.metadata.extra || {}, null, 2));
                      }}
                      onDelete={handleDelete}
                      onHealthCheck={handleHealthCheck}
                      onViewLogs={handleViewLogs}
                      loading={operationState}
                    />
                  );
                })}
                
                {/* Installation Progress Cards */}
                {Array.from(installTasks.values()).map((task) => (
                  <InstallProgressCard
                    key={task.id}
                    task={task}
                    onCancel={handleCancelInstall}
                  />
                ))}
                </>
              )}
            </motion.div>
          )}
        </TabsContent>
        
        <TabsContent value="whisper" className="mt-6">
          {loading ? (
            <div className="flex flex-col items-center justify-center py-12 space-y-4">
              <LoadingSpinner size="lg" />
              <p className="text-muted-foreground">Loading Whisper models...</p>
            </div>
          ) : (
            <motion.div 
              className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
            >
              {filteredWhisperModels.length === 0 ? (
                <Card className="col-span-full text-center py-12">
                  <CardContent>
                    <p className="text-muted-foreground">No Whisper models found.</p>
                  </CardContent>
                </Card>
              ) : (
                filteredWhisperModels.map((model) => (
                  <AvailableModelCard
                    key={`${model.provider}-${model.name}`}
                    model={model}
                    onInstall={handleInstall}
                    isInstalling={Array.from(installTasks.values()).some(
                      task => task.provider === model.provider && task.name === model.name
                    )}
                  />
                ))
              )}
            </motion.div>
          )}
        </TabsContent>
        
        <TabsContent value="ollama" className="mt-6">
          {loading ? (
            <div className="flex flex-col items-center justify-center py-12 space-y-4">
              <LoadingSpinner size="lg" />
              <p className="text-muted-foreground">Loading Ollama models...</p>
            </div>
          ) : (
            <motion.div 
              className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
            >
              {filteredOllamaModels.length === 0 ? (
                <Card className="col-span-full text-center py-12">
                  <CardContent>
                    <p className="text-muted-foreground">No Ollama models found.</p>
                  </CardContent>
                </Card>
              ) : (
                filteredOllamaModels.map((model) => (
                  <AvailableModelCard
                    key={`${model.provider}-${model.name}`}
                    model={model}
                    onInstall={handleInstall}
                    isInstalling={Array.from(installTasks.values()).some(
                      task => task.provider === model.provider && task.name === model.name
                    )}
                  />
                ))
              )}
            </motion.div>
          )}
        </TabsContent>
      </Tabs>

      {/* Enhanced Configuration Modal */}
      <AnimatePresence>
        {selectedModel && (
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.9 }}
              className="bg-background rounded-lg shadow-lg w-full max-w-3xl max-h-[90vh] overflow-y-auto"
            >
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <span>Configure Model</span>
                    <Button 
                      variant="ghost" 
                      size="sm" 
                      onClick={() => setSelectedModel(null)}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </CardTitle>
                  <CardDescription>
                    {selectedModel.id.name} ({selectedModel.id.provider}) v{selectedModel.id.version}
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* Model Information */}
                  <div className="grid grid-cols-2 gap-4 p-4 bg-muted/30 rounded-lg">
                    <div>
                      <Label className="text-sm font-medium">Size</Label>
                      <p className="text-sm text-muted-foreground">
                        {formatBytes(selectedModel.metadata.size_on_disk_bytes)}
                      </p>
                    </div>
                    <div>
                      <Label className="text-sm font-medium">Status</Label>
                      <div className="mt-1">
                        {getStatusBadge(selectedModel.status)}
                      </div>
                    </div>
                    {selectedModel.metadata.description && (
                      <div className="col-span-2">
                        <Label className="text-sm font-medium">Description</Label>
                        <p className="text-sm text-muted-foreground mt-1">
                          {selectedModel.metadata.description}
                        </p>
                      </div>
                    )}
                    {selectedModel.metadata.capabilities && (
                      <div className="col-span-2">
                        <Label className="text-sm font-medium">Capabilities</Label>
                        <div className="flex flex-wrap gap-1 mt-1">
                          {selectedModel.metadata.capabilities.map((cap, idx) => (
                            <Badge key={idx} variant="outline" className="text-xs">
                              {cap}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                  
                  {/* Configuration Editor */}
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <Label htmlFor="config">Configuration (JSON)</Label>
                      {configValidation.loading && (
                        <LoadingSpinner size="sm" />
                      )}
                    </div>
                    
                    <Textarea
                      id="config"
                      value={config}
                      onChange={(e) => setConfig(e.target.value)}
                      rows={12}
                      className={`font-mono text-sm ${
                        !configValidation.valid ? 'border-red-300 focus:border-red-500' : ''
                      }`}
                      placeholder="{\n  \"temperature\": 0.7,\n  \"max_tokens\": 2048\n}"
                    />
                    
                    {!configValidation.valid && configValidation.errors && (
                      <div className="text-sm text-red-600 space-y-1">
                        {configValidation.errors.map((error, idx) => (
                          <div key={idx} className="flex items-center gap-1">
                            <AlertCircle className="h-3 w-3" />
                            {error}
                          </div>
                        ))}
                      </div>
                    )}
                    
                    {configValidation.valid && config.trim() && (
                      <div className="text-sm text-green-600 flex items-center gap-1">
                        <CheckCircle className="h-3 w-3" />
                        Configuration is valid
                      </div>
                    )}
                  </div>
                </CardContent>
                <CardFooter className="flex justify-between">
                  <Button 
                    variant="outline" 
                    onClick={() => handleViewLogs(selectedModel.id.provider, selectedModel.id.name)}
                  >
                    <Eye className="mr-2 h-4 w-4" />
                    View Logs
                  </Button>
                  
                  <div className="flex space-x-2">
                    <Button variant="outline" onClick={() => setSelectedModel(null)}>
                      Cancel
                    </Button>
                    <Button 
                      onClick={handleConfigure}
                      disabled={!configValidation.valid || operationStates.get(`${selectedModel.id.provider}-${selectedModel.id.name}`)?.configuring}
                    >
                      {operationStates.get(`${selectedModel.id.provider}-${selectedModel.id.name}`)?.configuring ? (
                        <LoadingSpinner size="sm" className="mr-2" />
                      ) : (
                        <Settings className="mr-2 h-4 w-4" />
                      )}
                      Save Configuration
                    </Button>
                  </div>
                </CardFooter>
              </Card>
            </motion.div>
          </div>
        )}
      </AnimatePresence>
    </div>
  );
}

// Enhanced Model Card Component
function ModelCard({ model, onConfigure, onDelete, onHealthCheck, onViewLogs, loading }: ModelCardProps) {
  const modelKey = `${model.id.provider}-${model.id.name}`;
  
  const formatBytes = (bytes?: number) => {
    if (!bytes) return 'Unknown';
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    if (bytes === 0) return '0 Byte';
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      'installed': { variant: 'default' as const, icon: CheckCircle, color: 'text-green-600' },
      'downloading': { variant: 'secondary' as const, icon: Download, color: 'text-blue-600' },
      'installing': { variant: 'secondary' as const, icon: Play, color: 'text-blue-600' },
      'updating': { variant: 'secondary' as const, icon: RefreshCw, color: 'text-yellow-600' },
      'error': { variant: 'destructive' as const, icon: XCircle, color: 'text-red-600' },
      'deleting': { variant: 'outline' as const, icon: Trash2, color: 'text-orange-600' },
      'configuring': { variant: 'outline' as const, icon: Settings, color: 'text-purple-600' },
    };
    
    const config = statusConfig[status as keyof typeof statusConfig] || {
      variant: 'outline' as const,
      icon: AlertCircle,
      color: 'text-gray-600'
    };
    
    const Icon = config.icon;
    
    return (
      <Badge variant={config.variant} className="flex items-center gap-1">
        <Icon className={`h-3 w-3 ${config.color}`} />
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    );
  };
  
  const getHealthBadge = (health?: HealthCheckResult) => {
    if (!health) {
      return (
        <Badge variant="outline" className="flex items-center gap-1">
          <Monitor className="h-3 w-3 text-gray-500" />
          Unknown
        </Badge>
      );
    }
    
    const healthConfig = {
      'healthy': { variant: 'default' as const, icon: Wifi, color: 'text-green-600' },
      'unhealthy': { variant: 'destructive' as const, icon: WifiOff, color: 'text-red-600' },
      'unknown': { variant: 'outline' as const, icon: Monitor, color: 'text-gray-500' },
    };
    
    const config = healthConfig[health.status];
    const Icon = config.icon;
    
    return (
      <Badge variant={config.variant} className="flex items-center gap-1">
        <Icon className={`h-3 w-3 ${config.color}`} />
        {health.status.charAt(0).toUpperCase() + health.status.slice(1)}
      </Badge>
    );
  };
  
  const formatRequirements = (requirements?: any) => {
    if (!requirements) return null;
    
    const reqs = [];
    if (requirements.memory_gb) reqs.push(`${requirements.memory_gb}GB RAM`);
    if (requirements.disk_gb) reqs.push(`${requirements.disk_gb}GB Disk`);
    if (requirements.gpu_required) reqs.push('GPU Required');
    
    return reqs.length > 0 ? reqs.join(', ') : null;
  };
  
  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.2 }}
    >
      <Card className="flex flex-col h-full hover:shadow-lg transition-shadow">
        <CardHeader>
          <CardTitle className="flex justify-between items-start">
            <div className="flex-1 min-w-0">
              <div className="truncate font-semibold">{model.id.name}</div>
              <div className="text-sm text-muted-foreground">
                {model.id.provider} • v{model.id.version}
              </div>
            </div>
            <div className="flex flex-col gap-1 ml-2">
              {getStatusBadge(model.status)}
              {getHealthBadge(model.healthStatus)}
            </div>
          </CardTitle>
          {model.metadata.description && (
            <CardDescription className="line-clamp-2">
              {model.metadata.description}
            </CardDescription>
          )}
        </CardHeader>
        
        <CardContent className="flex-grow space-y-3">
          {/* Basic Metadata */}
          <div className="grid grid-cols-2 gap-2 text-sm">
            <div>
              <span className="text-muted-foreground">Size:</span>
              <span className="ml-1 font-medium">
                {formatBytes(model.metadata.size_on_disk_bytes)}
              </span>
            </div>
            {model.metadata.installed_at && (
              <div>
                <span className="text-muted-foreground">Installed:</span>
                <span className="ml-1 font-medium">
                  {new Date(model.metadata.installed_at).toLocaleDateString()}
                </span>
              </div>
            )}
          </div>
          
          {/* Health Details */}
          {model.healthStatus && (
            <div className="p-2 bg-muted/30 rounded text-xs space-y-1">
              <div className="flex justify-between">
                <span>Model Loaded:</span>
                <span className={model.healthStatus.details.model_loaded ? 'text-green-600' : 'text-red-600'}>
                  {model.healthStatus.details.model_loaded ? 'Yes' : 'No'}
                </span>
              </div>
              {model.healthStatus.details.response_time_ms && (
                <div className="flex justify-between">
                  <span>Response Time:</span>
                  <span>{model.healthStatus.details.response_time_ms}ms</span>
                </div>
              )}
              {model.healthStatus.details.memory_usage && (
                <div className="flex justify-between">
                  <span>Memory Usage:</span>
                  <span>{model.healthStatus.details.memory_usage}MB</span>
                </div>
              )}
            </div>
          )}
          
          {/* Capabilities */}
          {model.metadata.capabilities && model.metadata.capabilities.length > 0 && (
            <div>
              <div className="text-xs text-muted-foreground mb-1">Capabilities:</div>
              <div className="flex flex-wrap gap-1">
                {model.metadata.capabilities.slice(0, 3).map((cap, idx) => (
                  <Badge key={idx} variant="outline" className="text-xs px-1 py-0">
                    {cap}
                  </Badge>
                ))}
                {model.metadata.capabilities.length > 3 && (
                  <Badge variant="outline" className="text-xs px-1 py-0">
                    +{model.metadata.capabilities.length - 3} more
                  </Badge>
                )}
              </div>
            </div>
          )}
          
          {/* Requirements */}
          {model.metadata.requirements && (
            <div className="text-xs text-muted-foreground">
              <span>Requirements: </span>
              {formatRequirements(model.metadata.requirements) || 'None specified'}
            </div>
          )}
          
          {/* Last Used */}
          {model.metadata.last_used && (
            <div className="text-xs text-muted-foreground flex items-center gap-1">
              <Clock className="h-3 w-3" />
              Last used: {new Date(model.metadata.last_used).toLocaleDateString()}
            </div>
          )}
        </CardContent>
        
        <CardFooter className="flex flex-col gap-2">
          <div className="flex justify-between w-full">
            <Button
              variant="outline"
              size="sm"
              onClick={() => onConfigure(model)}
              disabled={loading?.configure}
            >
              {loading?.configure ? (
                <LoadingSpinner size="sm" className="mr-2" />
              ) : (
                <Settings className="mr-2 h-4 w-4" />
              )}
              Configure
            </Button>
            
            <div className="flex gap-1">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onHealthCheck(model.id.provider, model.id.name)}
                disabled={loading?.health}
                title="Run Health Check"
              >
                {loading?.health ? (
                  <LoadingSpinner size="sm" />
                ) : (
                  <Activity className="h-4 w-4" />
                )}
              </Button>
              
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onViewLogs(model.id.provider, model.id.name)}
                title="View Logs"
              >
                <Eye className="h-4 w-4" />
              </Button>
              
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onDelete(model.id.provider, model.id.name)}
                disabled={loading?.delete}
                title="Delete Model"
              >
                {loading?.delete ? (
                  <LoadingSpinner size="sm" />
                ) : (
                  <Trash2 className="h-4 w-4 text-red-500" />
                )}
              </Button>
            </div>
          </div>
        </CardFooter>
      </Card>
    </motion.div>
  );
}

// Available Model Card Component
function AvailableModelCard({ 
  model, 
  onInstall, 
  isInstalling 
}: { 
  model: AvailableModel; 
  onInstall: (provider: string, name: string, version?: string) => void;
  isInstalling: boolean;
}) {
  const getProviderIcon = (provider: string) => {
    switch (provider.toLowerCase()) {
      case 'whisper': return <Cpu className="h-4 w-4" />;
      case 'ollama': return <Database className="h-4 w-4" />;
      default: return <Monitor className="h-4 w-4" />;
    }
  };

  const formatBytes = (bytes?: number) => {
    if (!bytes) return 'Unknown';
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    if (bytes === 0) return '0 Byte';
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  };
  
  const formatRequirements = (requirements?: any) => {
    if (!requirements) return null;
    
    const reqs = [];
    if (requirements.memory_gb) reqs.push(`${requirements.memory_gb}GB RAM`);
    if (requirements.disk_gb) reqs.push(`${requirements.disk_gb}GB Disk`);
    if (requirements.gpu_required) reqs.push('GPU Required');
    
    return reqs.length > 0 ? reqs.join(', ') : null;
  };
  
  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.2 }}
    >
      <Card className="flex flex-col h-full hover:shadow-lg transition-shadow">
        <CardHeader>
          <CardTitle className="flex justify-between items-start">
            <div className="flex-1 min-w-0">
              <div className="truncate font-semibold">{model.name}</div>
              <div className="text-sm text-muted-foreground flex items-center gap-1">
                {getProviderIcon(model.provider)}
                {model.provider} • v{model.version}
              </div>
            </div>
            {model.metadata.popularity_score && (
              <div className="flex items-center gap-1 text-xs text-muted-foreground">
                <Heart className="h-3 w-3" />
                {model.metadata.popularity_score}
              </div>
            )}
          </CardTitle>
          {model.metadata.description && (
            <CardDescription className="line-clamp-3">
              {model.metadata.description}
            </CardDescription>
          )}
        </CardHeader>
        
        <CardContent className="flex-grow space-y-3">
          {/* Model Metadata */}
          <div className="grid grid-cols-2 gap-2 text-sm">
            {model.metadata.size_bytes && (
              <div>
                <span className="text-muted-foreground">Size:</span>
                <span className="ml-1 font-medium">
                  {formatBytes(model.metadata.size_bytes)}
                </span>
              </div>
            )}
            {model.metadata.last_updated && (
              <div>
                <span className="text-muted-foreground">Updated:</span>
                <span className="ml-1 font-medium">
                  {new Date(model.metadata.last_updated).toLocaleDateString()}
                </span>
              </div>
            )}
          </div>
          
          {/* Capabilities */}
          {model.metadata.capabilities && model.metadata.capabilities.length > 0 && (
            <div>
              <div className="text-xs text-muted-foreground mb-1">Capabilities:</div>
              <div className="flex flex-wrap gap-1">
                {model.metadata.capabilities.slice(0, 2).map((cap, idx) => (
                  <Badge key={idx} variant="outline" className="text-xs px-1 py-0">
                    {cap}
                  </Badge>
                ))}
                {model.metadata.capabilities.length > 2 && (
                  <Badge variant="outline" className="text-xs px-1 py-0">
                    +{model.metadata.capabilities.length - 2} more
                  </Badge>
                )}
              </div>
            </div>
          )}
          
          {/* Requirements */}
          {model.metadata.requirements && (
            <div className="text-xs text-muted-foreground p-2 bg-muted/20 rounded">
              <div className="font-medium mb-1">Requirements:</div>
              {formatRequirements(model.metadata.requirements) || 'Standard hardware'}
            </div>
          )}
        </CardContent>
        
        <CardFooter>
          <Button 
            className="w-full"
            onClick={() => onInstall(model.provider, model.name, model.version)}
            disabled={isInstalling}
          >
            {isInstalling ? (
              <>
                <LoadingSpinner size="sm" className="mr-2" />
                Installing...
              </>
            ) : (
              <>
                <Download className="mr-2 h-4 w-4" />
                Install
              </>
            )}
          </Button>
        </CardFooter>
      </Card>
    </motion.div>
  );
}

// Installation Progress Card Component
function InstallProgressCard({ 
  task, 
  onCancel 
}: { 
  task: InstallTask; 
  onCancel: (taskId: string) => void;
}) {
  const getProgressColor = (progress: number) => {
    if (progress < 30) return 'bg-red-500';
    if (progress < 70) return 'bg-yellow-500';
    return 'bg-green-500';
  };
  
  const formatBytes = (bytes?: number) => {
    if (!bytes) return 'Unknown';
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    if (bytes === 0) return '0 Byte';
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  };
  
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{ duration: 0.3 }}
    >
      <Card className="border-blue-200 bg-blue-50/30">
        <CardHeader className="pb-3">
          <CardTitle className="flex justify-between items-center text-base">
            <div>
              <span className="font-semibold">{task.name}</span>
              <div className="text-sm text-muted-foreground">
                {task.provider} {task.version && `• v${task.version}`}
              </div>
            </div>
            <Badge variant="secondary" className="flex items-center gap-1">
              <Download className="h-3 w-3 animate-bounce" />
              {task.progress.status}
            </Badge>
          </CardTitle>
        </CardHeader>
        
        <CardContent className="space-y-4">
          {/* Progress Bar */}
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>{task.progress.current_step || 'Installing...'}</span>
              <span>{Math.round(task.progress.progress)}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2 overflow-hidden">
              <motion.div
                className={`h-full ${getProgressColor(task.progress.progress)} transition-all duration-300`}
                initial={{ width: 0 }}
                animate={{ width: `${task.progress.progress}%` }}
              />
            </div>
          </div>
          
          {/* Download Progress */}
          {task.progress.downloaded_bytes && task.progress.total_bytes && (
            <div className="text-sm text-muted-foreground flex justify-between">
              <span>Downloaded:</span>
              <span>
                {formatBytes(task.progress.downloaded_bytes)} / {formatBytes(task.progress.total_bytes)}
              </span>
            </div>
          )}
          
          {/* Error Message */}
          {task.progress.status === 'error' && task.progress.error_message && (
            <div className="text-sm text-red-600 bg-red-50 p-2 rounded flex items-start gap-2">
              <AlertCircle className="h-4 w-4 mt-0.5 flex-shrink-0" />
              <span>{task.progress.error_message}</span>
            </div>
          )}
        </CardContent>
        
        <CardFooter>
          <Button 
            variant="outline" 
            size="sm" 
            onClick={() => onCancel(task.id)}
            disabled={task.progress.status === 'complete' || task.progress.status === 'error'}
            className="w-full"
          >
            <X className="mr-2 h-4 w-4" />
            Cancel Installation
          </Button>
        </CardFooter>
      </Card>
    </motion.div>
  );
}

export default function ModelsPage() {
  return (
    <ErrorBoundary
      fallback={
        <div className="container mx-auto py-8">
          <Card className="max-w-md mx-auto">
            <CardContent className="flex flex-col items-center justify-center py-12 space-y-4">
              <AlertTriangle className="h-12 w-12 text-red-500" />
              <div className="text-center">
                <h3 className="text-lg font-semibold text-red-700">Models Page Error</h3>
                <p className="text-muted-foreground mt-2">
                  Something went wrong while loading the models page. Please refresh and try again.
                </p>
              </div>
              <Button onClick={() => window.location.reload()} variant="outline">
                <RefreshCw className="mr-2 h-4 w-4" />
                Refresh Page
              </Button>
            </CardContent>
          </Card>
        </div>
      }
    >
      <ModelsPageContent />
    </ErrorBoundary>
  );
}