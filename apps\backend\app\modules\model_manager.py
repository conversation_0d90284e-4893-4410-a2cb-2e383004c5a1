"""
Model Manager Module - Modular Ollama model management.

This module provides self-contained model management functionality
that can be easily composed with other modules in a workflow.
"""

from typing import Dict, List, Any, Optional
import asyncio
import time

from . import (
    BaseModule,
    ModuleInput,
    ModuleOutput,
    ModuleType,
    ModuleConfig,
    ModuleStatus
)
from ..logger import get_logger
from ..models.model_cache import ModelCache
from ..models.model_api_client import ModelApiClient
from ..models.model_cli_client import ModelCliClient


logger = get_logger("modules.model_manager")


class OllamaModelModule(BaseModule):
    """Modular Ollama model management with self-contained functionality."""

    def __init__(self, name: str):
        super().__init__(name, ModuleType.MODEL_MANAGER)
        self.current_model: Optional[str] = None
        self._cache: Optional[ModelCache] = None
        self._api_client: Optional[ModelApiClient] = None
        self._cli_client: Optional[ModelCliClient] = None
        self._host: Optional[str] = None
        self._api_timeout: Optional[int] = None

    async def initialize(self, config: ModuleConfig) -> bool:
        """Initialize the model manager module."""
        try:
            self._status = ModuleStatus.INITIALIZING

            # Get settings from config
            settings = config.settings or {}
            self._host = settings.get('host')
            self._api_timeout = settings.get('api_timeout', 30)

            # Initialize components
            self._cache = ModelCache()
            self._api_client = ModelApiClient(self._host, self._api_timeout)
            self._cli_client = ModelCliClient()

            # Set dependencies if provided
            for dep_name, dep_module in self._dependencies.items():
                if hasattr(dep_module, 'get_client'):
                    # Could use dependency injection here
                    pass

            self._status = ModuleStatus.READY
            logger.info(f"Model manager module '{self.name}' initialized successfully")
            return True

        except Exception as e:
            self._status = ModuleStatus.ERROR
            logger.error(f"Failed to initialize model manager module '{self.name}': {e}")
            return False

    async def validate(self, input_data: ModuleInput) -> bool:
        """Validate input data for model operations."""
        action = input_data.data.get('action')
        if not action:
            logger.error("No action specified in input data")
            return False

        valid_actions = ['list', 'pull', 'set', 'use', 'info', 'remove']
        if action not in valid_actions:
            logger.error(f"Invalid action: {action}")
            return False

        return True

    async def execute(self, input_data: ModuleInput) -> ModuleOutput:
        """Execute model management operations."""
        if not await self.validate(input_data):
            return ModuleOutput(
                success=False,
                error="Invalid input data"
            )

        action = input_data.data.get('action')
        self._status = ModuleStatus.RUNNING

        try:
            start_time = time.time()

            if action == 'list':
                result = await self._list_models()
            elif action == 'pull':
                model_name = input_data.data.get('model_name')
                result = await self._pull_model(model_name)
            elif action == 'set':
                model_name = input_data.data.get('model_name')
                result = await self._set_model(model_name)
            elif action == 'use':
                model_name = input_data.data.get('model_name')
                input_text = input_data.data.get('input_data', '')
                options = input_data.data.get('options', {})
                result = await self._use_model(model_name, input_text, options)
            elif action == 'info':
                model_name = input_data.data.get('model_name')
                result = await self._get_model_info(model_name)
            elif action == 'remove':
                model_name = input_data.data.get('model_name')
                result = await self._remove_model(model_name)
            else:
                return ModuleOutput(
                    success=False,
                    error=f"Unsupported action: {action}"
                )

            duration = time.time() - start_time

            return ModuleOutput(
                success=True,
                data=result,
                metadata={
                    'action': action,
                    'duration': duration,
                    'module': self.name
                }
            )

        except Exception as e:
            logger.error(f"Error executing action '{action}' in model manager: {e}")
            return ModuleOutput(
                success=False,
                error=str(e),
                metadata={'action': action, 'module': self.name}
            )
        finally:
            self._status = ModuleStatus.READY

    async def _list_models(self) -> Dict[str, Any]:
        """List available Ollama models."""
        try:
            # Check cache first
            if self._cache.is_cache_valid():
                models = self._cache.get_available_models()
            else:
                # Try API first, fallback to CLI
                models = await self._api_client.list_models()
                if not models:
                    models = self._cli_client.list_models()

                self._cache.set_available_models(models)

            return {
                'models': models,
                'count': len(models),
                'method': 'cached' if self._cache.is_cache_valid() else 'api'
            }

        except Exception as e:
            logger.error(f"Failed to list models: {e}")
            return {'models': [], 'count': 0, 'error': str(e)}

    async def _pull_model(self, model_name: str) -> Dict[str, Any]:
        """Pull an Ollama model."""
        if not model_name:
            raise ValueError("Model name is required")

        try:
            logger.info(f"Pulling model: {model_name}")

            # Try API method first
            result = await self._api_client.pull_model(model_name)
            if result:
                self._cache.invalidate_cache()
                return {
                    'model_name': model_name,
                    'success': True,
                    'method': 'api',
                    'result': result
                }

            # Fallback to CLI method
            result = self._cli_client.pull_model(model_name)
            self._cache.invalidate_cache()
            return {
                'model_name': model_name,
                'success': True,
                'method': 'cli',
                'result': result
            }

        except Exception as e:
            logger.error(f"Failed to pull model {model_name}: {e}")
            raise

    async def _set_model(self, model_name: str) -> Dict[str, Any]:
        """Set the active Ollama model."""
        if not model_name:
            raise ValueError("Model name is required")

        # Verify the model exists
        available_models = await self._list_models()
        if model_name not in available_models.get('models', []):
            raise ValueError(f"Model {model_name} not available")

        self.current_model = model_name
        logger.info(f"Active model set to: {model_name}")

        return {
            'model_name': model_name,
            'success': True,
            'message': f"Active model set to {model_name}"
        }

    async def _use_model(self, model_name: str, input_data: str, options: Dict[str, Any]) -> Dict[str, Any]:
        """Use an Ollama model for inference."""
        if not model_name:
            raise ValueError("Model name is required")
        if not input_data:
            raise ValueError("Input data is required")

        # Verify the model exists
        available_models = await self._list_models()
        if model_name not in available_models.get('models', []):
            raise ValueError(f"Model {model_name} not available")

        logger.info(f"Using model {model_name} for inference")

        # Try API method first
        result = await self._api_client.use_model(model_name, input_data, **options)
        if result:
            return {
                'model_name': model_name,
                'success': True,
                'method': 'api',
                'result': result
            }

        # Fallback to CLI method
        result = self._cli_client.use_model(model_name, input_data, **options)
        return {
            'model_name': model_name,
            'success': True,
            'method': 'cli',
            'result': result
        }

    async def _get_model_info(self, model_name: str) -> Dict[str, Any]:
        """Get detailed information about a model."""
        if not model_name:
            raise ValueError("Model name is required")

        # Check cache first
        cached_info = self._cache.get_model_info(model_name)
        if cached_info:
            return {
                'model_name': model_name,
                'info': cached_info,
                'cached': True
            }

        # Get from API
        info = await self._api_client.get_model_info(model_name)
        if info:
            self._cache.set_model_info(model_name, info)

        return {
            'model_name': model_name,
            'info': info,
            'cached': False
        }

    async def _remove_model(self, model_name: str) -> Dict[str, Any]:
        """Remove a model from Ollama."""
        if not model_name:
            raise ValueError("Model name is required")

        try:
            # Try API method first
            if await self._api_client.remove_model(model_name):
                self._cache.invalidate_cache()
                return {
                    'model_name': model_name,
                    'success': True,
                    'method': 'api'
                }

            # Fallback to CLI method
            if self._cli_client.remove_model(model_name):
                self._cache.invalidate_cache()
                return {
                    'model_name': model_name,
                    'success': True,
                    'method': 'cli'
                }

            return {
                'model_name': model_name,
                'success': False,
                'error': 'Failed to remove model'
            }

        except Exception as e:
            logger.error(f"Failed to remove model {model_name}: {e}")
            return {
                'model_name': model_name,
                'success': False,
                'error': str(e)
            }

    def get_current_model(self) -> Optional[str]:
        """Get the currently active model."""
        return self.current_model

    def clear_cache(self) -> None:
        """Clear the model cache."""
        if self._cache:
            self._cache.clear_cache()
        logger.info("Model cache cleared")

    async def health_check(self) -> Dict[str, Any]:
        """Check Ollama service health."""
        try:
            result = await self._api_client.health_check()
            return {
                'status': 'healthy' if result else 'unhealthy',
                'details': result
            }
        except Exception as e:
            return {
                'status': 'error',
                'error': str(e)
            }

    async def cleanup(self) -> None:
        """Cleanup module resources."""
        try:
            if self._api_client:
                await self._api_client.close()
            if self._cache:
                self._cache.clear_cache()
            self.current_model = None
            await super().cleanup()
            logger.info(f"Model manager module '{self.name}' cleaned up")
        except Exception as e:
            logger.error(f"Error cleaning up model manager module: {e}")
