"use client";

import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/shared/ui/card';
import theme from '@/shared/config/theme';

const TypographyViewer: React.FC = () => {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Typography</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div>
            <h3 className="text-sm font-medium text-muted-foreground mb-1">Font Family</h3>
            <div className="flex space-x-4">
              <div>
                <p className="text-xs text-muted-foreground mb-1">Sans</p>
                <p className="font-sans">The quick brown fox jumps over the lazy dog</p>
              </div>
              <div>
                <p className="text-xs text-muted-foreground mb-1">Mono</p>
                <p className="font-mono">The quick brown fox jumps over the lazy dog</p>
              </div>
            </div>
          </div>
          
          <div>
            <h3 className="text-sm font-medium text-muted-foreground mb-1">Font Sizes</h3>
            <div className="space-y-2">
              {Object.entries(theme.typography.fontSize).map(([key, value]) => (
                <div key={key} className="flex items-center">
                  <span className="w-16 text-xs text-muted-foreground">{key}</span>
                  <span className="text-foreground" style={{ fontSize: value }}>
                    The quick brown fox jumps over the lazy dog
                  </span>
                </div>
              ))}
            </div>
          </div>
          
          <div>
            <h3 className="text-sm font-medium text-muted-foreground mb-1">Font Weights</h3>
            <div className="space-y-2">
              {Object.entries(theme.typography.fontWeight).map(([key, value]) => (
                <div key={key} className="flex items-center">
                  <span className="w-16 text-xs text-muted-foreground">{key}</span>
                  <span className="text-foreground" style={{ fontWeight: value }}>
                    The quick brown fox jumps over the lazy dog
                  </span>
                </div>
              ))}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default TypographyViewer;