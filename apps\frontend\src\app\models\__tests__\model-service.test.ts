"""
Tests for the model service.
"""

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { modelService, Model, AvailableModel, InstallProgress } from '@/shared/services/model-service';
import { apiClient } from '@/shared/lib/api-client';

// Mock the API client
vi.mock('@/shared/lib/api-client');
const mockApiClient = vi.mocked(apiClient);

// Mock EventSource for SSE testing
class MockEventSource {
  onmessage: ((event: MessageEvent) => void) | null = null;
  onerror: ((event: Event) => void) | null = null;
  readyState: number = 1;
  url: string;
  
  constructor(url: string) {
    this.url = url;
  }
  
  close() {
    this.readyState = 2;
  }
  
  // Helper method to simulate events
  simulateMessage(data: any) {
    if (this.onmessage) {
      this.onmessage(new MessageEvent('message', { data: JSON.stringify(data) }));
    }
  }
  
  simulateError() {
    if (this.onerror) {
      this.onerror(new Event('error'));
    }
  }
}

// Mock EventSource globally
(global as any).EventSource = MockEventSource;

describe('ModelService', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });
  
  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('getInstalledModels', () => {
    it('should fetch installed models successfully', async () => {
      const mockResponse = {
        items: [
          {
            id: { provider: 'WHISPER', name: 'tiny', version: '1.0' },
            status: 'installed',
            metadata: { size_on_disk_bytes: 40960000 }
          }
        ],
        total: 1,
        page: 1,
        page_size: 50
      };
      
      mockApiClient.get.mockResolvedValue(mockResponse);
      
      const result = await modelService.getInstalledModels();
      
      expect(mockApiClient.get).toHaveBeenCalledWith('/models/installed');
      expect(result).toEqual(mockResponse);
    });
    
    it('should handle API errors', async () => {
      const error = new Error('API Error');
      mockApiClient.get.mockRejectedValue(error);
      
      await expect(modelService.getInstalledModels()).rejects.toThrow('API Error');
    });
  });

  describe('getAvailableModels', () => {
    it('should fetch available models with default parameters', async () => {
      const mockResponse = {
        items: [
          {
            provider: 'WHISPER',
            name: 'base',
            version: '1.0',
            metadata: { size_bytes: 74000000 }
          }
        ],
        total: 1,
        page: 1,
        page_size: 50
      };
      
      mockApiClient.get.mockResolvedValue(mockResponse);
      
      const result = await modelService.getAvailableModels();
      
      expect(mockApiClient.get).toHaveBeenCalledWith('/models/available?page=1&page_size=50');
      expect(result).toEqual(mockResponse);
    });
    
    it('should fetch available models with provider filter', async () => {
      const mockResponse = { items: [], total: 0, page: 1, page_size: 50 };
      mockApiClient.get.mockResolvedValue(mockResponse);
      
      await modelService.getAvailableModels('whisper', 2, 25, 'search');
      
      expect(mockApiClient.get).toHaveBeenCalledWith(
        '/models/available?provider=whisper&page=2&page_size=25&q=search'
      );
    });
    
    it('should handle empty query parameters', async () => {
      const mockResponse = { items: [], total: 0, page: 1, page_size: 50 };
      mockApiClient.get.mockResolvedValue(mockResponse);
      
      await modelService.getAvailableModels(undefined, 1, 50, undefined);
      
      expect(mockApiClient.get).toHaveBeenCalledWith('/models/available?page=1&page_size=50');
    });
  });

  describe('installModel', () => {
    it('should start model installation', async () => {
      const mockResponse = { task_id: 'task-123' };
      mockApiClient.post.mockResolvedValue(mockResponse);
      
      const result = await modelService.installModel('WHISPER', 'tiny', '1.0');
      
      expect(mockApiClient.post).toHaveBeenCalledWith('/models/install', {
        provider: 'WHISPER',
        name: 'tiny',
        version: '1.0'
      });
      expect(result).toEqual(mockResponse);
    });
    
    it('should install model without version', async () => {
      const mockResponse = { task_id: 'task-456' };
      mockApiClient.post.mockResolvedValue(mockResponse);
      
      await modelService.installModel('OLLAMA', 'llama3');
      
      expect(mockApiClient.post).toHaveBeenCalledWith('/models/install', {
        provider: 'OLLAMA',
        name: 'llama3',
        version: undefined
      });
    });
    
    it('should handle installation errors', async () => {
      const error = new Error('Installation failed');
      mockApiClient.post.mockRejectedValue(error);
      
      await expect(modelService.installModel('WHISPER', 'tiny')).rejects.toThrow('Installation failed');
    });
  });

  describe('getInstallProgress', () => {
    it('should fetch installation progress', async () => {
      const mockProgress = {
        status: 'downloading' as const,
        progress: 50,
        downloaded_bytes: 500000,
        total_bytes: 1000000
      };
      
      mockApiClient.get.mockResolvedValue(mockProgress);
      
      const result = await modelService.getInstallProgress('task-123');
      
      expect(mockApiClient.get).toHaveBeenCalledWith('/models/install/task-123/progress');
      expect(result).toEqual(mockProgress);
    });
  });

  describe('cancelInstall', () => {
    it('should cancel installation', async () => {
      mockApiClient.delete.mockResolvedValue(undefined);
      
      await modelService.cancelInstall('task-123');
      
      expect(mockApiClient.delete).toHaveBeenCalledWith('/models/install/task-123');
    });
  });

  describe('configureModel', () => {
    it('should configure model successfully', async () => {
      const mockResponse = { ok: true };
      const config = { temperature: 0.7, language: 'en' };
      
      mockApiClient.post.mockResolvedValue(mockResponse);
      
      const result = await modelService.configureModel('WHISPER', 'tiny', config);
      
      expect(mockApiClient.post).toHaveBeenCalledWith('/models/configure', {
        provider: 'WHISPER',
        name: 'tiny',
        config
      });
      expect(result).toEqual(mockResponse);
    });
    
    it('should handle configuration errors', async () => {
      const error = new Error('Configuration failed');
      mockApiClient.post.mockRejectedValue(error);
      
      await expect(
        modelService.configureModel('WHISPER', 'tiny', {})
      ).rejects.toThrow('Configuration failed');
    });
  });

  describe('deleteModel', () => {
    it('should delete model without version', async () => {
      const mockResponse = { ok: true };
      mockApiClient.delete.mockResolvedValue(mockResponse);
      
      const result = await modelService.deleteModel('WHISPER', 'tiny');
      
      expect(mockApiClient.delete).toHaveBeenCalledWith('/models/WHISPER/tiny?');
      expect(result).toEqual(mockResponse);
    });
    
    it('should delete model with version', async () => {
      const mockResponse = { ok: true };
      mockApiClient.delete.mockResolvedValue(mockResponse);
      
      await modelService.deleteModel('OLLAMA', 'llama3', '8b');
      
      expect(mockApiClient.delete).toHaveBeenCalledWith('/models/OLLAMA/llama3?version=8b');
    });
    
    it('should handle deletion errors', async () => {
      const error = new Error('Deletion failed');
      mockApiClient.delete.mockRejectedValue(error);
      
      await expect(
        modelService.deleteModel('WHISPER', 'tiny')
      ).rejects.toThrow('Deletion failed');
    });
  });

  describe('checkModelHealth', () => {
    it('should check model health', async () => {
      const mockHealth = {
        status: 'healthy' as const,
        details: {
          model_loaded: true,
          memory_usage: 512,
          response_time_ms: 150
        },
        timestamp: '2024-01-01T12:00:00Z'
      };
      
      mockApiClient.get.mockResolvedValue(mockHealth);
      
      const result = await modelService.checkModelHealth('WHISPER', 'tiny');
      
      expect(mockApiClient.get).toHaveBeenCalledWith('/models/WHISPER/tiny/health');
      expect(result).toEqual(mockHealth);
    });
  });

  describe('runHealthCheck', () => {
    it('should run health check', async () => {
      const mockHealth = {
        status: 'healthy' as const,
        details: { model_loaded: true },
        timestamp: '2024-01-01T12:00:00Z'
      };
      
      mockApiClient.post.mockResolvedValue(mockHealth);
      
      const result = await modelService.runHealthCheck('WHISPER', 'tiny');
      
      expect(mockApiClient.post).toHaveBeenCalledWith('/models/WHISPER/tiny/health');
      expect(result).toEqual(mockHealth);
    });
  });

  describe('getModelLogs', () => {
    it('should fetch model logs with default limit', async () => {
      const mockLogs = [
        { timestamp: '2024-01-01T12:00:00Z', level: 'INFO', message: 'Model loaded' },
        { timestamp: '2024-01-01T12:01:00Z', level: 'DEBUG', message: 'Processing request' }
      ];
      
      mockApiClient.get.mockResolvedValue(mockLogs);
      
      const result = await modelService.getModelLogs('WHISPER', 'tiny');
      
      expect(mockApiClient.get).toHaveBeenCalledWith('/models/WHISPER/tiny/logs?limit=100');
      expect(result).toEqual(mockLogs);
    });
    
    it('should fetch model logs with custom limit', async () => {
      const mockLogs = [];
      mockApiClient.get.mockResolvedValue(mockLogs);
      
      await modelService.getModelLogs('WHISPER', 'tiny', 50);
      
      expect(mockApiClient.get).toHaveBeenCalledWith('/models/WHISPER/tiny/logs?limit=50');
    });
  });

  describe('validateConfiguration', () => {
    it('should validate configuration successfully', async () => {
      const mockValidation = { valid: true };
      const config = { temperature: 0.7 };
      
      mockApiClient.post.mockResolvedValue(mockValidation);
      
      const result = await modelService.validateConfiguration('WHISPER', 'tiny', config);
      
      expect(mockApiClient.post).toHaveBeenCalledWith('/models/WHISPER/tiny/validate-config', {
        config
      });
      expect(result).toEqual(mockValidation);
    });
    
    it('should return validation errors', async () => {
      const mockValidation = {
        valid: false,
        errors: ['Invalid temperature value']
      };
      
      mockApiClient.post.mockResolvedValue(mockValidation);
      
      const result = await modelService.validateConfiguration('WHISPER', 'tiny', {
        temperature: 2.0
      });
      
      expect(result).toEqual(mockValidation);
    });
  });

  describe('subscribeToInstallProgress', () => {
    it('should create EventSource and handle progress updates', () => {
      const onProgress = vi.fn();
      const onError = vi.fn();
      
      const cleanup = modelService.subscribeToInstallProgress('task-123', onProgress, onError);
      
      // Find the created EventSource
      const eventSource = new MockEventSource('/models/install/task-123/stream');
      
      // Simulate progress update
      const progressData = {
        status: 'downloading',
        progress: 50,
        downloaded_bytes: 500000,
        total_bytes: 1000000
      };
      
      eventSource.simulateMessage(progressData);
      
      expect(onProgress).toHaveBeenCalledWith(progressData);
      
      // Test cleanup
      cleanup();
      expect(eventSource.readyState).toBe(2); // CLOSED
    });
    
    it('should handle EventSource errors', () => {
      const onProgress = vi.fn();
      const onError = vi.fn();
      
      modelService.subscribeToInstallProgress('task-123', onProgress, onError);
      
      const eventSource = new MockEventSource('/models/install/task-123/stream');
      eventSource.simulateError();
      
      expect(onError).toHaveBeenCalledWith(expect.any(Error));
    });
    
    it('should handle invalid JSON in progress updates', () => {
      const onProgress = vi.fn();
      const onError = vi.fn();
      
      modelService.subscribeToInstallProgress('task-123', onProgress, onError);
      
      const eventSource = new MockEventSource('/models/install/task-123/stream');
      
      // Simulate invalid JSON
      if (eventSource.onmessage) {
        eventSource.onmessage(new MessageEvent('message', { data: 'invalid json' }));
      }
      
      expect(onError).toHaveBeenCalledWith(expect.any(Error));
      expect(onProgress).not.toHaveBeenCalled();
    });
    
    it('should throw error in server environment', () => {
      // Mock server environment
      const originalWindow = global.window;
      delete (global as any).window;
      
      expect(() => {
        modelService.subscribeToInstallProgress('task-123', vi.fn());
      }).toThrow('SSE not supported in server environment');
      
      // Restore window
      (global as any).window = originalWindow;
    });
    
    it('should handle EventSource without error callback', () => {
      const onProgress = vi.fn();
      
      modelService.subscribeToInstallProgress('task-123', onProgress);
      
      const eventSource = new MockEventSource('/models/install/task-123/stream');
      
      // Should not throw when no error callback is provided
      expect(() => {
        eventSource.simulateError();
      }).not.toThrow();
    });
  });

  describe('Error Handling', () => {
    it('should handle network errors gracefully', async () => {
      const networkError = new Error('Network error');
      networkError.name = 'NetworkError';
      
      mockApiClient.get.mockRejectedValue(networkError);
      
      await expect(modelService.getInstalledModels()).rejects.toThrow('Network error');
    });
    
    it('should handle timeout errors', async () => {
      const timeoutError = new Error('Request timeout');
      timeoutError.name = 'TimeoutError';
      
      mockApiClient.post.mockRejectedValue(timeoutError);
      
      await expect(
        modelService.installModel('WHISPER', 'tiny')
      ).rejects.toThrow('Request timeout');
    });
    
    it('should handle API response errors', async () => {
      const apiError = {
        response: {
          status: 400,
          data: {
            detail: {
              message: 'Model not found',
              code: 'NOT_FOUND'
            }
          }
        }
      };
      
      mockApiClient.delete.mockRejectedValue(apiError);
      
      await expect(
        modelService.deleteModel('WHISPER', 'nonexistent')
      ).rejects.toEqual(apiError);
    });
  });

  describe('Parameter Validation', () => {
    it('should handle empty provider names', async () => {
      await expect(
        modelService.installModel('', 'model')
      ).rejects.toThrow();
    });
    
    it('should handle empty model names', async () => {
      await expect(
        modelService.installModel('WHISPER', '')
      ).rejects.toThrow();
    });
    
    it('should handle null/undefined parameters', async () => {
      await expect(
        modelService.configureModel('WHISPER', 'tiny', null as any)
      ).rejects.toThrow();
    });
  });

  describe('Concurrent Requests', () => {
    it('should handle multiple concurrent requests', async () => {
      const mockResponse1 = { items: [], total: 0, page: 1, page_size: 50 };
      const mockResponse2 = { task_id: 'task-1' };
      const mockResponse3 = { task_id: 'task-2' };
      
      mockApiClient.get.mockResolvedValue(mockResponse1);
      mockApiClient.post.mockResolvedValueOnce(mockResponse2)
                           .mockResolvedValueOnce(mockResponse3);
      
      const promises = [
        modelService.getInstalledModels(),
        modelService.installModel('WHISPER', 'tiny'),
        modelService.installModel('OLLAMA', 'llama3')
      ];
      
      const results = await Promise.all(promises);
      
      expect(results[0]).toEqual(mockResponse1);
      expect(results[1]).toEqual(mockResponse2);
      expect(results[2]).toEqual(mockResponse3);
    });
  });

  describe('Memory Management', () => {
    it('should clean up EventSource subscriptions', () => {
      const cleanupFunctions: (() => void)[] = [];
      
      // Create multiple subscriptions
      for (let i = 0; i < 5; i++) {
        const cleanup = modelService.subscribeToInstallProgress(
          `task-${i}`,
          vi.fn()
        );
        cleanupFunctions.push(cleanup);
      }
      
      // Clean up all subscriptions
      cleanupFunctions.forEach(cleanup => cleanup());
      
      // Should not cause memory leaks or errors
      expect(cleanupFunctions).toHaveLength(5);
    });
  });
});
