"""
Data models for the Project Scanner system.

This module defines the core data structures used throughout the scanning system,
including request/response models, issue classifications, and metadata structures.
"""

from datetime import datetime
from enum import Enum
from pathlib import Path
from typing import Any, Dict, List, Optional, Set, Union
from pydantic import BaseModel, Field, validator


class IssueSeverity(str, Enum):
    """Severity levels for detected issues."""
    CRITICAL = "critical"
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"
    INFO = "info"


class IssueType(str, Enum):
    """Categories of issues that can be detected."""
    # Code Quality Issues
    CODE_COMPLEXITY = "code_complexity"
    CODE_DUPLICATION = "code_duplication"
    STYLE_VIOLATION = "style_violation"
    MAINTAINABILITY = "maintainability"
    TEST_COVERAGE = "test_coverage"
    
    # Security Issues
    SECURITY_VULNERABILITY = "security_vulnerability"
    HARDCODED_SECRET = "hardcoded_secret"
    INSECURE_DEPENDENCY = "insecure_dependency"
    AUTHENTICATION_ISSUE = "authentication_issue"
    AUTHORIZATION_ISSUE = "authorization_issue"
    INPUT_VALIDATION = "input_validation"
    CORS_MISCONFIGURATION = "cors_misconfiguration"
    
    # Dependency Issues
    OUTDATED_DEPENDENCY = "outdated_dependency"
    VULNERABLE_DEPENDENCY = "vulnerable_dependency"
    LICENSE_ISSUE = "license_issue"
    UNUSED_DEPENDENCY = "unused_dependency"
    
    # Structure Issues
    CIRCULAR_DEPENDENCY = "circular_dependency"
    UNUSED_FILE = "unused_file"
    MISSING_FILE = "missing_file"
    ARCHITECTURE_VIOLATION = "architecture_violation"
    
    # Performance Issues
    LARGE_FILE = "large_file"
    INEFFICIENT_ALGORITHM = "inefficient_algorithm"
    MEMORY_LEAK = "memory_leak"
    DATABASE_PERFORMANCE = "database_performance"
    
    # Configuration Issues
    MISSING_CONFIG = "missing_config"
    INVALID_CONFIG = "invalid_config"
    INSECURE_CONFIG = "insecure_config"
    
    # Documentation Issues
    MISSING_DOCUMENTATION = "missing_documentation"
    OUTDATED_DOCUMENTATION = "outdated_documentation"


class IssueLocation(BaseModel):
    """Represents the location of an issue within a file."""
    file_path: str = Field(..., description="Path to the file containing the issue")
    line_number: Optional[int] = Field(None, description="Line number where issue occurs")
    column_number: Optional[int] = Field(None, description="Column number where issue occurs")
    line_end: Optional[int] = Field(None, description="End line number for multi-line issues")
    function_name: Optional[str] = Field(None, description="Function/method name if applicable")
    class_name: Optional[str] = Field(None, description="Class name if applicable")


class RemediationSuggestion(BaseModel):
    """Suggested remediation for an issue."""
    title: str = Field(..., description="Brief title of the remediation")
    description: str = Field(..., description="Detailed description of how to fix")
    code_example: Optional[str] = Field(None, description="Example code showing the fix")
    automated_fix: bool = Field(False, description="Whether this can be auto-fixed")
    effort_estimate: Optional[str] = Field(None, description="Estimated effort (e.g., '5 minutes', '2 hours')")
    references: List[str] = Field(default_factory=list, description="Helpful references/links")


class Issue(BaseModel):
    """Represents a detected issue in the codebase."""
    id: str = Field(..., description="Unique identifier for this issue")
    type: IssueType = Field(..., description="Type/category of the issue")
    severity: IssueSeverity = Field(..., description="Severity level of the issue")
    title: str = Field(..., description="Brief, human-readable title")
    description: str = Field(..., description="Detailed description of the issue")
    location: IssueLocation = Field(..., description="Where the issue is located")
    
    # Detection metadata
    detector: str = Field(..., description="Name of detector that found this issue")
    rule_id: Optional[str] = Field(None, description="Specific rule ID that triggered")
    confidence: float = Field(..., ge=0.0, le=1.0, description="Confidence level (0.0-1.0)")
    
    # Impact assessment
    impact_score: int = Field(..., ge=0, le=100, description="Impact score (0-100)")
    likelihood_score: int = Field(..., ge=0, le=100, description="Likelihood score (0-100)")
    risk_score: float = Field(..., ge=0.0, le=100.0, description="Calculated risk score")
    
    # Remediation
    remediation: Optional[RemediationSuggestion] = Field(None, description="How to fix this issue")
    
    # Additional metadata
    tags: Set[str] = Field(default_factory=set, description="Tags for categorization")
    cwe_id: Optional[str] = Field(None, description="CWE (Common Weakness Enumeration) ID")
    owasp_category: Optional[str] = Field(None, description="OWASP Top 10 category if applicable")
    created_at: datetime = Field(default_factory=datetime.now, description="When issue was detected")
    
    @validator('risk_score', pre=True, always=True)
    def calculate_risk_score(cls, v, values):
        """Calculate risk score based on impact and likelihood if not provided."""
        if v is None:
            impact = values.get('impact_score', 0)
            likelihood = values.get('likelihood_score', 0)
            return round((impact * likelihood) / 100, 2)
        return v


class ScanConfiguration(BaseModel):
    """Configuration for a scan operation."""
    include_patterns: List[str] = Field(
        default=["**/*.py", "**/*.js", "**/*.ts", "**/*.json", "**/*.yaml", "**/*.yml"],
        description="File patterns to include in scan"
    )
    exclude_patterns: List[str] = Field(
        default=["**/node_modules/**", "**/.venv/**", "**/__pycache__/**", "**/dist/**"],
        description="File patterns to exclude from scan"
    )
    max_file_size: int = Field(default=10*1024*1024, description="Maximum file size to scan (bytes)")
    
    # Detector configuration
    enable_code_quality: bool = Field(True, description="Enable code quality detection")
    enable_security: bool = Field(True, description="Enable security vulnerability detection")
    enable_dependencies: bool = Field(True, description="Enable dependency analysis")
    enable_structure: bool = Field(True, description="Enable structure analysis")
    enable_performance: bool = Field(True, description="Enable performance analysis")
    
    # Severity filtering
    min_severity: IssueSeverity = Field(IssueSeverity.INFO, description="Minimum severity to report")
    max_issues_per_type: int = Field(default=1000, description="Maximum issues per type to report")
    
    # Custom rules
    custom_rules: Dict[str, Any] = Field(default_factory=dict, description="Custom detection rules")


class ScanRequest(BaseModel):
    """Request to scan a project."""
    project_path: str = Field(..., description="Path to the project to scan")
    scan_id: Optional[str] = Field(None, description="Optional scan identifier")
    configuration: ScanConfiguration = Field(
        default_factory=ScanConfiguration,
        description="Scan configuration"
    )
    
    @validator('project_path')
    def validate_project_path(cls, v):
        """Validate that project path exists."""
        path = Path(v)
        if not path.exists():
            raise ValueError(f"Project path does not exist: {v}")
        if not path.is_dir():
            raise ValueError(f"Project path is not a directory: {v}")
        return str(path.resolve())


class ScanMetrics(BaseModel):
    """Metrics collected during scanning."""
    files_scanned: int = Field(0, description="Number of files scanned")
    files_skipped: int = Field(0, description="Number of files skipped")
    total_lines_of_code: int = Field(0, description="Total lines of code analyzed")
    scan_duration: float = Field(0.0, description="Total scan duration in seconds")
    
    # Per-detector metrics
    detector_metrics: Dict[str, Dict[str, Any]] = Field(
        default_factory=dict,
        description="Metrics per detector"
    )


class ScanSummary(BaseModel):
    """Summary statistics for a scan."""
    total_issues: int = Field(0, description="Total number of issues found")
    issues_by_severity: Dict[IssueSeverity, int] = Field(
        default_factory=dict,
        description="Count of issues by severity"
    )
    issues_by_type: Dict[IssueType, int] = Field(
        default_factory=dict,
        description="Count of issues by type"
    )
    top_files_by_issues: List[Dict[str, Union[str, int]]] = Field(
        default_factory=list,
        description="Files with most issues"
    )
    risk_distribution: Dict[str, int] = Field(
        default_factory=dict,
        description="Distribution of risk scores"
    )
    average_risk_score: float = Field(0.0, description="Average risk score across all issues")


class ScanResponse(BaseModel):
    """Response from a project scan."""
    scan_id: str = Field(..., description="Unique identifier for this scan")
    project_path: str = Field(..., description="Path that was scanned")
    scan_start: datetime = Field(..., description="When the scan started")
    scan_end: datetime = Field(..., description="When the scan completed")
    
    # Results
    issues: List[Issue] = Field(..., description="All detected issues")
    summary: ScanSummary = Field(..., description="Summary statistics")
    metrics: ScanMetrics = Field(..., description="Scan metrics")
    
    # Status
    success: bool = Field(True, description="Whether scan completed successfully")
    errors: List[str] = Field(default_factory=list, description="Any errors encountered")
    warnings: List[str] = Field(default_factory=list, description="Any warnings generated")
    
    @property
    def scan_duration(self) -> float:
        """Calculate scan duration in seconds."""
        return (self.scan_end - self.scan_start).total_seconds()
    
    def get_issues_by_severity(self, severity: IssueSeverity) -> List[Issue]:
        """Get all issues of a specific severity."""
        return [issue for issue in self.issues if issue.severity == severity]
    
    def get_issues_by_type(self, issue_type: IssueType) -> List[Issue]:
        """Get all issues of a specific type."""
        return [issue for issue in self.issues if issue.type == issue_type]
    
    def get_critical_issues(self) -> List[Issue]:
        """Get all critical severity issues."""
        return self.get_issues_by_severity(IssueSeverity.CRITICAL)
    
    def get_high_risk_issues(self, threshold: float = 70.0) -> List[Issue]:
        """Get issues with risk score above threshold."""
        return [issue for issue in self.issues if issue.risk_score >= threshold]


class DetectorResult(BaseModel):
    """Result from an individual detector."""
    detector_name: str = Field(..., description="Name of the detector")
    issues: List[Issue] = Field(..., description="Issues found by this detector")
    execution_time: float = Field(..., description="Time taken by detector in seconds")
    files_processed: int = Field(0, description="Number of files processed")
    success: bool = Field(True, description="Whether detector completed successfully")
    error_message: Optional[str] = Field(None, description="Error message if failed")


class ProjectMetadata(BaseModel):
    """Metadata about the scanned project."""
    project_name: Optional[str] = Field(None, description="Name of the project")
    project_type: Optional[str] = Field(None, description="Type of project (e.g., 'python', 'javascript')")
    languages: List[str] = Field(default_factory=list, description="Programming languages detected")
    frameworks: List[str] = Field(default_factory=list, description="Frameworks detected")
    total_files: int = Field(0, description="Total number of files in project")
    total_size: int = Field(0, description="Total size in bytes")
    git_info: Optional[Dict[str, Any]] = Field(None, description="Git repository information")
    package_managers: List[str] = Field(default_factory=list, description="Package managers found")