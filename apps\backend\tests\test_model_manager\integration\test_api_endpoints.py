"""
Integration tests for model manager API endpoints.
"""

import pytest
import json
from unittest.mock import Mock, patch, AsyncMock
from fastapi.testclient import TestClient
from fastapi import FastAP<PERSON>

from app.model_manager.app.router import router as model_router
from app.model_manager.domain.entities import Provider, ModelStatus, ModelId, Model, AvailableModel, ModelMetadata
from app.model_manager.utils.errors import ValidationError, ProviderError, NotFoundError


@pytest.fixture
def app():
    """Create FastAPI app with model manager router."""
    app = FastAPI()
    app.include_router(model_router, prefix="/models")
    return app


@pytest.fixture
def client(app):
    """Create test client."""
    return TestClient(app)


@pytest.fixture
def mock_services():
    """Mock model manager services."""
    with patch('app.model_manager.app.controllers.model_controller._catalog_service') as catalog_service, \
         patch('app.model_manager.app.controllers.model_controller._inventory_service') as inventory_service, \
         patch('app.model_manager.app.controllers.model_controller._install_service') as install_service, \
         patch('app.model_manager.app.controllers.model_controller._config_service') as config_service:
        
        yield {
            'catalog': catalog_service,
            'inventory': inventory_service,
            'install': install_service,
            'config': config_service
        }


class TestAvailableModelsEndpoint:
    """Test /models/available endpoint."""
    
    def test_list_available_success(self, client, mock_services):
        """Test successful listing of available models."""
        # Mock catalog service response
        available_models = [
            AvailableModel(
                provider=Provider.WHISPER,
                name="tiny",
                version=None,
                metadata={"size": "39 MB", "multilingual": True}
            ),
            AvailableModel(
                provider=Provider.OLLAMA,
                name="llama3",
                version="latest",
                metadata={"size_bytes": **********, "description": "Meta Llama 3"}
            )
        ]
        
        from app.model_manager.domain.entities import PagedResult
        mock_services['catalog'].list_available.return_value = PagedResult(
            items=available_models,
            total=2,
            page=1,
            page_size=50
        )
        
        response = client.get("/models/available")
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["total"] == 2
        assert len(data["items"]) == 2
        assert data["page"] == 1
        assert data["page_size"] == 50
        
        # Check first model
        whisper_model = data["items"][0]
        assert whisper_model["provider"] == "WHISPER"
        assert whisper_model["name"] == "tiny"
        assert whisper_model["metadata"]["size"] == "39 MB"
        
        # Check second model
        ollama_model = data["items"][1]
        assert ollama_model["provider"] == "OLLAMA"
        assert ollama_model["name"] == "llama3"
        assert ollama_model["version"] == "latest"
    
    def test_list_available_with_provider_filter(self, client, mock_services):
        """Test listing available models with provider filter."""
        from app.model_manager.domain.entities import PagedResult
        mock_services['catalog'].list_available.return_value = PagedResult(
            items=[],
            total=0,
            page=1,
            page_size=50
        )
        
        response = client.get("/models/available?provider=WHISPER")
        
        assert response.status_code == 200
        mock_services['catalog'].list_available.assert_called_once_with(
            Provider.WHISPER, 1, 50, None
        )
    
    def test_list_available_with_pagination(self, client, mock_services):
        """Test listing available models with pagination."""
        from app.model_manager.domain.entities import PagedResult
        mock_services['catalog'].list_available.return_value = PagedResult(
            items=[],
            total=0,
            page=2,
            page_size=25
        )
        
        response = client.get("/models/available?page=2&page_size=25")
        
        assert response.status_code == 200
        mock_services['catalog'].list_available.assert_called_once_with(
            None, 2, 25, None
        )
    
    def test_list_available_with_query(self, client, mock_services):
        """Test listing available models with search query."""
        from app.model_manager.domain.entities import PagedResult
        mock_services['catalog'].list_available.return_value = PagedResult(
            items=[],
            total=0,
            page=1,
            page_size=50
        )
        
        response = client.get("/models/available?q=llama")
        
        assert response.status_code == 200
        mock_services['catalog'].list_available.assert_called_once_with(
            None, 1, 50, "llama"
        )
    
    def test_list_available_invalid_pagination(self, client, mock_services):
        """Test listing available models with invalid pagination parameters."""
        response = client.get("/models/available?page=0")
        assert response.status_code == 422  # Validation error
        
        response = client.get("/models/available?page_size=0")
        assert response.status_code == 422
        
        response = client.get("/models/available?page_size=1000")
        assert response.status_code == 422
    
    def test_list_available_service_error(self, client, mock_services):
        """Test listing available models when service raises error."""
        mock_services['catalog'].list_available.side_effect = ProviderError(
            message="Catalog fetch failed",
            provider="OLLAMA",
            operation="CATALOG",
            cause="NETWORK"
        )
        
        response = client.get("/models/available")
        
        assert response.status_code == 400
        data = response.json()
        assert "Catalog fetch failed" in data["detail"]["message"]


class TestInstalledModelsEndpoint:
    """Test /models/installed endpoint."""
    
    def test_list_installed_success(self, client, mock_services):
        """Test successful listing of installed models."""
        installed_models = [
            Model(
                id=ModelId(provider=Provider.WHISPER, name="tiny", version=None),
                status=ModelStatus.INSTALLED,
                metadata=ModelMetadata(
                    size_on_disk_bytes=40960000,
                    installed_at="2024-01-01T12:00:00Z",
                    source="whisper_sdk",
                    extra={"catalog_version": "1.0.0"}
                )
            ),
            Model(
                id=ModelId(provider=Provider.OLLAMA, name="llama3", version="latest"),
                status=ModelStatus.INSTALLED,
                metadata=ModelMetadata(
                    size_on_disk_bytes=**********,
                    installed_at="2024-01-01T13:00:00Z",
                    source="ollama_cli"
                )
            )
        ]
        
        mock_services['inventory'].list_installed.return_value = installed_models
        
        response = client.get("/models/installed")
        
        assert response.status_code == 200
        data = response.json()
        
        assert len(data["items"]) == 2
        
        # Check first model
        whisper_model = data["items"][0]
        assert whisper_model["id"]["provider"] == "WHISPER"
        assert whisper_model["id"]["name"] == "tiny"
        assert whisper_model["status"] == "INSTALLED"
        assert whisper_model["metadata"]["size_on_disk_bytes"] == 40960000
        assert whisper_model["metadata"]["source"] == "whisper_sdk"
        
        # Check second model
        ollama_model = data["items"][1]
        assert ollama_model["id"]["provider"] == "OLLAMA"
        assert ollama_model["id"]["name"] == "llama3"
        assert ollama_model["id"]["version"] == "latest"
        assert ollama_model["status"] == "INSTALLED"
    
    def test_list_installed_empty(self, client, mock_services):
        """Test listing installed models when none are installed."""
        mock_services['inventory'].list_installed.return_value = []
        
        response = client.get("/models/installed")
        
        assert response.status_code == 200
        data = response.json()
        assert len(data["items"]) == 0
    
    def test_list_installed_service_error(self, client, mock_services):
        """Test listing installed models when service raises error."""
        mock_services['inventory'].list_installed.side_effect = Exception("Database error")
        
        response = client.get("/models/installed")
        
        assert response.status_code == 400


class TestInstallModelEndpoint:
    """Test /models/install endpoint."""
    
    def test_install_model_success(self, client, mock_services):
        """Test successful model installation."""
        installed_model = Model(
            id=ModelId(provider=Provider.WHISPER, name="tiny", version=None),
            status=ModelStatus.INSTALLED,
            metadata=ModelMetadata()
        )
        
        mock_services['install'].install.return_value = installed_model
        
        request_data = {
            "provider": "WHISPER",
            "name": "tiny",
            "version": None
        }
        
        response = client.post("/models/install", json=request_data)
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["id"]["provider"] == "WHISPER"
        assert data["id"]["name"] == "tiny"
        assert data["status"] == "INSTALLED"
        
        mock_services['install'].install.assert_called_once_with(
            Provider.WHISPER, "tiny", None
        )
    
    def test_install_model_with_version(self, client, mock_services):
        """Test model installation with specific version."""
        installed_model = Model(
            id=ModelId(provider=Provider.OLLAMA, name="llama3", version="8b"),
            status=ModelStatus.INSTALLED,
            metadata=ModelMetadata()
        )
        
        mock_services['install'].install.return_value = installed_model
        
        request_data = {
            "provider": "OLLAMA",
            "name": "llama3",
            "version": "8b"
        }
        
        response = client.post("/models/install", json=request_data)
        
        assert response.status_code == 200
        mock_services['install'].install.assert_called_once_with(
            Provider.OLLAMA, "llama3", "8b"
        )
    
    def test_install_model_invalid_request(self, client, mock_services):
        """Test model installation with invalid request data."""
        # Missing provider
        request_data = {
            "name": "tiny"
        }
        
        response = client.post("/models/install", json=request_data)
        assert response.status_code == 422
        
        # Empty name
        request_data = {
            "provider": "WHISPER",
            "name": ""
        }
        
        response = client.post("/models/install", json=request_data)
        assert response.status_code == 422
        
        # Name too long
        request_data = {
            "provider": "WHISPER",
            "name": "a" * 201
        }
        
        response = client.post("/models/install", json=request_data)
        assert response.status_code == 422
    
    def test_install_model_not_found(self, client, mock_services):
        """Test model installation when model is not found."""
        mock_services['install'].install.side_effect = NotFoundError(
            message="Model not in catalog",
            provider="WHISPER",
            operation="INSTALL",
            cause="CATALOG"
        )
        
        request_data = {
            "provider": "WHISPER",
            "name": "nonexistent"
        }
        
        response = client.post("/models/install", json=request_data)
        
        assert response.status_code == 400
        data = response.json()
        assert "Model not in catalog" in data["detail"]["message"]
    
    def test_install_model_provider_error(self, client, mock_services):
        """Test model installation when provider raises error."""
        mock_services['install'].install.side_effect = ProviderError(
            message="Installation failed",
            provider="OLLAMA",
            operation="INSTALL",
            cause="CLI_ERROR",
            details={"stderr": "Network timeout"}
        )
        
        request_data = {
            "provider": "OLLAMA",
            "name": "llama3"
        }
        
        response = client.post("/models/install", json=request_data)
        
        assert response.status_code == 400
        data = response.json()
        assert "Installation failed" in data["detail"]["message"]
        assert data["detail"]["cause"] == "CLI_ERROR"


class TestConfigureModelEndpoint:
    """Test /models/configure endpoint."""
    
    def test_configure_model_success(self, client, mock_services):
        """Test successful model configuration."""
        mock_services['config'].configure.return_value = None
        
        request_data = {
            "provider": "WHISPER",
            "name": "tiny",
            "config": {
                "device": "cuda",
                "language": "en",
                "temperature": 0.7
            }
        }
        
        response = client.post("/models/configure", json=request_data)
        
        assert response.status_code == 200
        data = response.json()
        assert data["ok"] is True
        
        mock_services['config'].configure.assert_called_once_with(
            Provider.WHISPER,
            "tiny",
            {"device": "cuda", "language": "en", "temperature": 0.7}
        )
    
    def test_configure_model_empty_config(self, client, mock_services):
        """Test model configuration with empty config."""
        mock_services['config'].configure.return_value = None
        
        request_data = {
            "provider": "WHISPER",
            "name": "tiny",
            "config": {}
        }
        
        response = client.post("/models/configure", json=request_data)
        
        assert response.status_code == 200
        mock_services['config'].configure.assert_called_once_with(
            Provider.WHISPER, "tiny", {}
        )
    
    def test_configure_model_invalid_request(self, client, mock_services):
        """Test model configuration with invalid request data."""
        # Missing provider
        request_data = {
            "name": "tiny",
            "config": {}
        }
        
        response = client.post("/models/configure", json=request_data)
        assert response.status_code == 422
        
        # Missing config
        request_data = {
            "provider": "WHISPER",
            "name": "tiny"
        }
        
        response = client.post("/models/configure", json=request_data)
        assert response.status_code == 422
    
    def test_configure_model_validation_error(self, client, mock_services):
        """Test model configuration with validation error."""
        mock_services['config'].configure.side_effect = ValidationError(
            message="Invalid temperature",
            provider="WHISPER",
            operation="CONFIGURE",
            cause="INVALID_TEMPERATURE"
        )
        
        request_data = {
            "provider": "WHISPER",
            "name": "tiny",
            "config": {"temperature": 2.0}
        }
        
        response = client.post("/models/configure", json=request_data)
        
        assert response.status_code == 400
        data = response.json()
        assert "Invalid temperature" in data["detail"]["message"]


class TestDeleteModelEndpoint:
    """Test /models/{provider}/{name} DELETE endpoint."""
    
    def test_delete_model_success(self, client, mock_services):
        """Test successful model deletion."""
        mock_services['install'].uninstall.return_value = None
        
        response = client.delete("/models/WHISPER/tiny")
        
        assert response.status_code == 200
        data = response.json()
        assert data["ok"] is True
        
        mock_services['install'].uninstall.assert_called_once_with(
            Provider.WHISPER, "tiny", None
        )
    
    def test_delete_model_with_version(self, client, mock_services):
        """Test model deletion with specific version."""
        mock_services['install'].uninstall.return_value = None
        
        response = client.delete("/models/OLLAMA/llama3?version=8b")
        
        assert response.status_code == 200
        mock_services['install'].uninstall.assert_called_once_with(
            Provider.OLLAMA, "llama3", "8b"
        )
    
    def test_delete_model_provider_error(self, client, mock_services):
        """Test model deletion when provider raises error."""
        mock_services['install'].uninstall.side_effect = ProviderError(
            message="Delete failed",
            provider="OLLAMA",
            operation="DELETE",
            cause="CLI_ERROR"
        )
        
        response = client.delete("/models/OLLAMA/llama3")
        
        assert response.status_code == 400
        data = response.json()
        assert "Delete failed" in data["detail"]["message"]


class TestHealthEndpoint:
    """Test /models/health endpoint."""
    
    def test_health_success(self, client):
        """Test successful health check."""
        with patch('app.model_manager.app.controllers.model_controller._fs') as mock_fs, \
             patch('app.model_manager.app.controllers.model_controller._proc') as mock_proc, \
             patch('app.model_manager.app.controllers.model_controller._cfg') as mock_cfg:
            
            mock_fs.ensure_dir.return_value = None
            mock_proc.which.return_value = "/usr/bin/whisper"
            mock_cfg.paths.data_root = "/tmp/data"
            mock_cfg.providers.whisper_enabled = True
            mock_cfg.providers.ollama_enabled = True
            mock_cfg.whisper.use_cli = True
            mock_cfg.whisper.executable = "whisper"
            mock_cfg.ollama.executable = "ollama"
            
            response = client.get("/models/health")
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["ok"] is True
        assert data["data_root"] == "/tmp/data"
        assert data["whisper_cli"] is True
        assert data["ollama_cli"] is True
        assert data["config_valid"] is True
    
    def test_health_missing_executables(self, client):
        """Test health check when executables are missing."""
        with patch('app.model_manager.app.controllers.model_controller._fs') as mock_fs, \
             patch('app.model_manager.app.controllers.model_controller._proc') as mock_proc, \
             patch('app.model_manager.app.controllers.model_controller._cfg') as mock_cfg:
            
            mock_fs.ensure_dir.return_value = None
            mock_proc.which.return_value = None  # Missing executables
            mock_cfg.paths.data_root = "/tmp/data"
            mock_cfg.providers.whisper_enabled = True
            mock_cfg.providers.ollama_enabled = True
            mock_cfg.whisper.use_cli = True
            mock_cfg.whisper.executable = "whisper"
            mock_cfg.ollama.executable = "ollama"
            
            response = client.get("/models/health")
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["ok"] is True
        assert data["whisper_cli"] is False
        assert data["ollama_cli"] is False
    
    def test_health_filesystem_error(self, client):
        """Test health check when filesystem operations fail."""
        with patch('app.model_manager.app.controllers.model_controller._fs') as mock_fs:
            mock_fs.ensure_dir.side_effect = Exception("Permission denied")
            
            response = client.get("/models/health")
        
        assert response.status_code == 500
        data = response.json()
        assert data["detail"]["ok"] is False
        assert "Permission denied" in data["detail"]["error"]


class TestDiagnosticsEndpoint:
    """Test /models/diagnostics endpoint."""
    
    def test_diagnostics_success(self, client):
        """Test successful diagnostics retrieval."""
        with patch('app.model_manager.app.controllers.model_controller._fs') as mock_fs, \
             patch('app.model_manager.app.controllers.model_controller._cache') as mock_cache, \
             patch('app.model_manager.app.controllers.model_controller._cfg') as mock_cfg, \
             patch.dict('os.environ', {'MODEL_MGR_STREAMLIT_PORT': '8501'}):
            
            mock_fs.size_on_disk.side_effect = lambda path: {
                "/tmp/models": **********,  # 1GB
                "/tmp/models/whisper": 500000000,  # 500MB
                "/tmp/models/ollama": 524000000   # 524MB
            }.get(path, 0)
            
            mock_cache.stats.return_value = {
                "hit_count": 150,
                "miss_count": 25,
                "total_size": 1024
            }
            
            mock_cfg.paths.models_root = "/tmp/models"
            
            response = client.get("/models/diagnostics")
        
        assert response.status_code == 200
        data = response.json()
        
        assert "models" in data
        assert data["models"]["total_bytes"] == **********
        assert data["models"]["whisper_bytes"] == 500000000
        assert data["models"]["ollama_bytes"] == 524000000
        
        assert "cache" in data
        assert data["cache"]["hit_count"] == 150
        assert data["cache"]["miss_count"] == 25
        
        assert "preview" in data
        assert "streamlit" in data["preview"]
        assert "8501" in data["preview"]["streamlit"]
    
    def test_diagnostics_error(self, client):
        """Test diagnostics when operations fail."""
        with patch('app.model_manager.app.controllers.model_controller._fs') as mock_fs:
            mock_fs.size_on_disk.side_effect = Exception("Disk access error")
            
            response = client.get("/models/diagnostics")
        
        assert response.status_code == 500
        data = response.json()
        assert data["detail"]["ok"] is False
        assert "Disk access error" in data["detail"]["error"]
