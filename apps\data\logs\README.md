# LONI Platform Logging

This directory contains logs from all LONI platform services:

## Log File Naming Convention

- `backend-{service}.log` - Backend service logs
- `frontend-{service}.log` - Frontend service logs
- `infra-{service}.log` - Infrastructure service logs
- `devtools-{task}.log` - Development tool execution logs

## Log Rotation

Logs are rotated daily and compressed after 7 days. Old logs are purged after 30 days.

## Best Practices

- All development tools should output to log files in this directory
- Logs should be structured JSON when possible
- Sensitive information should be redacted from logs
- Errors should be clearly identifiable and traceable