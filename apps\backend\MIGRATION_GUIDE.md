# Backend Modular Architecture Migration Guide

## Overview

This guide provides step-by-step instructions for migrating the backend from a monolithic architecture to the new modular, n8n-like architecture. The modular architecture transforms each business logic component into a self-contained "node" with clear interfaces, configuration, and dependencies.

## Migration Steps

### Phase 1: Preparation (1-2 days)

#### 1.1 Understand Current Architecture
Analyze existing code structure:
```bash
# Examine current structure
find apps/backend/app -name "*.py" -type f | head -20

# Review main dependencies
cat apps/backend/pyproject.toml
```

#### 1.2 Set Up Module Structure
Create the new modular architecture:
```bash
# Create modules directory structure
mkdir -p apps/backend/app/modules

# Copy base module system
cp -r path/to/new_modules/* apps/backend/app/modules/
```

#### 1.3 Install Dependencies
Update requirements if needed:
```bash
# Add any new dependencies to pyproject.toml
# Most existing dependencies should work with modules
```

### Phase 2: Core Module System (2-3 days)

#### 2.1 Create Base Module Classes
The core module system is already implemented in `apps/backend/app/modules/__init__.py`. Review the key classes:

- `BaseModule`: Abstract base class for all modules
- `ModuleRegistry`: Manages module registration and lifecycle
- `ModuleFactory`: Creates module instances and configurations

#### 2.2 Define Module Types
Current module types:
- `MODEL_MANAGER`: Ollama model operations
- `TRANSCRIPTION`: Whisper audio transcription
- `SCANNER`: Project scanning and analysis

#### 2.3 Set Up Data Contracts
Standardize input/output data structures:
```python
@dataclass
class ModuleInput:
    data: Dict[str, Any]
    metadata: Dict[str, Any] = None
    context: Dict[str, Any] = None

@dataclass
class ModuleOutput:
    success: bool
    data: Dict[str, Any] = None
    error: str = None
    metadata: Dict[str, Any] = None
```

### Phase 3: Convert Services to Modules (3-5 days)

#### 3.1 Extract Model Manager Module
Convert `ModelManager` class to modular format:

**Before:**
```python
class ModelManager:
    def __init__(self):
        self.host = settings.ollama_host  # Hardcoded!
        self.current_model = None

    def list_models(self):
        # Implementation...
```

**After:**
```python
class OllamaModelModule(BaseModule):
    async def initialize(self, config: ModuleConfig) -> bool:
        self.host = config.settings['host']  # Configurable!
        return True

    async def execute(self, input_data: ModuleInput) -> ModuleOutput:
        action = input_data.data.get('action')
        if action == 'list':
            return await self._list_models()
        # Handle other actions...
```

#### 3.2 Extract Transcription Module
Convert Whisper service to modular format:

**Before:**
```python
class WhisperService:
    def __init__(self, model_name=None):
        self.model_name = model_name or settings.whisper_model
        self.model = None
        self._load_model()
```

**After:**
```python
class WhisperTranscriptionModule(BaseModule):
    async def initialize(self, config: ModuleConfig) -> bool:
        self.model_name = config.settings['model_name']
        self._load_model()
        return True
```

#### 3.3 Extract Scanner Module
Convert project scanner to modular format:

**Before:**
```python
class ProjectScanner:
    def __init__(self):
        self.detectors = {}
        self._initialize_detectors()
```

**After:**
```python
class ProjectScannerModule(BaseModule):
    async def initialize(self, config: ModuleConfig) -> bool:
        self.config = config
        return True
```

### Phase 4: Update FastAPI Application (2-3 days)

#### 4.1 Create Modular App Version
Replace monolithic app with modular version:

**Before:**
```python
# apps/backend/app/app.py
def create_app() -> FastAPI:
    app = FastAPI(...)
    # Direct route registration
    @app.get("/api/v1/models")
    async def list_models():
        return await get_model_manager().list_models()
    return app
```

**After:**
```python
# apps/backend/app/app_modular.py
async def create_modular_app() -> FastAPI:
    app = FastAPI(...)
    await initialize_modules()

    @app.get("/api/v1/models")
    async def list_models():
        model_manager = registry.get("ollama_manager")
        result = await model_manager.execute(input_data)
        return result.data
    return app
```

#### 4.2 Update Application Entry Point
Modify main.py to use modular app:
```python
# apps/backend/app/main.py
from app.app_modular import create_modular_app

app = await create_modular_app()  # Async creation
```

#### 4.3 Set Up Module Configuration
Create module configurations:
```python
# Configuration for all modules
configs = [
    ModuleConfig(
        name="ollama_manager",
        type=ModuleType.MODEL_MANAGER,
        settings={'host': settings.ollama_host}
    ),
    ModuleConfig(
        name="whisper_transcriber",
        type=ModuleType.TRANSCRIPTION,
        settings={'model_name': settings.whisper_model}
    ),
    ModuleConfig(
        name="project_scanner",
        type=ModuleType.SCANNER,
        settings={'enable_code_quality': True}
    )
]
```

### Phase 5: Dependency Injection (1-2 days)

#### 5.1 Set Up Dependencies
Configure module dependencies:
```python
# Set up dependencies between modules
data_provider.set_dependency('api_client', api_client)

# Dependencies are automatically resolved
dependencies = module.get_dependencies()
```

#### 5.2 Update Import Statements
Replace direct service imports:
```python
# Before
from app.models.model_manager import ModelManager
model_manager = ModelManager()

# After
from app.modules import registry
model_manager = registry.get("ollama_manager")
```

### Phase 6: Configuration Management (1-2 days)

#### 6.1 Remove Hardcoded Values
Replace all hardcoded values with configuration:

**Before:**
```python
class WhisperService:
    def __init__(self):
        self.model_name = "base"  # Hardcoded!
        self.device = "cpu"       # Hardcoded!
```

**After:**
```python
class WhisperTranscriptionModule(BaseModule):
    async def initialize(self, config: ModuleConfig) -> bool:
        self.model_name = config.settings['model_name']  # Configurable!
        self.device = config.settings.get('device', 'cpu')  # Configurable!
```

#### 6.2 Environment-Based Configuration
Create environment-specific configurations:
```python
# config/production.py
PRODUCTION_MODULES = [
    ModuleConfig(
        name="prod_ollama",
        type=ModuleType.MODEL_MANAGER,
        settings={'host': 'production-ollama:11434'}
    )
]

# config/development.py
DEVELOPMENT_MODULES = [
    ModuleConfig(
        name="dev_ollama",
        type=ModuleType.MODEL_MANAGER,
        settings={'host': 'localhost:11434'}
    )
]
```

### Phase 7: Testing and Validation (2-3 days)

#### 7.1 Unit Tests for Modules
Create tests for individual modules:
```python
# tests/test_modules.py
async def test_ollama_model_module():
    config = ModuleConfig(
        name="test_ollama",
        type=ModuleType.MODEL_MANAGER,
        settings={'host': 'localhost:11434'}
    )

    module = OllamaModelModule("test_ollama")
    await module.initialize(config)

    input_data = ModuleInput(data={"action": "list"})
    result = await module.execute(input_data)

    assert result.success
    assert "models" in result.data
```

#### 7.2 Integration Tests
Test module interactions:
```python
# tests/test_integration.py
async def test_module_integration():
    # Set up modules
    model_module = OllamaModelModule("test_model")
    transcription_module = WhisperTranscriptionModule("test_transcription")

    # Test interaction
    result = await model_module.execute(input_data)
    # Verify transcription module can use model results
```

#### 7.3 API Endpoint Tests
Test FastAPI endpoints with modules:
```python
# tests/test_api.py
def test_list_models_endpoint():
    response = client.get("/api/v1/models")
    assert response.status_code == 200
    assert "models" in response.json()
```

### Phase 8: Deployment and Monitoring (1-2 days)

#### 8.1 Update Deployment Scripts
Modify Docker and deployment configurations:
```dockerfile
# Dockerfile
FROM python:3.11
COPY apps/backend/app/modules /app/modules
COPY apps/backend/app/app_modular.py /app/main.py
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]
```

#### 8.2 Set Up Monitoring
Add module-specific monitoring:
```python
# Add to startup
@app.on_event("startup")
async def startup_event():
    # Initialize modules
    await initialize_modules()

    # Set up monitoring
    for module in registry.list_all():
        logger.info(f"Module {module.name} initialized", {
            'type': module.type,
            'status': module.status
        })
```

#### 8.3 Health Checks
Implement module health checks:
```python
@app.get("/health/modules")
async def module_health():
    health_status = {}
    for module in registry.list_all():
        health_status[module.name] = {
            'status': module.status.value,
            'type': module.type.value
        }
    return health_status
```

## Migration Checklist

### ✅ Phase 1: Preparation
- [ ] Analyze current architecture
- [ ] Set up module directory structure
- [ ] Update dependencies

### ✅ Phase 2: Core System
- [ ] Implement base module classes
- [ ] Define module types and interfaces
- [ ] Set up data contracts

### ✅ Phase 3: Service Conversion
- [ ] Convert ModelManager to OllamaModelModule
- [ ] Convert WhisperService to WhisperTranscriptionModule
- [ ] Convert ProjectScanner to ProjectScannerModule

### ✅ Phase 4: Application Updates
- [ ] Create modular FastAPI app
- [ ] Update application entry point
- [ ] Configure all modules

### ✅ Phase 5: Dependencies
- [ ] Set up dependency injection
- [ ] Update import statements
- [ ] Verify dependency resolution

### ✅ Phase 6: Configuration
- [ ] Remove hardcoded values
- [ ] Implement environment-based configuration
- [ ] Validate configuration loading

### ✅ Phase 7: Testing
- [ ] Create unit tests for modules
- [ ] Implement integration tests
- [ ] Test API endpoints

### ✅ Phase 8: Deployment
- [ ] Update deployment scripts
- [ ] Set up monitoring
- [ ] Implement health checks

## Troubleshooting

### Common Issues

**Module Initialization Fails**
```python
# Check module configuration
config = registry.get_config("module_name")
print(f"Config: {config}")

# Verify dependencies
dependencies = module.get_dependencies()
print(f"Dependencies: {dependencies}")
```

**Dependency Resolution Issues**
```python
# Check if dependencies are registered
for dep_name in module.get_dependencies():
    dependency = registry.get(dep_name)
    if not dependency:
        print(f"Missing dependency: {dep_name}")

# Set dependencies explicitly
module.set_dependency('api_client', api_client)
```

**Configuration Errors**
```python
# Validate configuration
try:
    await module.initialize(config)
    print("Module initialized successfully")
except Exception as e:
    print(f"Initialization failed: {e}")

# Check required settings
required_settings = ['host', 'api_timeout']
for setting in required_settings:
    if setting not in config.settings:
        print(f"Missing setting: {setting}")
```

### Performance Issues

**Slow Module Loading**
- Implement lazy loading for heavy modules
- Use connection pooling
- Cache module instances

**Memory Leaks**
- Ensure proper cleanup in `module.destroy()`
- Monitor module lifecycle
- Use weak references for dependencies

**High CPU Usage**
- Implement request throttling
- Use async/await properly
- Monitor thread pool usage

## Rollback Plan

### Emergency Rollback
If issues occur, rollback to monolithic architecture:

1. **Switch Entry Point**
```python
# apps/backend/app/main.py
# from app.app_modular import create_modular_app
from app.app import create_app

# app = await create_modular_app()
app = create_app()
```

2. **Restore Original Routes**
```python
# Keep original app.py alongside modular version
# Switch imports back to original services
```

3. **Revert Configuration**
```python
# Use original configuration files
# Remove modular configuration
```

### Gradual Rollback
For partial rollback of specific features:

1. **Route-Level Rollback**
```python
# Use monolithic service for specific routes
@app.get("/api/v1/models")
async def list_models():
    # Use original service temporarily
    return await original_model_service.list_models()
```

2. **Module-Level Rollback**
```python
# Disable problematic modules
config = ModuleConfig(
    name="problematic_module",
    type=ModuleType.SERVICE,
    enabled=False  # Disable without removing
)
```

## Best Practices

### Module Development
- Keep modules focused on single responsibilities
- Use clear, descriptive names
- Document inputs, outputs, and configuration
- Handle errors gracefully

### Configuration Management
- Never hardcode values in modules
- Use configuration objects for all settings
- Validate configuration at initialization
- Support environment-specific configurations

### Testing Strategy
- Test each module in isolation
- Mock dependencies for unit tests
- Test integration between modules
- Validate input/output contracts

### Monitoring and Logging
- Log all module operations
- Monitor module health and performance
- Implement structured logging
- Set up alerts for module failures

This migration guide provides a comprehensive roadmap for transforming the backend architecture. The modular approach will significantly improve maintainability, testability, and scalability of the application.
