"""
Unit tests for Ollama provider implementation.
"""

import pytest
from unittest.mock import Mock, patch, call
from datetime import datetime

from app.model_manager.domain.entities import Provider, ModelStatus, ModelId, Pagination, PagedResult
from app.model_manager.infra.providers.ollama_provider import OllamaProviderImpl
from app.model_manager.utils.errors import ValidationError, ProviderError, NotFoundError


class TestOllamaProviderCatalog:
    """Test Ollama provider catalog operations."""
    
    def test_get_catalog_when_enabled(self, ollama_provider, mock_config, mock_cache):
        """Test catalog retrieval when Ollama is enabled."""
        mock_config.providers.ollama_enabled = True
        mock_cache.get.return_value = None  # No cache
        
        with patch.object(ollama_provider, '_fetch_catalog', return_value=[
            Mock(provider=Provider.OLLAMA, name="llama3", version=None, metadata={"source": "web"}),
            Mock(provider=Provider.OLLAMA, name="mistral", version=None, metadata={"source": "web"})
        ]):
            result = ollama_provider.get_catalog()
        
        assert result.total == 2
        assert len(result.items) == 2
        assert result.items[0].name == "llama3"
        assert result.items[1].name == "mistral"
    
    def test_get_catalog_when_disabled(self, ollama_provider, mock_config):
        """Test catalog retrieval when Ollama is disabled."""
        mock_config.providers.ollama_enabled = False
        
        result = ollama_provider.get_catalog()
        
        assert result.total == 0
        assert len(result.items) == 0
    
    def test_get_catalog_with_pagination(self, ollama_provider, mock_config, mock_cache):
        """Test catalog with pagination."""
        mock_config.providers.ollama_enabled = True
        mock_cache.get.return_value = None
        
        # Mock 10 models
        models = [Mock(provider=Provider.OLLAMA, name=f"model-{i}", version=None, metadata={}) for i in range(10)]
        
        with patch.object(ollama_provider, '_fetch_catalog', return_value=models):
            pagination = Pagination(page=1, page_size=5)
            result = ollama_provider.get_catalog(pagination)
        
        assert result.total == 10
        assert len(result.items) == 5
        assert result.page == 1
        assert result.page_size == 5
    
    def test_get_catalog_with_query_filter(self, ollama_provider, mock_config, mock_cache):
        """Test catalog with query filtering."""
        mock_config.providers.ollama_enabled = True
        mock_cache.get.return_value = None
        
        models = [
            Mock(provider=Provider.OLLAMA, name="llama3", version=None, metadata={}),
            Mock(provider=Provider.OLLAMA, name="mistral", version=None, metadata={}),
            Mock(provider=Provider.OLLAMA, name="codellama", version=None, metadata={})
        ]
        
        with patch.object(ollama_provider, '_fetch_catalog', return_value=models):
            result = ollama_provider.get_catalog(query="llama")
        
        assert result.total == 2  # llama3 and codellama
        assert len(result.items) == 2
        assert all("llama" in item.name.lower() for item in result.items)
    
    def test_get_catalog_cached(self, ollama_provider, mock_config, mock_cache):
        """Test catalog retrieval from cache."""
        mock_config.providers.ollama_enabled = True
        
        cached_data = {
            "items": [
                {"provider": "OLLAMA", "name": "llama3", "version": None, "metadata": {"source": "cache"}},
                {"provider": "OLLAMA", "name": "mistral", "version": None, "metadata": {"source": "cache"}}
            ]
        }
        mock_cache.get.return_value = cached_data
        
        result = ollama_provider.get_catalog()
        
        assert result.total == 2
        assert result.items[0].metadata["source"] == "cache"
    
    def test_fetch_catalog_cli_mode(self, ollama_provider, mock_config, mock_process, sample_ollama_models):
        """Test catalog fetching in CLI mode."""
        mock_config.ollama.catalog_source = "cli"
        mock_config.ollama.executable = "ollama"
        mock_process.which.return_value = "/usr/bin/ollama"
        
        result = Mock()
        result.exit_code = 0
        result.stdout = sample_ollama_models
        mock_process.run.return_value = result
        
        with patch('urllib.request.urlopen') as mock_urlopen:
            mock_response = Mock()
            mock_response.read.return_value = b"<html></html>"
            mock_urlopen.return_value.__enter__.return_value = mock_response
            
            models = ollama_provider._fetch_catalog()
        
        # Should have models from CLI output
        cli_models = [m for m in models if m.metadata.get("source") == "cli"]
        assert len(cli_models) >= 3  # llama3, mistral, codellama
        assert any(m.name == "llama3:latest" for m in cli_models)
    
    def test_fetch_catalog_web_mode(self, ollama_provider, mock_config, sample_ollama_html):
        """Test catalog fetching from web."""
        mock_config.ollama.catalog_source = "web"
        
        with patch('urllib.request.urlopen') as mock_urlopen:
            mock_response = Mock()
            mock_response.read.return_value = sample_ollama_html.encode('utf-8')
            mock_urlopen.return_value.__enter__.return_value = mock_response
            
            models = ollama_provider._fetch_catalog()
        
        # Should have models from HTML parsing
        web_models = [m for m in models if m.metadata.get("source") == "web"]
        assert len(web_models) >= 5  # llama3, mistral, codellama, phi, gemma
        assert any(m.name == "llama3" for m in web_models)
        assert any(m.name == "mistral" for m in web_models)
    
    def test_fetch_catalog_web_failure(self, ollama_provider, mock_config):
        """Test catalog fetching when web request fails."""
        mock_config.ollama.catalog_source = "web"
        
        with patch('urllib.request.urlopen', side_effect=Exception("Network error")):
            models = ollama_provider._fetch_catalog()
        
        # Should return empty list on failure
        assert len(models) == 0


class TestOllamaProviderValidation:
    """Test Ollama provider validation methods."""
    
    def test_validate_name_valid(self, ollama_provider):
        """Test validation with valid model names."""
        # Should not raise
        ollama_provider._validate_name("llama3")
        ollama_provider._validate_name("mistral:7b")
        ollama_provider._validate_name("namespace/model")
        ollama_provider._validate_name("model-name")
        ollama_provider._validate_name("model_name")
        ollama_provider._validate_name("model.name")
    
    def test_validate_name_invalid(self, ollama_provider):
        """Test validation with invalid model names."""
        with pytest.raises(ValidationError) as exc_info:
            ollama_provider._validate_name("")
        assert "Empty model name" in str(exc_info.value)
        
        with pytest.raises(ValidationError) as exc_info:
            ollama_provider._validate_name("model with spaces")
        assert "Invalid model name" in str(exc_info.value)
        
        with pytest.raises(ValidationError) as exc_info:
            ollama_provider._validate_name("model@invalid")
        assert "Invalid model name" in str(exc_info.value)
    
    def test_validate_model_success(self, ollama_provider, mock_config, mock_process):
        """Test successful model validation."""
        mock_config.ollama.executable = "ollama"
        mock_process.which.return_value = "/usr/bin/ollama"
        
        result = Mock()
        result.exit_code = 0
        result.stdout = "Model info"
        mock_process.run.return_value = result
        
        valid, message = ollama_provider.validate("llama3")
        
        assert valid is True
        assert message == "ok"
        mock_process.run.assert_called_once_with(["ollama", "show", "llama3"], timeout_sec=600)
    
    def test_validate_model_missing_cli(self, ollama_provider, mock_config, mock_process):
        """Test validation failure when CLI is missing."""
        mock_config.ollama.executable = "ollama"
        mock_process.which.return_value = None
        
        valid, message = ollama_provider.validate("llama3")
        
        assert valid is False
        assert message == "ollama_missing"
    
    def test_validate_model_show_failed(self, ollama_provider, mock_config, mock_process):
        """Test validation failure when show command fails."""
        mock_config.ollama.executable = "ollama"
        mock_process.which.return_value = "/usr/bin/ollama"
        
        result = Mock()
        result.exit_code = 1
        result.stderr = "Model not found"
        mock_process.run.return_value = result
        
        valid, message = ollama_provider.validate("nonexistent")
        
        assert valid is False
        assert message == "ollama_show_failed"


class TestOllamaProviderInstallation:
    """Test Ollama provider installation methods."""
    
    def test_install_success(self, ollama_provider, mock_config, mock_process, mock_filesystem):
        """Test successful model installation."""
        mock_config.ollama.executable = "ollama"
        mock_process.which.return_value = "/usr/bin/ollama"
        mock_filesystem.acquire_lock.return_value = True
        mock_filesystem.exists.return_value = False  # No existing manifest
        
        result = Mock()
        result.exit_code = 0
        result.stdout = "Success"
        mock_process.run.return_value = result
        
        model = ollama_provider.install("llama3", "latest")
        
        assert model.id.provider == Provider.OLLAMA
        assert model.id.name == "llama3"
        assert model.id.version == "latest"
        assert model.status == ModelStatus.INSTALLED
        
        mock_process.run.assert_called_once_with(["ollama", "pull", "llama3:latest"], timeout_sec=600)
        mock_filesystem.write_json_atomic.assert_called_once()
    
    def test_install_without_version(self, ollama_provider, mock_config, mock_process, mock_filesystem):
        """Test installation without specifying version."""
        mock_config.ollama.executable = "ollama"
        mock_process.which.return_value = "/usr/bin/ollama"
        mock_filesystem.acquire_lock.return_value = True
        mock_filesystem.exists.return_value = False
        
        result = Mock()
        result.exit_code = 0
        mock_process.run.return_value = result
        
        model = ollama_provider.install("llama3")
        
        assert model.id.name == "llama3"
        assert model.id.version is None
        mock_process.run.assert_called_once_with(["ollama", "pull", "llama3"], timeout_sec=600)
    
    def test_install_missing_cli(self, ollama_provider, mock_config, mock_process):
        """Test installation failure when CLI is missing."""
        mock_config.ollama.executable = "ollama"
        mock_process.which.return_value = None
        
        with pytest.raises(ProviderError) as exc_info:
            ollama_provider.install("llama3")
        
        assert "Ollama CLI not available" in str(exc_info.value)
        assert exc_info.value.cause == "ENOENT"
    
    def test_install_pull_failed(self, ollama_provider, mock_config, mock_process, mock_filesystem):
        """Test installation failure when pull command fails."""
        mock_config.ollama.executable = "ollama"
        mock_process.which.return_value = "/usr/bin/ollama"
        mock_filesystem.acquire_lock.return_value = True
        mock_filesystem.exists.return_value = False
        
        result = Mock()
        result.exit_code = 1
        result.stderr = "Pull failed"
        mock_process.run.return_value = result
        
        with pytest.raises(ProviderError) as exc_info:
            ollama_provider.install("llama3")
        
        assert "Ollama pull failed" in str(exc_info.value)
        assert exc_info.value.cause == "EXIT_CODE"
        assert exc_info.value.details["code"] == 1
        assert "Pull failed" in exc_info.value.details["stderr"]
    
    def test_install_lock_unavailable(self, ollama_provider, mock_filesystem):
        """Test installation failure when lock is unavailable."""
        mock_filesystem.acquire_lock.return_value = False
        
        with pytest.raises(ProviderError) as exc_info:
            ollama_provider.install("llama3")
        
        assert "Model is locked" in str(exc_info.value)
        assert exc_info.value.cause == "LOCKED"
    
    def test_install_idempotent_already_installed(self, ollama_provider, mock_filesystem, sample_manifest):
        """Test idempotent installation when model is already installed."""
        mock_filesystem.exists.return_value = True
        mock_filesystem.acquire_lock.return_value = True
        
        ollama_manifest = {
            "provider": "OLLAMA",
            "name": "llama3",
            "version": "latest",
            "installed_at": "2024-01-01T12:00:00Z",
            "source": "ollama_cli",
            "checksum": None
        }
        mock_filesystem.read_json.return_value = ollama_manifest
        
        with patch.object(ollama_provider, 'validate', return_value=(True, "ok")):
            model = ollama_provider.install("llama3", "latest")
        
        assert model.status == ModelStatus.INSTALLED
        # Should not call ollama pull
        mock_filesystem.write_json_atomic.assert_not_called()


class TestOllamaProviderConfiguration:
    """Test Ollama provider configuration methods."""
    
    def test_configure_success(self, ollama_provider, mock_filesystem):
        """Test successful model configuration."""
        config = {
            "temperature": 0.8,
            "top_p": 0.9,
            "max_tokens": 2048,
            "stop": ["\n\n"]
        }
        
        ollama_provider.configure("llama3", config)
        
        mock_filesystem.write_json_atomic.assert_called_once()
        call_args = mock_filesystem.write_json_atomic.call_args
        saved_config = call_args[0][1]
        
        assert saved_config["provider"] == "OLLAMA"
        assert saved_config["name"] == "llama3"
        assert saved_config["config"] == config
    
    def test_configure_empty_config(self, ollama_provider, mock_filesystem):
        """Test configuration with empty config."""
        ollama_provider.configure("llama3", {})
        
        mock_filesystem.write_json_atomic.assert_called_once()
        call_args = mock_filesystem.write_json_atomic.call_args
        saved_config = call_args[0][1]
        
        assert saved_config["config"] == {}


class TestOllamaProviderUninstall:
    """Test Ollama provider uninstall methods."""
    
    def test_uninstall_success(self, ollama_provider, mock_config, mock_process, mock_filesystem):
        """Test successful model uninstallation."""
        mock_config.ollama.executable = "ollama"
        mock_process.which.return_value = "/usr/bin/ollama"
        mock_filesystem.exists.return_value = True
        
        result = Mock()
        result.exit_code = 0
        mock_process.run.return_value = result
        
        ollama_provider.uninstall("llama3:latest")
        
        mock_process.run.assert_called_once_with(["ollama", "rm", "llama3:latest"], timeout_sec=600)
        mock_filesystem.remove_dir_safe.assert_called_once()
    
    def test_uninstall_model_not_found_tolerated(self, ollama_provider, mock_config, mock_process, mock_filesystem):
        """Test uninstall when model is not found (should be tolerated)."""
        mock_config.ollama.executable = "ollama"
        mock_process.which.return_value = "/usr/bin/ollama"
        mock_filesystem.exists.return_value = True
        
        result = Mock()
        result.exit_code = 1
        result.stderr = "model not found"
        mock_process.run.return_value = result
        
        # Should not raise error for "not found"
        ollama_provider.uninstall("nonexistent")
        
        mock_filesystem.remove_dir_safe.assert_called_once()
    
    def test_uninstall_other_error(self, ollama_provider, mock_config, mock_process, mock_filesystem):
        """Test uninstall failure for other errors."""
        mock_config.ollama.executable = "ollama"
        mock_process.which.return_value = "/usr/bin/ollama"
        mock_filesystem.exists.return_value = True
        
        result = Mock()
        result.exit_code = 1
        result.stderr = "permission denied"
        mock_process.run.return_value = result
        
        with pytest.raises(ProviderError) as exc_info:
            ollama_provider.uninstall("llama3")
        
        assert "Ollama remove failed" in str(exc_info.value)
        assert exc_info.value.cause == "EXIT_CODE"
    
    def test_uninstall_missing_cli_warning(self, ollama_provider, mock_config, mock_process, mock_filesystem):
        """Test uninstall when CLI is missing (should warn but continue)."""
        mock_config.ollama.executable = "ollama"
        mock_process.which.return_value = None
        mock_filesystem.exists.return_value = True
        
        # Should not raise error, just clean local artifacts
        ollama_provider.uninstall("llama3")
        
        mock_filesystem.remove_dir_safe.assert_called_once()
    
    def test_uninstall_idempotent(self, ollama_provider, mock_config, mock_process, mock_filesystem):
        """Test that uninstall is idempotent."""
        mock_config.ollama.executable = "ollama"
        mock_process.which.return_value = "/usr/bin/ollama"
        mock_filesystem.exists.return_value = False  # No local artifacts
        
        result = Mock()
        result.exit_code = 0
        mock_process.run.return_value = result
        
        # Should not raise error
        ollama_provider.uninstall("llama3")
        
        mock_filesystem.remove_dir_safe.assert_called_once()  # Still attempts cleanup


class TestOllamaProviderHelpers:
    """Test Ollama provider helper methods."""
    
    def test_model_dir_path_generation(self, ollama_provider, mock_filesystem, temp_dir):
        """Test model directory path generation."""
        mock_filesystem.join_data_path.side_effect = lambda *args: os.path.join(temp_dir, *args)
        
        # Test with simple name
        path = ollama_provider._model_dir("llama3")
        expected = os.path.join(temp_dir, "models", "ollama", "llama3")
        assert path == expected
        
        # Test with version (colon should be replaced)
        path = ollama_provider._model_dir("llama3:latest")
        expected = os.path.join(temp_dir, "models", "ollama", "llama3_latest")
        assert path == expected
    
    def test_manifest_path_generation(self, ollama_provider, mock_filesystem, temp_dir):
        """Test manifest path generation."""
        mock_filesystem.join_data_path.side_effect = lambda *args: os.path.join(temp_dir, *args)
        
        path = ollama_provider._manifest_path("llama3")
        expected = os.path.join(temp_dir, "models", "ollama", "llama3", "manifest.json")
        assert path == expected
    
    def test_runtime_path_generation(self, ollama_provider, mock_filesystem, temp_dir):
        """Test runtime config path generation."""
        mock_filesystem.join_data_path.side_effect = lambda *args: os.path.join(temp_dir, *args)
        
        path = ollama_provider._runtime_path("llama3")
        expected = os.path.join(temp_dir, "models", "ollama", "llama3", "runtime.config.json")
        assert path == expected
    
    def test_metadata_from_manifest(self, ollama_provider, mock_filesystem):
        """Test metadata extraction from manifest."""
        manifest = {
            "provider": "OLLAMA",
            "name": "llama3",
            "version": "latest",
            "installed_at": "2024-01-01T12:00:00Z",
            "source": "ollama_cli",
            "checksum": "abc123"
        }
        mock_filesystem.read_json.return_value = manifest
        
        metadata = ollama_provider._metadata_from_manifest("/path/to/manifest.json", 5120000)
        
        assert metadata.size_on_disk_bytes == 5120000
        assert metadata.source == "ollama_cli"
        assert metadata.checksum == "abc123"
        assert isinstance(metadata.installed_at, datetime)
