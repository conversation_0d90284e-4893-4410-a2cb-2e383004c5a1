#!/bin/bash
# Automated documentation generation script
# This script generates API documentation and project documentation

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/../.." && pwd)"
BACKEND_DIR="$PROJECT_ROOT/apps/backend"
FRONTEND_DIR="$PROJECT_ROOT/apps/frontend"
DOCS_DIR="$PROJECT_ROOT/docs"
LOG_FILE="$PROJECT_ROOT/apps/data/logs/doc-generation.log"

# Logging function
log_message() {
    local level=$1
    local message=$2
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo -e "${!level}[$level] $timestamp: $message${NC}" | tee -a "$LOG_FILE"
}

info_message() {
    log_message "BLUE" "$1"
}

success_message() {
    log_message "GREEN" "$1"
}

warning_message() {
    log_message "YELLOW" "$1"
}

error_message() {
    log_message "RED" "$1"
}

# Create directories
mkdir -p "$PROJECT_ROOT/apps/data/logs"
mkdir -p "$DOCS_DIR/api"
mkdir -p "$DOCS_DIR/frontend"
mkdir -p "$DOCS_DIR/backend"

info_message "Starting documentation generation..."

# Generate Python API documentation
generate_python_docs() {
    info_message "Generating Python API documentation..."
    
    cd "$BACKEND_DIR"
    
    # Create Sphinx documentation
    if [[ ! -f docs/conf.py ]]; then
        info_message "Initializing Sphinx documentation..."
        mkdir -p docs
        
        # Create Sphinx configuration
        cat > docs/conf.py << 'EOF'
import os
import sys
sys.path.insert(0, os.path.abspath('../app'))

# Configuration file for the Sphinx documentation builder.
project = 'LONI Backend API'
copyright = '2024, LONI Team'
author = 'LONI Team'
release = '0.1.0'

extensions = [
    'sphinx.ext.autodoc',
    'sphinx.ext.viewcode',
    'sphinx.ext.napoleon',
    'sphinx_rtd_theme',
    'autoapi.extension',
]

autoapi_dirs = ['../app']
autoapi_type = 'python'
autoapi_file_patterns = ['*.py']
autoapi_options = [
    'members',
    'undoc-members',
    'show-inheritance',
    'show-module-summary',
    'special-members',
    'imported-members',
]

templates_path = ['_templates']
exclude_patterns = ['_build', 'Thumbs.db', '.DS_Store']

html_theme = 'sphinx_rtd_theme'
html_static_path = ['_static']

# Napoleon settings
napoleon_google_docstring = True
napoleon_numpy_docstring = True
napoleon_include_init_with_doc = False
napoleon_include_private_with_doc = False
napoleon_include_special_with_doc = True
napoleon_use_admonition_for_examples = False
napoleon_use_admonition_for_notes = False
napoleon_use_admonition_for_references = False
napoleon_use_ivar = False
napoleon_use_param = True
napoleon_use_rtype = True
EOF
        
        # Create main index
        cat > docs/index.rst << 'EOF'
LONI Backend API Documentation
=============================

Welcome to the LONI Backend API documentation.

.. toctree::
   :maxdepth: 2
   :caption: Contents:

   autoapi/index

API Reference
=============

This documentation is automatically generated from the source code.

Indices and tables
==================

* :ref:`genindex`
* :ref:`modindex`
* :ref:`search`
EOF
    fi
    
    # Generate documentation
    if uv run sphinx-build -b html docs/ "$DOCS_DIR/api/backend" 2>/dev/null; then
        success_message "Python API documentation generated successfully"
    else
        warning_message "Python API documentation generation had issues"
    fi
    
    # Generate OpenAPI spec
    info_message "Generating OpenAPI specification..."
    if uv run python -c "
import json
from app.main import app

try:
    openapi_spec = app.openapi()
    with open('$DOCS_DIR/api/openapi.json', 'w') as f:
        json.dump(openapi_spec, f, indent=2)
    print('OpenAPI specification generated')
except Exception as e:
    print(f'Error generating OpenAPI spec: {e}')
"; then
        success_message "OpenAPI specification generated"
    else
        warning_message "OpenAPI specification generation failed"
    fi
}

# Generate TypeScript documentation
generate_typescript_docs() {
    info_message "Generating TypeScript documentation..."
    
    cd "$FRONTEND_DIR"
    
    # Install typedoc if needed
    if ! command -v typedoc &> /dev/null; then
        info_message "Installing TypeDoc..."
        bun add -D typedoc
    fi
    
    # Generate TypeScript documentation
    if bun run typedoc --out "$DOCS_DIR/frontend" src/ 2>/dev/null; then
        success_message "TypeScript documentation generated successfully"
    else
        warning_message "TypeScript documentation generation had issues"
    fi
}

# Generate README files
generate_readme_files() {
    info_message "Generating README files..."
    
    # Main project README
    if [[ ! -f "$PROJECT_ROOT/README.md" ]]; then
        cat > "$PROJECT_ROOT/README.md" << 'EOF'
# LONI - Software Visualization Platform

A modern, scalable platform for software visualization and analysis.

## 🚀 Quick Start

### Prerequisites

- Python 3.11+
- Node.js 18+
- Bun package manager
- Docker (optional)

### Setup

```bash
# Run the automated setup script
./scripts/devtools/setup-dev-environment.sh

# Or manually:
# Backend
cd apps/backend
uv sync --dev

# Frontend
cd apps/frontend
bun install
```

### Development

```bash
# Backend
cd apps/backend
uv run uvicorn app.main:app --reload

# Frontend
cd apps/frontend
bun run dev
```

## 🏗 Architecture

- **Backend**: FastAPI with uv package management
- **Frontend**: Next.js with Bun and TypeScript
- **Database**: PostgreSQL with async support
- **Infrastructure**: Docker containers with optimized builds

## 📚 Documentation

- [API Documentation](./docs/api/)
- [Frontend Documentation](./docs/frontend/)
- [Development Guide](./docs/development.md)

## 🧪 Testing

```bash
# Backend tests
cd apps/backend
uv run pytest

# Frontend tests
cd apps/frontend
bun run test
```

## 🔧 Development Tools

- Pre-commit hooks for code quality
- Automated linting and formatting
- Security scanning
- Dependency checking
- IDE integration

## 📝 License

MIT License - see [LICENSE](LICENSE) file.
EOF
        success_message "Main README.md generated"
    fi
    
    # Development guide
    cat > "$DOCS_DIR/development.md" << 'EOF'
# Development Guide

## Code Quality

This project uses comprehensive tooling for code quality:

### Python (Backend)
- **Ruff**: Modern linter and formatter (replaces flake8, black, isort)
- **MyPy**: Static type checking
- **Bandit**: Security vulnerability scanning
- **Safety**: Dependency vulnerability checking
- **Pytest**: Testing framework with coverage

### TypeScript (Frontend)
- **ESLint**: Linting with TypeScript support
- **Prettier**: Code formatting
- **TypeScript**: Static type checking
- **Jest**: Testing framework

### Development Workflow

1. **Setup**: Run `./scripts/devtools/setup-dev-environment.sh`
2. **Development**: Use pre-commit hooks for automatic quality checks
3. **Testing**: Write tests for all new features
4. **Documentation**: Update docs for API changes
5. **Security**: Regular dependency audits

### IDE Configuration

VSCode settings are provided in `.vscode/` directory with:
- Automatic formatting on save
- Integrated linting and type checking
- Python virtual environment detection
- Recommended extensions

### Scripts

- `scripts/devtools/setup-dev-environment.sh`: Complete environment setup
- `scripts/devtools/check-dependencies.sh`: Security and update checks
- `scripts/devtools/generate-docs.sh`: Documentation generation

### Quality Gates

All code must pass:
- Linting without warnings
- Type checking without errors
- All tests passing
- Security scans clean
- Pre-commit hooks passing
EOF
        success_message "Development guide generated"
    fi
}

# Generate API documentation overview
generate_api_overview() {
    info_message "Generating API overview..."
    
    cat > "$DOCS_DIR/api/README.md" << 'EOF'
# API Documentation

## Overview

The LONI Backend API provides RESTful endpoints for software visualization and analysis.

## Documentation Format

- **OpenAPI Spec**: Machine-readable API specification
- **Sphinx Docs**: Human-readable API documentation with examples
- **Interactive Docs**: Available at `/docs` endpoint when running

## Key Endpoints

### Health Check
- `GET /health` - System health status

### Authentication
- `POST /auth/login` - User authentication
- `POST /auth/refresh` - Token refresh

### Visualization
- `GET /visualization/` - Get visualizations
- `POST /visualization/` - Create visualization

### Canvas
- `GET /canvas/` - Get canvas data
- `POST /canvas/` - Create canvas

## Authentication

The API uses JWT tokens for authentication. Include the token in the Authorization header:

```
Authorization: Bearer <your-jwt-token>
```

## Error Handling

Standard HTTP status codes are used:
- 200: Success
- 400: Bad Request
- 401: Unauthorized
- 404: Not Found
- 500: Internal Server Error

Error responses include detailed error messages in JSON format.
EOF
    
    success_message "API overview generated"
}

# Copy documentation to web-accessible location
copy_to_web() {
    if [[ "${1:-}" == "--web" ]]; then
        info_message "Copying documentation to web directory..."
        
        # Create web docs directory in frontend public folder
        mkdir -p "$FRONTEND_DIR/public/docs"
        
        # Copy generated docs
        if [[ -d "$DOCS_DIR/api/backend" ]]; then
            cp -r "$DOCS_DIR/api/backend"/* "$FRONTEND_DIR/public/docs/" 2>/dev/null || true
        fi
        
        if [[ -d "$DOCS_DIR/frontend" ]]; then
            cp -r "$DOCS_DIR/frontend" "$FRONTEND_DIR/public/docs/" 2>/dev/null || true
        fi
        
        success_message "Documentation copied to web directory"
    fi
}

# Print summary
print_summary() {
    info_message "Documentation generation completed!"
    echo ""
    info_message "Generated documentation:"
    echo "  - Python API docs: $DOCS_DIR/api/backend/"
    echo "  - TypeScript docs: $DOCS_DIR/frontend/"
    echo "  - OpenAPI spec: $DOCS_DIR/api/openapi.json"
    echo "  - README files: $PROJECT_ROOT/README.md"
    echo ""
    info_message "To serve documentation locally:"
    echo "  - API docs: python -m http.server 8080 -d $DOCS_DIR/api/backend/"
    echo "  - TypeScript docs: python -m http.server 8081 -d $DOCS_DIR/frontend/"
    echo ""
    echo "To copy docs to web directory:"
    echo "  $0 --web"
}

# Main execution
main() {
    generate_python_docs
    generate_typescript_docs
    generate_readme_files
    generate_api_overview
    copy_to_web "$@"
    print_summary
    
    success_message "Documentation generation completed! 📚"
}

# Run main function
main "$@"