"use client";

import React from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/shared/ui/card';
import theme from '@/shared/config/theme';

interface ColorPaletteProps {
  colors: Record<string, string>;
  name: string;
}

const ColorPalette: React.FC<ColorPaletteProps> = ({ colors, name }) => {
  return (
    <div className="mb-6">
      <h3 className="text-lg font-semibold mb-3">{name}</h3>
      <div className="grid grid-cols-5 gap-2">
        {Object.entries(colors).map(([key, value]) => (
          <div key={key} className="flex flex-col items-center">
            <div 
              className="w-full h-12 rounded-md mb-1 border" 
              style={{ backgroundColor: value }}
            />
            <span className="text-xs text-muted-foreground">{key}</span>
          </div>
        ))}
      </div>
    </div>
  );
};

const ColorPaletteViewer: React.FC = () => {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Color Palette</CardTitle>
      </CardHeader>
      <CardContent>
        <ColorPalette colors={theme.colors.primary} name="Primary" />
        <ColorPalette colors={theme.colors.secondary} name="Secondary" />
        <ColorPalette colors={theme.colors.success} name="Success" />
        <ColorPalette colors={theme.colors.warning} name="Warning" />
        <ColorPalette colors={theme.colors.error} name="Error" />
      </CardContent>
    </Card>
  );
};

export default ColorPaletteViewer;