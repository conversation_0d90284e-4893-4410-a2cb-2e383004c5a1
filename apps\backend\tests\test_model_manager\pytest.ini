[tool:pytest]
addopts = 
    --verbose
    --tb=short
    --strict-markers
    --strict-config
    --cov=app.model_manager
    --cov-report=html:htmlcov
    --cov-report=term-missing
    --cov-report=xml
    --cov-fail-under=85
    --asyncio-mode=auto

markers =
    unit: Unit tests
    integration: Integration tests
    e2e: End-to-end tests
    performance: Performance tests
    slow: Slow running tests
    benchmark: Benchmark tests

testpaths = .
norecursedirs = 
    .git
    .tox
    __pycache__
    *.egg-info
    .venv
    venv

python_files = test_*.py *_test.py
python_classes = Test*
python_functions = test_*

filerwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning

log_cli = true
log_cli_level = INFO
log_cli_format = %(asctime)s [%(levelname)8s] %(name)s: %(message)s
log_cli_date_format = %Y-%m-%d %H:%M:%S

minversion = 7.0
