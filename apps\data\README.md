# LONI Data Directory

This directory contains all persistent data for the LONI platform:

## Structure

- `logs/` - Application logs and audit trails
- `metadata/` - System metadata and configuration
- `models/` - AI/ML model files and related data
- `temp/` - Temporary files and caches

## Purpose

The data directory serves as the central storage location for all LONI platform data, ensuring proper separation between code (in `apps/`) and data. This directory is mounted as a volume in Docker containers to persist data across container restarts.

## Best Practices

- All data should be stored in appropriate subdirectories
- Files should follow consistent naming conventions
- Sensitive data should be properly secured
- Regular backups should be performed