[project]
name = "backend"
version = "0.1.0"
description = "Backend application for LONI project"
readme = "README.md"
requires-python = ">=3.9"
dependencies = [
    "fastapi>=0.104.0",
    "pydantic-ai>=0.0.14",
    "httpx>=0.25.0",
    "pydantic-settings>=2.0.0",
    "python-dotenv>=1.0.0",
    "loguru>=0.7.0",
    "uvicorn[standard]>=0.24.0",
    "whisper>=1.0",
    # Scanner dependencies
    "pydantic>=2.0.0",
]

[project.optional-dependencies]
dev = [
    "ruff>=0.1.0",
    "black>=23.0.0",
    "pytest>=7.0.0",
    "mypy>=1.0.0",
    "pre-commit>=3.0.0",
    "pytest-asyncio>=0.21.0",
    "pytest-cov>=4.0.0",
    "isort>=5.0.0",
    "bandit>=1.7.0",
    "safety>=2.0.0",
    "httpx>=0.25.0",
]

[tool.black]
line-length = 88
target-version = ['py39']

[tool.ruff]
line-length = 88
select = [
    "E",   # pycodestyle errors
    "W",   # pycodestyle warnings
    "F",   # Pyflakes
    "C90", # mccabe complexity
    "I",   # isort
    "N",   # pep8-naming
    "UP",  # pyupgrade
    "B",   # flake8-bugbear
    "A",   # flake8-builtins
    "C4",  # flake8-comprehensions
    "PIE", # flake8-pie
    "SIM", # flake8-simplify
]
ignore = [
    "E501",   # line too long, handled by black
    "B008",   # do not perform function calls in argument defaults
    "C901",   # too complex
    "W191",   # indentation contains tabs
    "E203",   # whitespace before ':'
]

[tool.ruff.per-file-ignores]
"tests/*" = ["B011"]  # assert false

[tool.ruff.mccabe]
max-complexity = 10

[tool.isort]
profile = "black"
line_length = 88
multi_line_output = 3
include_trailing_comma = true
force_grid_wrap = 0
use_parentheses = true
ensure_newline_before_comments = true
known_first_party = ["app"]

[tool.mypy]
python_version = "3.9"
strict = true
ignore_missing_imports = true
disallow_untyped_defs = true
disallow_any_generics = true
warn_return_any = true
warn_unused_configs = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = [
    "--strict-markers",
    "--strict-config",
    "--verbose",
    "--tb=short",
    "--cov=app",
    "--cov-report=term-missing",
    "--cov-report=html:htmlcov",
    "--cov-report=xml",
    "--cov-fail-under=70",
]
markers = [
    "unit: Unit tests",
    "integration: Integration tests",
    "slow: Slow tests",
    "asyncio: Async tests",
]
filterwarnings = [
    "ignore::DeprecationWarning",
    "ignore::PendingDeprecationWarning",
]
