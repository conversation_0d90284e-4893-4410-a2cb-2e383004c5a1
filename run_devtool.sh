#!/bin/bash\n# Unified development tool runner for LONI platform\n\n# Set variables\nPROJECT_ROOT=\"/mnt/e/Projects/lonors/loni\"\n\n# Function to log messages\nlog_message() {\n    echo \"[$(date '+%Y-%m-%d %H:%M:%S')] $1\"\n}\n\n# Function to run a tool with logging\nrun_tool() {\n    local tool_name=$1\n    local tool_script=$2\n    local log_file=\"$PROJECT_ROOT/apps/data/logs/devtools-$tool_name.log\"\n    \n    # Create log file if it doesn't exist\n    mkdir -p \"$PROJECT_ROOT/apps/data/logs\"\n    touch \"$log_file\"\n    \n    log_message \"Starting $tool_name execution\" | tee -a \"$log_file\"\n    \n    # Run the tool script and capture output\n    bash \"$tool_script\" 2>&1 | tee -a \"$log_file\"\n    \n    # Check exit status\n    if [ ${PIPESTATUS[0]} -eq 0 ]; then\n        log_message \"$tool_name completed successfully\" | tee -a \"$log_file\"\n        return 0\n    else\n        log_message \"ERROR: $tool_name failed\" | tee -a \"$log_file\"\n        return 1\n    fi\n}\n\n# Display help\nshow_help() {\n    echo \"LONI Platform Development Tool Runner\"\n    echo \"Usage: $0 [tool_name]\"\n    echo \"\"\n    echo \"Available tools:\"\n    echo \"  backend-tests      - Run backend tests\"\n    echo \"  backend-format     - Run backend code formatting\"\n    echo \"  backend-lint       - Run backend linting\"\n    echo \"  backend-typecheck  - Run backend type checking\"\n    echo \"  frontend-lint      - Run frontend linting\"\n    echo \"  frontend-typecheck - Run frontend type checking\"\n    echo \"  all                - Run all tools\"\n    echo \"\"\n    echo \"Examples:\"\n    echo \"  $0 backend-tests\"\n    echo \"  $0 frontend-lint\"\n    echo \"  $0 all\"\n}\n\n# Main execution\nmain() {\n    local tool_name=$1\n    \n    case $tool_name in\n        backend-tests)\n            run_tool \"backend-tests\" \"$PROJECT_ROOT/apps/backend/run_tests.sh\"\n            ;;\n        backend-format)\n            run_tool \"backend-format\" \"$PROJECT_ROOT/apps/backend/run_formatting.sh\"\n            ;;\n        backend-lint)\n            run_tool \"backend-lint\" \"$PROJECT_ROOT/apps/backend/run_linting.sh\"\n            ;;\n        backend-typecheck)\n            run_tool \"backend-typecheck\" \"$PROJECT_ROOT/apps/backend/run_typecheck.sh\"\n            ;;\n        frontend-lint)\n            run_tool \"frontend-lint\" \"$PROJECT_ROOT/apps/frontend/run_linting.sh\"\n            ;;\n        frontend-typecheck)\n            run_tool \"frontend-typecheck\" \"$PROJECT_ROOT/apps/frontend/run_typecheck.sh\"\n            ;;\n        all)\n            log_message \"Running all development tools...\"\n            run_tool \"backend-tests\" \"$PROJECT_ROOT/apps/backend/run_tests.sh\" && \\\n            run_tool \"backend-format\" \"$PROJECT_ROOT/apps/backend/run_formatting.sh\" && \\\n            run_tool \"backend-lint\" \"$PROJECT_ROOT/apps/backend/run_linting.sh\" && \\\n            run_tool \"backend-typecheck\" \"$PROJECT_ROOT/apps/backend/run_typecheck.sh\" && \\\n            run_tool \"frontend-lint\" \"$PROJECT_ROOT/apps/frontend/run_linting.sh\" && \\\n            run_tool \"frontend-typecheck\" \"$PROJECT_ROOT/apps/frontend/run_typecheck.sh\"\n            ;;\n        help|--help|-h)\n            show_help\n            ;;\n        *)\n            echo \"Unknown tool: $tool_name\"\n            show_help\n            exit 1\n            ;;\n    esac\n}\n\n# Run main function with all arguments\nmain \"$@\"\n