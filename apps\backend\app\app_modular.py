"""
Modular FastAPI application using the n8n-like module architecture.

This version of the app uses the modular system where each business
logic component is a self-contained module/node.
"""

from typing import Optional
from fastapi import FastAP<PERSON>, HTTPException
from fastapi.responses import J<PERSON><PERSON>esponse
from fastapi.middleware.base import BaseHTTPMiddleware
from fastapi.middleware.cors import CORSMiddleware

from app.api_models import HealthResponse
from app.config import settings
from app.logger import get_logger, LoggerContext
from app.middleware.logging_middleware import (
    correlation_id_middleware,
    performance_logging_middleware,
    security_logging_middleware
)
from app.modules import (
    registry,
    ModuleFactory,
    ModuleType,
    ModuleConfig,
    ModuleInput,
    ModuleOutput
)

logger = get_logger("app_modular")


async def create_modular_app() -> FastAPI:
    """Create and configure the FastAPI application with modular architecture."""

    app = FastAPI(
        title=settings.app_name,
        version=settings.app_version,
        description="Backend API for LONI project - AI-powered audio transcription and model management (Modular Architecture)",
        debug=settings.debug,
        docs_url="/docs",
        redoc_url="/redoc",
        openapi_url="/openapi.json",
        contact={
            "name": "LONI Team",
            "description": "Professional AI-powered platform for audio transcription and Ollama model management"
        },
        license_info={
            "name": "MIT License",
            "url": "https://opensource.org/licenses/MIT"
        }
    )

    # Add CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.cors_origins or ["http://localhost:3000"],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

    # Add logging middleware
    app.middleware("http")(correlation_id_middleware)
    app.middleware("http")(performance_logging_middleware)
    app.middleware("http")(security_logging_middleware)

    # Initialize and configure modules
    await initialize_modules()

    # Register routes using modular endpoints
    _register_modular_routes(app)

    # Register lifecycle events
    _register_lifecycle_events(app)

    # Register exception handlers
    _register_exception_handlers(app)

    return app


async def initialize_modules() -> None:
    """Initialize all modules in the system."""

    # Create module configurations
    configs = [
        # Model Manager Module
        ModuleConfig(
            name="ollama_manager",
            type=ModuleType.MODEL_MANAGER,
            enabled=True,
            priority=10,
            settings={
                'host': settings.ollama_host,
                'api_timeout': settings.ollama_api_timeout
            }
        ),

        # Transcription Module
        ModuleConfig(
            name="whisper_transcriber",
            type=ModuleType.TRANSCRIPTION,
            enabled=True,
            priority=20,
            settings={
                'model_name': settings.whisper_model,
                'device': getattr(settings, 'whisper_device', 'cpu'),
                'cache_dir': getattr(settings, 'whisper_cache_dir', None)
            }
        ),

        # Scanner Module
        ModuleConfig(
            name="project_scanner",
            type=ModuleType.SCANNER,
            enabled=True,
            priority=30,
            settings={
                'enable_code_quality': True,
                'enable_security': True,
                'enable_dependencies': True,
                'enable_structure': True,
                'enable_performance': True,
                'timeout_per_detector': 300
            }
        )
    ]

    # Create and register modules
    for config in configs:
        try:
            module = ModuleFactory.create_module(config.type, config.name)
            registry.register(module)
            registry.set_config(config.name, config)

            logger.info(f"Registered module: {config.name} ({config.type.value})")
        except Exception as e:
            logger.error(f"Failed to create module {config.name}: {e}")

    # Initialize all modules
    init_results = await registry.initialize_all()

    for module_name, success in init_results.items():
        if success:
            logger.info(f"Module {module_name} initialized successfully")
        else:
            logger.error(f"Module {module_name} failed to initialize")


def _register_modular_routes(app: FastAPI) -> None:
    """Register API routes using modular endpoints."""

    @app.get("/health", response_model=HealthResponse)
    async def health_check():
        """Health check endpoint using modular architecture."""
        return HealthResponse(
            status="ok",
            version=settings.app_version,
            timestamp="2024-01-15T10:00:00Z"
        )

    @app.get("/")
    async def root():
        """Root endpoint."""
        return {
            "message": "Welcome to LONI Backend API (Modular Architecture)",
            "version": settings.app_version,
            "docs_url": "/docs",
            "modules": {
                "total": len(registry.list_all()),
                "types": [m.type.value for m in registry.list_all()]
            }
        }

    @app.get("/api/v1/modules")
    async def list_modules():
        """List all registered modules."""
        modules_info = []
        for module in registry.list_all():
            config = registry.get_config(module.name)
            modules_info.append({
                "name": module.name,
                "type": module.type.value,
                "status": module.status.value,
                "enabled": config.enabled if config else False,
                "priority": config.priority if config else 100
            })

        return {
            "modules": modules_info,
            "total": len(modules_info)
        }

    @app.get("/api/v1/modules/{module_name}")
    async def get_module_info(module_name: str):
        """Get information about a specific module."""
        module = registry.get(module_name)
        if not module:
            raise HTTPException(status_code=404, detail=f"Module '{module_name}' not found")

        config = registry.get_config(module_name)
        return {
            "name": module.name,
            "type": module.type.value,
            "status": module.status.value,
            "config": {
                "enabled": config.enabled if config else False,
                "priority": config.priority if config else 100,
                "settings": config.settings if config else {}
            },
            "dependencies": module.get_dependencies()
        }

    # Model Management Routes
    @app.get("/api/v1/models")
    async def list_models():
        """List available Ollama models using modular architecture."""
        model_manager = registry.get("ollama_manager")
        if not model_manager:
            raise HTTPException(status_code=503, detail="Model manager module not available")

        input_data = ModuleInput(
            data={"action": "list"},
            metadata={"request_type": "api"}
        )

        try:
            result = await model_manager.execute(input_data)
            if result.success:
                return {"models": result.data.get("models", [])}
            else:
                raise HTTPException(status_code=500, detail=result.error)
        except Exception as e:
            logger.error(f"Error listing models: {e}")
            raise HTTPException(status_code=500, detail="Failed to list models")

    @app.post("/api/v1/models/pull")
    async def pull_model(model_name: str):
        """Pull an Ollama model using modular architecture."""
        model_manager = registry.get("ollama_manager")
        if not model_manager:
            raise HTTPException(status_code=503, detail="Model manager module not available")

        input_data = ModuleInput(
            data={"action": "pull", "model_name": model_name},
            metadata={"request_type": "api"}
        )

        try:
            result = await model_manager.execute(input_data)
            if result.success:
                return {
                    "message": f"Successfully pulled model {model_name}",
                    "result": result.data
                }
            else:
                raise HTTPException(status_code=500, detail=result.error)
        except Exception as e:
            logger.error(f"Error pulling model {model_name}: {e}")
            raise HTTPException(status_code=500, detail=f"Failed to pull model {model_name}")

    @app.post("/api/v1/models/set")
    async def set_model(model_name: str):
        """Set the active Ollama model using modular architecture."""
        model_manager = registry.get("ollama_manager")
        if not model_manager:
            raise HTTPException(status_code=503, detail="Model manager module not available")

        input_data = ModuleInput(
            data={"action": "set", "model_name": model_name},
            metadata={"request_type": "api"}
        )

        try:
            result = await model_manager.execute(input_data)
            if result.success:
                return {
                    "message": f"Successfully set active model to {model_name}",
                    "result": result.data
                }
            else:
                raise HTTPException(status_code=500, detail=result.error)
        except Exception as e:
            logger.error(f"Error setting model {model_name}: {e}")
            raise HTTPException(status_code=500, detail=f"Failed to set model {model_name}")

    @app.post("/api/v1/models/use")
    async def use_model(model_name: str, input_data: str):
        """Use an Ollama model for inference using modular architecture."""
        model_manager = registry.get("ollama_manager")
        if not model_manager:
            raise HTTPException(status_code=503, detail="Model manager module not available")

        module_input = ModuleInput(
            data={"action": "use", "model_name": model_name, "input_data": input_data},
            metadata={"request_type": "api"}
        )

        try:
            result = await model_manager.execute(module_input)
            if result.success:
                return {"result": result.data}
            else:
                raise HTTPException(status_code=500, detail=result.error)
        except Exception as e:
            logger.error(f"Error using model {model_name}: {e}")
            raise HTTPException(status_code=500, detail=f"Failed to use model {model_name}")

    # Transcription Routes
    @app.post("/api/v1/transcribe")
    async def transcribe_audio(audio_path: str, language: Optional[str] = None):
        """Transcribe audio using modular architecture."""
        transcriber = registry.get("whisper_transcriber")
        if not transcriber:
            raise HTTPException(status_code=503, detail="Transcription module not available")

        input_data = ModuleInput(
            data={
                "audio_path": audio_path,
                "language": language
            },
            metadata={"request_type": "api"}
        )

        try:
            result = await transcriber.execute(input_data)
            if result.success:
                return {
                    "transcription": result.data.get("transcription"),
                    "language": result.data.get("language"),
                    "confidence": result.data.get("confidence", 0.95)
                }
            else:
                raise HTTPException(status_code=500, detail=result.error)
        except Exception as e:
            logger.error(f"Error transcribing audio {audio_path}: {e}")
            raise HTTPException(status_code=500, detail="Failed to transcribe audio")

    # Scanner Routes
    @app.post("/api/v1/scan")
    async def scan_project(project_path: str, scan_config: Optional[dict] = None):
        """Scan a project using modular architecture."""
        scanner = registry.get("project_scanner")
        if not scanner:
            raise HTTPException(status_code=503, detail="Scanner module not available")

        input_data = ModuleInput(
            data={
                "project_path": project_path,
                "scan_config": scan_config
            },
            metadata={"request_type": "api"}
        )

        try:
            result = await scanner.execute(input_data)
            if result.success:
                return result.data
            else:
                raise HTTPException(status_code=500, detail=result.error)
        except Exception as e:
            logger.error(f"Error scanning project {project_path}: {e}")
            raise HTTPException(status_code=500, detail="Failed to scan project")


def _register_lifecycle_events(app: FastAPI) -> None:
    """Register application lifecycle events."""

    @app.on_event("startup")
    async def startup_event():
        """Application startup event with structured logging."""
        logger.info(
            "Modular application startup",
            app_name=settings.app_name,
            app_version=settings.app_version,
            environment=settings.environment,
            debug=settings.debug,
            modules_count=len(registry.list_all()),
            module_types=[m.type.value for m in registry.list_all()]
        )

    @app.on_event("shutdown")
    async def shutdown_event():
        """Application shutdown event with structured logging."""
        logger.info(
            "Modular application shutdown",
            app_name=settings.app_name,
            app_version=settings.app_version
        )

        # Cleanup all modules
        await registry.cleanup_all()


def _register_exception_handlers(app: FastAPI) -> None:
    """Register global exception handlers."""

    @app.exception_handler(HTTPException)
    async def http_exception_handler(request, exc: HTTPException):
        """Handle HTTP exceptions with proper error responses."""
        logger.warning(
            "HTTP exception",
            status_code=exc.status_code,
            detail=exc.detail,
            url=str(request.url),
            method=request.method
        )

        return JSONResponse(
            status_code=exc.status_code,
            content={
                "message": exc.detail,
                "error_id": f"http_{exc.status_code}",
                "status_code": exc.status_code
            }
        )

    @app.exception_handler(ValueError)
    async def value_error_handler(request, exc: ValueError):
        """Handle validation errors."""
        logger.warning(
            "Validation error",
            error=str(exc),
            url=str(request.url),
            method=request.method
        )

        return JSONResponse(
            status_code=400,
            content={
                "message": f"Validation error: {str(exc)}",
                "error_id": "validation_error"
            }
        )

    @app.exception_handler(FileNotFoundError)
    async def file_not_found_handler(request, exc: FileNotFoundError):
        """Handle file not found errors."""
        logger.warning(
            "File not found",
            error=str(exc),
            url=str(request.url),
            method=request.method
        )

        return JSONResponse(
            status_code=404,
            content={
                "message": f"File not found: {str(exc)}",
                "error_id": "file_not_found"
            }
        )

    @app.exception_handler(PermissionError)
    async def permission_error_handler(request, exc: PermissionError):
        """Handle permission errors."""
        logger.warning(
            "Permission denied",
            error=str(exc),
            url=str(request.url),
            method=request.method
        )

        return JSONResponse(
            status_code=403,
            content={
                "message": "Permission denied",
                "error_id": "permission_denied"
            }
        )

    @app.exception_handler(Exception)
    async def general_exception_handler(request, exc):
        """Handle general exceptions with structured logging."""
        import traceback

        logger.error(
            "Unhandled exception",
            exception_type=type(exc).__name__,
            exception_message=str(exc),
            traceback=traceback.format_exc(),
            url=str(request.url),
            method=request.method,
            headers=dict(request.headers)
        )

        return JSONResponse(
            status_code=500,
            content={
                "message": "Internal server error",
                "error_id": "internal_error",
                "detail": str(exc) if settings.debug else None
            }
        )
