"""Enhanced model manager for handling Ollama model operations via API and CLI."""
from typing import Dict, List, Optional, Any

from app.config import settings
from app.logger import get_logger, log_model_operation
from app.models.model_cache import ModelCache
from app.models.model_api_client import ModelApi<PERSON>lient
from app.models.model_cli_client import ModelCliClient

logger = get_logger("model_manager")


class ModelManager:
    """Enhanced manager for Ollama model operations with API integration."""

    def __init__(self, host: Optional[str] = None, api_timeout: Optional[int] = None):
        """Initialize the model manager."""
        self.current_model: Optional[str] = None
        self._ollama_host = host or settings.ollama_host
        self._api_timeout = api_timeout or settings.ollama_api_timeout

        # Initialize components
        self._cache = ModelCache()
        self._api_client = ModelApiClient(host, api_timeout)
        self._cli_client = ModelCliClient()

    async def list_models(self) -> List[str]:
        """List available Ollama models with caching."""
        try:
            # Check cache first
            if self._cache.is_cache_valid():
                logger.debug("Returning cached model list")
                return self._cache.get_available_models()

            # Try API first, fallback to CLI
            models = await self._api_client.list_models()
            if not models:
                models = self._cli_client.list_models()

            self._cache.set_available_models(models)

            log_model_operation(
                model_name="all",
                operation="list",
                model_count=len(models),
                method="api" if models else "cli"
            )

            return models.copy()

        except Exception as e:
            logger.error(
                "Failed to list models",
                error=str(e),
                error_type=type(e).__name__,
                host=self._ollama_host
            )
            return []

    async def pull_model(self, model_name: str) -> Dict[str, Any]:
        """Pull an Ollama model with progress tracking."""
        import time
        start_time = time.time()

        try:
            logger.info("Starting model pull", model_name=model_name)

            # Try API method first
            result = await self._api_client.pull_model(model_name)
            if result:
                duration = time.time() - start_time
                log_model_operation(
                    model_name=model_name,
                    operation="pull",
                    duration=duration,
                    method="api",
                    success=True
                )
                # Invalidate cache to refresh model list
                self._cache.invalidate_cache()
                return result

            # Fallback to CLI method
            result = self._cli_client.pull_model(model_name)
            duration = time.time() - start_time
            log_model_operation(
                model_name=model_name,
                operation="pull",
                duration=duration,
                method="cli",
                success=True
            )
            # Invalidate cache to refresh model list
            self._cache.invalidate_cache()
            return {"message": f"Successfully pulled model {model_name}", "output": result}

        except Exception as e:
            duration = time.time() - start_time
            log_model_operation(
                model_name=model_name,
                operation="pull",
                duration=duration,
                success=False,
                error=str(e)
            )
            raise

    async def set_model(self, model_name: str) -> None:
        """Set the active Ollama model with validation."""
        try:
            # Verify the model exists by refreshing the list
            available_models = await self.list_models()

            if model_name not in available_models:
                raise Exception(f"Model {model_name} not available. Available models: {available_models}")

            self.current_model = model_name
            settings.ollama_model = model_name

            logger.info("Active model changed", model_name=model_name)
            log_model_operation(
                model_name=model_name,
                operation="set_active",
                success=True
            )

        except Exception as e:
            logger.error(
                "Failed to set active model",
                model_name=model_name,
                error=str(e),
                error_type=type(e).__name__
            )
            raise

    async def use_model(self, model_name: str, input_data: str, **kwargs) -> Dict[str, Any]:
        """Use an Ollama model for inference with enhanced error handling."""
        import time
        start_time = time.time()

        try:
            # Validate model availability
            available_models = await self.list_models()
            if model_name not in available_models:
                raise Exception(f"Model {model_name} not available. Available models: {available_models}")

            logger.info(
                "Starting model inference",
                model_name=model_name,
                input_length=len(input_data),
                options=kwargs
            )

            # Try API method first
            result = await self._api_client.use_model(model_name, input_data, **kwargs)
            if result:
                duration = time.time() - start_time
                log_model_operation(
                    model_name=model_name,
                    operation="inference",
                    duration=duration,
                    method="api",
                    input_tokens=len(input_data.split()),
                    output_tokens=len(result.get("response", "").split()),
                    success=True
                )
                return result

            # Fallback to CLI method
            result = self._cli_client.use_model(model_name, input_data, **kwargs)
            duration = time.time() - start_time
            log_model_operation(
                model_name=model_name,
                operation="inference",
                duration=duration,
                method="cli",
                input_tokens=len(input_data.split()),
                output_tokens=len(result.split()) if isinstance(result, str) else 0,
                success=True
            )
            return {"response": result}

        except Exception as e:
            duration = time.time() - start_time
            log_model_operation(
                model_name=model_name,
                operation="inference",
                duration=duration,
                success=False,
                error=str(e)
            )
            raise

    def get_current_model(self) -> Optional[str]:
        """Get the currently active model."""
        return self.current_model

    def get_available_models(self) -> List[str]:
        """Get the list of available models."""
        return self._cache.get_available_models()

    async def get_model_info(self, model_name: str) -> Optional[Dict[str, Any]]:
        """Get detailed information about a model."""
        # Check cache first
        cached_info = self._cache.get_model_info(model_name)
        if cached_info:
            return cached_info

        # Get from API
        info = await self._api_client.get_model_info(model_name)
        if info:
            self._cache.set_model_info(model_name, info)

        return info

    async def remove_model(self, model_name: str) -> bool:
        """Remove a model from Ollama."""
        try:
            logger.info("Removing model", model_name=model_name)

            # Try API method first
            if await self._api_client.remove_model(model_name):
                log_model_operation(
                    model_name=model_name,
                    operation="remove",
                    method="api",
                    success=True
                )
                self._cache.invalidate_cache()
                return True

            # Fallback to CLI method
            if self._cli_client.remove_model(model_name):
                log_model_operation(
                    model_name=model_name,
                    operation="remove",
                    method="cli",
                    success=True
                )
                self._cache.invalidate_cache()
                return True

            return False

        except Exception as e:
            log_model_operation(
                model_name=model_name,
                operation="remove",
                success=False,
                error=str(e)
            )
            raise

    async def health_check(self) -> Dict[str, Any]:
        """Check Ollama service health."""
        return await self._api_client.health_check()

    async def close(self) -> None:
        """Close the HTTP client and cleanup resources."""
        await self._api_client.close()

    def clear_cache(self) -> None:
        """Clear the model cache."""
        self._cache.clear_cache()
