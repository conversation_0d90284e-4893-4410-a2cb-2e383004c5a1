# Dependencies
node_modules/
.next/
.nuxt/
dist/
build/

# Logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids
*.pid
*.seed

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# Compiled files
*.tsbuildinfo

# Environment files
.env*
!.env.example

# Cache
.cache/
.parcel-cache/

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Package manager files
package-lock.json
yarn.lock
bun.lockb

# Auto-generated files
**/*.generated.*