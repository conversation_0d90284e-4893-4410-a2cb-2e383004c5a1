"""
Tests for authentication endpoints.
"""

import pytest
from fastapi.testclient import Test<PERSON><PERSON>
from sqlalchemy.ext.asyncio import AsyncSession
from app.main import create_app
from app.models.user import User


@pytest.fixture
def client() -> TestClient:
    """Create a test client for the FastAPI app."""
    app = create_app()
    return TestClient(app)


@pytest.mark.asyncio
async def test_register_user(client: TestClient, db_session: AsyncSession):
    """Test user registration endpoint."""
    response = client.post("/api/v1/auth/register", json={
        "email": "<EMAIL>",
        "username": "newuser",
        "password": "password123",
        "full_name": "New User"
    })
    
    assert response.status_code == 200
    data = response.json()
    assert data["email"] == "<EMAIL>"
    assert data["username"] == "newuser"
    assert "id" in data
    assert "hashed_password" not in data  # Should not be in response


@pytest.mark.asyncio
async def test_login_user(client: <PERSON><PERSON><PERSON>, test_user: User):
    """Test user login endpoint."""
    response = client.post("/api/v1/auth/login", data={
        "username": "<EMAIL>",
        "password": "password123"
    })
    
    assert response.status_code == 200
    data = response.json()
    assert "access_token" in data
    assert data["token_type"] == "bearer"


@pytest.mark.asyncio
async def test_get_current_user(client: TestClient, test_user: User):
    """Test get current user endpoint."""
    # First login to get a token
    login_response = client.post("/api/v1/auth/login", data={
        "username": "<EMAIL>",
        "password": "password123"
    })
    
    assert login_response.status_code == 200
    token_data = login_response.json()
    access_token = token_data["access_token"]
    
    # Use token to get current user
    response = client.get("/api/v1/auth/me", headers={
        "Authorization": f"Bearer {access_token}"
    })
    
    assert response.status_code == 200
    data = response.json()
    assert data["email"] == "<EMAIL>"
    assert data["username"] == "testuser"