#!/bin/bash
# Script to run backend type checking with logging

# Set variables
PROJECT_ROOT="/mnt/e/Projects/lonors/loni"
BACKEND_DIR="$PROJECT_ROOT/apps/backend"
LOG_FILE="$PROJECT_ROOT/apps/data/logs/devtools-backend-typecheck.log"

# Create log file if it doesn't exist
mkdir -p "$PROJECT_ROOT/apps/data/logs"
touch "$LOG_FILE"

# Function to log messages
log_message() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

# Start type checking execution
log_message "Starting backend type checking execution"

# Change to backend directory
cd "$BACKEND_DIR" || { log_message "ERROR: Failed to change to backend directory"; exit 1; }

# Run type checking with mypy
log_message "Running mypy type checker..."
uv run mypy app/ 2>&1 | tee -a "$LOG_FILE"

# Check exit status
if [ ${PIPESTATUS[0]} -eq 0 ]; then
    log_message "Backend type checking completed successfully"
else
    log_message "ERROR: Backend type checking failed"
    exit 1
fi