#!/usr/bin/env python3
"""
Advanced Coverage Reporter with Quality Gates
Provides detailed coverage analysis and reporting
"""

import json
import os
import subprocess
import sys
import xml.etree.ElementTree as ET
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
import re


@dataclass
class CoverageMetrics:
    """Coverage metrics for a component"""
    lines_covered: int
    lines_total: int
    branches_covered: int
    branches_total: int
    functions_covered: int
    functions_total: int
    statements_covered: int
    statements_total: int
    
    @property
    def line_percentage(self) -> float:
        return (self.lines_covered / self.lines_total * 100) if self.lines_total > 0 else 0
    
    @property
    def branch_percentage(self) -> float:
        return (self.branches_covered / self.branches_total * 100) if self.branches_total > 0 else 0
    
    @property
    def function_percentage(self) -> float:
        return (self.functions_covered / self.functions_total * 100) if self.functions_total > 0 else 0
    
    @property
    def statement_percentage(self) -> float:
        return (self.statements_covered / self.statements_total * 100) if self.statements_total > 0 else 0


@dataclass
class QualityGate:
    """Quality gate configuration"""
    name: str
    line_threshold: float
    branch_threshold: float
    function_threshold: float
    statement_threshold: float
    enforce_increase: bool = False  # Require coverage to increase
    previous_coverage: Optional[float] = None


@dataclass
class CoverageReport:
    """Complete coverage report"""
    component: str
    metrics: CoverageMetrics
    file_coverage: Dict[str, CoverageMetrics]
    uncovered_lines: Dict[str, List[int]]
    timestamp: datetime
    quality_gates_passed: bool
    quality_gate_results: Dict[str, bool]


class CoverageAnalyzer:
    """Analyzes coverage data from different sources"""
    
    def analyze_python_coverage(self, coverage_json_path: Path) -> Optional[CoverageReport]:
        """Analyze Python coverage from coverage.json"""
        if not coverage_json_path.exists():
            return None
        
        try:
            with open(coverage_json_path) as f:
                data = json.load(f)
            
            # Extract overall metrics
            totals = data.get('totals', {})
            overall_metrics = CoverageMetrics(
                lines_covered=totals.get('covered_lines', 0),
                lines_total=totals.get('num_statements', 0),
                branches_covered=totals.get('covered_branches', 0),
                branches_total=totals.get('num_branches', 0),
                functions_covered=0,  # Not available in coverage.py
                functions_total=0,
                statements_covered=totals.get('covered_lines', 0),
                statements_total=totals.get('num_statements', 0)
            )
            
            # Extract file-level coverage
            file_coverage = {}
            uncovered_lines = {}
            
            for file_path, file_data in data.get('files', {}).items():
                summary = file_data.get('summary', {})
                file_metrics = CoverageMetrics(
                    lines_covered=summary.get('covered_lines', 0),
                    lines_total=summary.get('num_statements', 0),
                    branches_covered=summary.get('covered_branches', 0),
                    branches_total=summary.get('num_branches', 0),
                    functions_covered=0,
                    functions_total=0,
                    statements_covered=summary.get('covered_lines', 0),
                    statements_total=summary.get('num_statements', 0)
                )
                
                file_coverage[file_path] = file_metrics
                uncovered_lines[file_path] = file_data.get('missing_lines', [])
            
            return CoverageReport(
                component='backend',
                metrics=overall_metrics,
                file_coverage=file_coverage,
                uncovered_lines=uncovered_lines,
                timestamp=datetime.now(),
                quality_gates_passed=False,  # Will be set later
                quality_gate_results={}
            )
            
        except Exception as e:
            print(f"Error analyzing Python coverage: {e}")
            return None
    
    def analyze_javascript_coverage(self, coverage_dir: Path) -> Optional[CoverageReport]:
        """Analyze JavaScript coverage from various formats"""
        # Try to find coverage-summary.json (Jest/Istanbul format)
        summary_path = coverage_dir / 'coverage-summary.json'
        
        if summary_path.exists():
            return self._analyze_istanbul_coverage(summary_path)
        
        # Try other formats...
        return None
    
    def _analyze_istanbul_coverage(self, summary_path: Path) -> Optional[CoverageReport]:
        """Analyze Istanbul/NYC coverage format"""
        try:
            with open(summary_path) as f:
                data = json.load(f)
            
            # Extract total metrics
            total = data.get('total', {})
            
            overall_metrics = CoverageMetrics(
                lines_covered=total.get('lines', {}).get('covered', 0),
                lines_total=total.get('lines', {}).get('total', 0),
                branches_covered=total.get('branches', {}).get('covered', 0),
                branches_total=total.get('branches', {}).get('total', 0),
                functions_covered=total.get('functions', {}).get('covered', 0),
                functions_total=total.get('functions', {}).get('total', 0),
                statements_covered=total.get('statements', {}).get('covered', 0),
                statements_total=total.get('statements', {}).get('total', 0)
            )
            
            # Extract file-level coverage
            file_coverage = {}
            uncovered_lines = {}
            
            for file_path, file_data in data.items():
                if file_path == 'total':
                    continue
                
                file_metrics = CoverageMetrics(
                    lines_covered=file_data.get('lines', {}).get('covered', 0),
                    lines_total=file_data.get('lines', {}).get('total', 0),
                    branches_covered=file_data.get('branches', {}).get('covered', 0),
                    branches_total=file_data.get('branches', {}).get('total', 0),
                    functions_covered=file_data.get('functions', {}).get('covered', 0),
                    functions_total=file_data.get('functions', {}).get('total', 0),
                    statements_covered=file_data.get('statements', {}).get('covered', 0),
                    statements_total=file_data.get('statements', {}).get('total', 0)
                )
                
                file_coverage[file_path] = file_metrics
                # Extract uncovered lines from ranges
                uncovered = []
                for range_data in file_data.get('lines', {}).get('ranges', []):
                    if not range_data.get('covered'):
                        start = range_data.get('start', {}).get('line', 0)
                        end = range_data.get('end', {}).get('line', 0)
                        uncovered.extend(range(start, end + 1))
                
                uncovered_lines[file_path] = uncovered
            
            return CoverageReport(
                component='frontend',
                metrics=overall_metrics,
                file_coverage=file_coverage,
                uncovered_lines=uncovered_lines,
                timestamp=datetime.now(),
                quality_gates_passed=False,
                quality_gate_results={}
            )
            
        except Exception as e:
            print(f"Error analyzing JavaScript coverage: {e}")
            return None


class QualityGateValidator:
    """Validates coverage against quality gates"""
    
    def __init__(self, gates: List[QualityGate]):
        self.gates = gates
    
    def validate(self, report: CoverageReport) -> Tuple[bool, Dict[str, bool]]:
        """Validate report against all quality gates"""
        results = {}
        all_passed = True
        
        for gate in self.gates:
            passed = self._validate_single_gate(report, gate)
            results[gate.name] = passed
            if not passed:
                all_passed = False
        
        report.quality_gates_passed = all_passed
        report.quality_gate_results = results
        
        return all_passed, results
    
    def _validate_single_gate(self, report: CoverageReport, gate: QualityGate) -> bool:
        """Validate against a single quality gate"""
        metrics = report.metrics
        
        # Check thresholds
        conditions = [
            metrics.line_percentage >= gate.line_threshold,
            metrics.branch_percentage >= gate.branch_threshold,
            metrics.function_percentage >= gate.function_threshold,
            metrics.statement_percentage >= gate.statement_threshold
        ]
        
        # Check coverage increase requirement
        if gate.enforce_increase and gate.previous_coverage is not None:
            current_avg = (metrics.line_percentage + metrics.branch_percentage + 
                          metrics.function_percentage + metrics.statement_percentage) / 4
            conditions.append(current_avg > gate.previous_coverage)
        
        return all(conditions)


class CoverageReporter:
    """Generates coverage reports in various formats"""
    
    def __init__(self):
        self.analyzer = CoverageAnalyzer()
        self.validator = None
    
    def set_quality_gates(self, gates: List[QualityGate]):
        """Set quality gates for validation"""
        self.validator = QualityGateValidator(gates)
    
    def generate_comprehensive_report(self, backend_path: Path, frontend_path: Path) -> Dict[str, CoverageReport]:
        """Generate comprehensive report for both components"""
        reports = {}
        
        # Backend coverage
        backend_coverage_json = backend_path / 'coverage.json'
        if backend_coverage_json.exists():
            backend_report = self.analyzer.analyze_python_coverage(backend_coverage_json)
            if backend_report:
                reports['backend'] = backend_report
        
        # Frontend coverage
        frontend_coverage_dir = frontend_path / 'coverage'
        if frontend_coverage_dir.exists():
            frontend_report = self.analyzer.analyze_javascript_coverage(frontend_coverage_dir)
            if frontend_report:
                reports['frontend'] = frontend_report
        
        # Validate against quality gates
        if self.validator:
            for report in reports.values():
                self.validator.validate(report)
        
        return reports
    
    def generate_html_report(self, reports: Dict[str, CoverageReport], output_path: Path):
        """Generate HTML coverage report"""
        html_content = self._generate_html_template(reports)
        
        with open(output_path, 'w') as f:
            f.write(html_content)
    
    def _generate_html_template(self, reports: Dict[str, CoverageReport]) -> str:
        """Generate HTML report template"""
        html = """
<!DOCTYPE html>
<html>
<head>
    <title>TDD Coverage Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background: #f5f5f5; padding: 20px; border-radius: 5px; }
        .component { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .metrics { display: flex; gap: 20px; margin: 10px 0; }
        .metric { background: #f0f0f0; padding: 10px; border-radius: 3px; text-align: center; }
        .passed { background: #d4edda; color: #155724; }
        .failed { background: #f8d7da; color: #721c24; }
        .file-list { margin-top: 15px; }
        .file-item { margin: 5px 0; padding: 8px; background: #f9f9f9; border-radius: 3px; }
        .uncovered { color: #dc3545; font-size: 0.9em; }
    </style>
</head>
<body>
    <div class="header">
        <h1>TDD Coverage Report</h1>
        <p>Generated: """ + datetime.now().strftime('%Y-%m-%d %H:%M:%S') + """</p>
    </div>
"""
        
        for component, report in reports.items():
            status_class = 'passed' if report.quality_gates_passed else 'failed'
            
            html += f"""
    <div class="component {status_class}">
        <h2>{component.title()} Coverage</h2>
        
        <div class="metrics">
            <div class="metric">
                <div><strong>Lines</strong></div>
                <div>{report.metrics.line_percentage:.1f}%</div>
                <div>{report.metrics.lines_covered}/{report.metrics.lines_total}</div>
            </div>
            <div class="metric">
                <div><strong>Branches</strong></div>
                <div>{report.metrics.branch_percentage:.1f}%</div>
                <div>{report.metrics.branches_covered}/{report.metrics.branches_total}</div>
            </div>
            <div class="metric">
                <div><strong>Functions</strong></div>
                <div>{report.metrics.function_percentage:.1f}%</div>
                <div>{report.metrics.functions_covered}/{report.metrics.functions_total}</div>
            </div>
            <div class="metric">
                <div><strong>Statements</strong></div>
                <div>{report.metrics.statement_percentage:.1f}%</div>
                <div>{report.metrics.statements_covered}/{report.metrics.statements_total}</div>
            </div>
        </div>
        
        <h3>Quality Gates</h3>
        <ul>
"""
            
            for gate_name, passed in report.quality_gate_results.items():
                gate_status = "✅ PASSED" if passed else "❌ FAILED"
                html += f"            <li>{gate_name}: {gate_status}</li>\n"
            
            html += """        </ul>
        
        <h3>File Coverage</h3>
        <div class="file-list">
"""
            
            for file_path, file_metrics in report.file_coverage.items():
                uncovered = report.uncovered_lines.get(file_path, [])
                uncovered_str = f" (Uncovered lines: {', '.join(map(str, uncovered[:10]))}{'...' if len(uncovered) > 10 else ''})" if uncovered else ""
                
                html += f"""
            <div class="file-item">
                <strong>{file_path}</strong>
                <span style="float: right;">Lines: {file_metrics.line_percentage:.1f}%</span>
                <div class="uncovered">{uncovered_str}</div>
            </div>
"""
            
            html += """        </div>
    </div>
"""
        
        html += """
</body>
</html>
"""
        return html
    
    def generate_console_report(self, reports: Dict[str, CoverageReport]):
        """Generate console coverage report"""
        print("\n" + "="*60)
        print("🎯 TDD COVERAGE REPORT")
        print("="*60)
        
        for component, report in reports.items():
            status = "✅ PASSED" if report.quality_gates_passed else "❌ FAILED"
            
            print(f"\n📊 {component.upper()} COVERAGE {status}")
            print("-" * 40)
            print(f"Lines:      {report.metrics.line_percentage:6.1f}% ({report.metrics.lines_covered}/{report.metrics.lines_total})")
            print(f"Branches:   {report.metrics.branch_percentage:6.1f}% ({report.metrics.branches_covered}/{report.metrics.branches_total})")
            print(f"Functions:  {report.metrics.function_percentage:6.1f}% ({report.metrics.functions_covered}/{report.metrics.functions_total})")
            print(f"Statements: {report.metrics.statement_percentage:6.1f}% ({report.metrics.statements_covered}/{report.metrics.statements_total})")
            
            # Quality gates
            print("\n🚪 Quality Gates:")
            for gate_name, passed in report.quality_gate_results.items():
                status_icon = "✅" if passed else "❌"
                print(f"   {status_icon} {gate_name}")
            
            # Show worst coverage files
            if report.file_coverage:
                worst_files = sorted(
                    report.file_coverage.items(),
                    key=lambda x: x[1].line_percentage
                )[:5]
                
                print(f"\n📉 Files needing attention:")
                for file_path, metrics in worst_files:
                    if metrics.line_percentage < 80:  # Only show files below 80%
                        print(f"   {metrics.line_percentage:5.1f}% {file_path}")


def main():
    """Main entry point"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Coverage Reporter with Quality Gates")
    parser.add_argument('--backend-path', type=str, default='apps/backend',
                       help='Backend project path')
    parser.add_argument('--frontend-path', type=str, default='apps/frontend',
                       help='Frontend project path')
    parser.add_argument('--output', type=str, help='Output HTML report path')
    parser.add_argument('--min-backend-coverage', type=float, default=80.0,
                       help='Minimum backend coverage threshold')
    parser.add_argument('--min-frontend-coverage', type=float, default=75.0,
                       help='Minimum frontend coverage threshold')
    parser.add_argument('--enforce-increase', action='store_true',
                       help='Enforce coverage increase')
    
    args = parser.parse_args()
    
    # Setup paths
    backend_path = Path(args.backend_path)
    frontend_path = Path(args.frontend_path)
    
    # Create quality gates
    quality_gates = [
        QualityGate(
            name='Backend Minimum Coverage',
            line_threshold=args.min_backend_coverage,
            branch_threshold=args.min_backend_coverage,
            function_threshold=args.min_backend_coverage,
            statement_threshold=args.min_backend_coverage,
            enforce_increase=args.enforce_increase
        ),
        QualityGate(
            name='Frontend Minimum Coverage',
            line_threshold=args.min_frontend_coverage,
            branch_threshold=args.min_frontend_coverage,
            function_threshold=args.min_frontend_coverage,
            statement_threshold=args.min_frontend_coverage,
            enforce_increase=args.enforce_increase
        )
    ]
    
    # Generate report
    reporter = CoverageReporter()
    reporter.set_quality_gates(quality_gates)
    
    reports = reporter.generate_comprehensive_report(backend_path, frontend_path)
    
    if not reports:
        print("❌ No coverage data found")
        sys.exit(1)
    
    # Console report
    reporter.generate_console_report(reports)
    
    # HTML report
    if args.output:
        output_path = Path(args.output)
        reporter.generate_html_report(reports, output_path)
        print(f"\n📄 HTML report generated: {output_path}")
    
    # Exit with error if quality gates failed
    failed_gates = any(not report.quality_gates_passed for report in reports.values())
    if failed_gates:
        print("\n❌ Quality gates failed!")
        sys.exit(1)
    else:
        print("\n✅ All quality gates passed!")


if __name__ == "__main__":
    main()