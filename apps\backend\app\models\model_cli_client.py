"""CLI client for Ollama model operations."""
import asyncio
import subprocess
from typing import Dict, List, Any

from app.logger import get_logger

logger = get_logger("model_cli_client")


class ModelCliClient:
    """CLI client for Ollama operations."""

    def list_models(self) -> List[str]:
        """List models using Ollama CLI (fallback method)."""
        try:
            result = self._run_ollama_command(["list"])

            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')
                models = []
                for line in lines[1:]:  # Skip header
                    if line.strip():
                        parts = line.split()
                        if parts:
                            models.append(parts[0])
                return models
            else:
                logger.error("CLI command failed", stderr=result.stderr)
                return []

        except Exception as e:
            logger.error("CLI method failed", error=str(e))
            return []

    def pull_model(self, model_name: str) -> str:
        """Pull model using Ollama CLI."""
        result = self._run_ollama_command(["pull", model_name])

        if result.returncode == 0:
            logger.info("Model pulled successfully via CLI", model_name=model_name)
            return result.stdout
        else:
            error_msg = f"Failed to pull model {model_name}: {result.stderr}"
            logger.error(error_msg)
            raise Exception(error_msg)

    def use_model(self, model_name: str, input_data: str, **kwargs) -> str:
        """Use model via Ollama CLI."""
        # Build command with options
        cmd = ["run", model_name, input_data]

        result = self._run_ollama_command(cmd)

        if result.returncode == 0:
            output = result.stdout.strip()
            logger.info("Model inference successful via CLI", model_name=model_name)
            return output
        else:
            error_msg = f"Failed to use model {model_name}: {result.stderr}"
            logger.error(error_msg)
            raise Exception(error_msg)

    def remove_model(self, model_name: str) -> bool:
        """Remove model using CLI."""
        try:
            result = self._run_ollama_command(["rm", model_name])
            return result.returncode == 0

        except Exception:
            return False

    def _run_ollama_command(self, args: List[str]) -> subprocess.CompletedProcess:
        """Run an Ollama command synchronously."""
        try:
            # Create the full command
            cmd = ["ollama"] + args

            # Run the command
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=300  # 5 minute timeout
            )

            return result

        except subprocess.TimeoutExpired:
            raise Exception(f"Command timeout: ollama {' '.join(args)}")
        except Exception as e:
            raise Exception(f"Failed to run ollama command: {e}")
