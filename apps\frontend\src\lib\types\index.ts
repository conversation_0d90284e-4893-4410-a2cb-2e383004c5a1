// Global type definitions for the LONI frontend application

export interface ApiResponse<T = unknown> {
  data?: T;
  error?: string;
  success: boolean;
}

export interface ModelInfo {
  id: string;
  name: string;
  provider: string;
  version?: string;
  description?: string;
  tags?: string[];
  size?: string;
  parameters?: string;
  context_length?: number;
  supports_streaming?: boolean;
  supports_tools?: boolean;
  supports_vision?: boolean;
  supports_function_calling?: boolean;
  last_used?: Date;
  usage_count?: number;
}

export interface OllamaModel {
  name: string;
  model: string;
  modified_at: string;
  size: number;
  digest: string;
  details: {
    format: string;
    family: string;
    families: string[];
    parameter_size: string;
    quantization_level: string;
  };
}

export interface TranscriptionResult {
  transcription: string;
  language?: string;
  duration?: number;
  word_count?: number;
  confidence?: number;
}

export interface ChatMessage {
  id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: Date;
  model?: string;
  tokens?: number;
}

export interface ModelMetrics {
  total_requests: number;
  successful_requests: number;
  failed_requests: number;
  average_response_time: number;
  total_tokens_processed: number;
  active_models: number;
  memory_usage?: number;
}

export interface DesignConfig {
  theme: 'light' | 'dark' | 'auto';
  primary_color: string;
  accent_color: string;
  border_radius: 'none' | 'sm' | 'md' | 'lg' | 'xl';
  font_size: 'sm' | 'base' | 'lg' | 'xl';
  animations: 'none' | 'minimal' | 'full';
  layout: 'default' | 'compact' | 'spacious';
}

export interface AppConfig {
  api_base_url: string;
  websocket_url: string;
  environment: 'development' | 'production' | 'staging';
  features: {
    copilot: boolean;
    analytics: boolean;
    debug_mode: boolean;
  };
}

export type LoadingState = 'idle' | 'loading' | 'success' | 'error';

export type AnimationVariant = 'fadeIn' | 'slideIn' | 'scaleIn' | 'bounceIn';

export type ToastType = 'success' | 'error' | 'warning' | 'info';

export interface Toast {
  id: string;
  type: ToastType;
  title: string;
  description?: string;
  duration?: number;
  action?: {
    label: string;
    onClick: () => void;
  };
}

// Project Scanner Types
export interface ProjectScanRequest {
  id: string;
  projectName: string;
  projectPath?: string;
  repositoryUrl?: string;
  scanType: 'security' | 'quality' | 'performance' | 'dependency' | 'comprehensive';
  options: {
    includeTests: boolean;
    includeDependencies: boolean;
    maxDepth: number;
    excludePatterns: string[];
  };
  createdAt: Date;
  status: ScanStatus;
}

export type ScanStatus = 'pending' | 'initializing' | 'scanning' | 'analyzing' | 'completed' | 'failed' | 'cancelled';

export interface ScanProgress {
  scanId: string;
  status: ScanStatus;
  currentPhase: string;
  progress: number; // 0-100
  estimatedTimeRemaining?: number; // seconds
  filesScanned: number;
  totalFiles: number;
  startedAt: Date;
  updatedAt: Date;
}

export type IssueSeverity = 'critical' | 'high' | 'medium' | 'low' | 'info';
export type IssueCategory = 'security' | 'performance' | 'maintainability' | 'reliability' | 'style' | 'dependency' | 'documentation';

export interface ScanIssue {
  id: string;
  scanId: string;
  severity: IssueSeverity;
  category: IssueCategory;
  title: string;
  description: string;
  file: string;
  line?: number;
  column?: number;
  rule: string;
  ruleUrl?: string;
  codeSnippet?: string;
  suggestion?: string;
  affectedCode?: {
    startLine: number;
    endLine: number;
    content: string;
  };
  metadata?: Record<string, unknown>;
}

export interface ScanResult {
  id: string;
  scanId: string;
  projectName: string;
  scanType: ProjectScanRequest['scanType'];
  summary: {
    totalIssues: number;
    issuesBySeverity: Record<IssueSeverity, number>;
    issuesByCategory: Record<IssueCategory, number>;
    filesScanned: number;
    linesOfCode: number;
    technicalDebt?: string; // e.g., "2.5 days"
    score?: number; // 0-100
  };
  issues: ScanIssue[];
  metrics: {
    scanDuration: number; // seconds
    performanceMetrics?: {
      cyclomatic_complexity: number;
      cognitive_complexity: number;
      maintainability_index: number;
    };
    dependencyMetrics?: {
      outdated: number;
      vulnerable: number;
      total: number;
    };
    codeMetrics?: {
      duplicated_lines: number;
      test_coverage?: number;
      technical_debt_ratio?: number;
    };
  };
  recommendations: string[];
  completedAt: Date;
  reportUrl?: string;
}

export interface ScanFilter {
  severities: IssueSeverity[];
  categories: IssueCategory[];
  files: string[];
  searchQuery: string;
  sortBy: 'severity' | 'category' | 'file' | 'line';
  sortOrder: 'asc' | 'desc';
}

export interface ProjectScannerConfig {
  autoScan: boolean;
  defaultScanType: ProjectScanRequest['scanType'];
  notifications: {
    onScanComplete: boolean;
    onCriticalIssues: boolean;
    email?: string;
  };
  exportFormats: ('json' | 'csv' | 'pdf' | 'html')[];
}

export interface DashboardStats {
  totalScans: number;
  completedScans: number;
  averageScanTime: number;
  totalIssuesFound: number;
  criticalIssuesResolved: number;
  projectsScanned: number;
  lastScanDate?: Date;
}

export interface ExportOptions {
  format: 'json' | 'csv' | 'pdf' | 'html';
  includeCodeSnippets: boolean;
  groupBy: 'severity' | 'category' | 'file';
  filters?: ScanFilter;
  customFields?: string[];
}
