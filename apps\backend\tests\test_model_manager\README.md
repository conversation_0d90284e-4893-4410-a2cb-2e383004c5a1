# Model Manager Test Suite

Comprehensive test suite for the model manager feature covering all aspects from unit tests to end-to-end testing.

## Test Structure

```
tests/test_model_manager/
├── conftest.py              # Test configuration and fixtures
├── pytest.ini              # Pytest configuration
├── test_runner.py           # Test runner script
├── __init__.py
├── unit/                    # Unit tests
│   ├── test_whisper_provider.py
│   └── test_ollama_provider.py
├── integration/             # Integration tests
│   ├── test_api_endpoints.py
│   └── test_services.py
├── e2e/                     # End-to-end tests
│   └── test_full_workflow.py
└── performance/             # Performance tests
    └── test_performance.py
```

## Coverage Areas

### Unit Tests (95%+ coverage target)

#### Whisper Provider (`test_whisper_provider.py`)
- **Catalog Operations**: Model listing, metadata extraction, catalog validation
- **Validation Methods**: Name validation, device compatibility, model support checks
- **Installation**: SDK mode, CLI mode, device detection, error scenarios
- **Configuration**: Parameter validation, device selection, language codes
- **Health Validation**: Model loading, inference testing, validation checks
- **Uninstallation**: Cleanup, idempotency, lock handling
- **Helper Methods**: Test audio creation, inference testing, metadata parsing

#### Ollama Provider (`test_ollama_provider.py`)
- **Catalog Operations**: Web scraping, CLI listing, caching, pagination
- **Validation**: Name validation, model existence, CLI availability
- **Installation**: Model pulling, version handling, manifest creation
- **Configuration**: Parameter storage, validation
- **Uninstallation**: CLI removal, local cleanup, error tolerance
- **Helper Methods**: Path generation, manifest parsing, caching logic

### Integration Tests (90%+ coverage target)

#### API Endpoints (`test_api_endpoints.py`)
- **Available Models Endpoint**: Pagination, filtering, provider selection
- **Installed Models Endpoint**: Listing, metadata formatting
- **Install Endpoint**: Request validation, async processing, error handling
- **Configure Endpoint**: Parameter validation, JSON handling
- **Delete Endpoint**: Model removal, version handling
- **Health Endpoint**: System status, executable checks
- **Diagnostics Endpoint**: Disk usage, cache stats, monitoring

#### Service Layer (`test_services.py`)
- **Catalog Service**: Provider coordination, filtering, pagination
- **Inventory Service**: Filesystem scanning, manifest parsing
- **Install Service**: Provider routing, error propagation
- **Config Service**: Parameter validation, provider delegation
- **Error Handling**: Cross-service error propagation, partial failures

### End-to-End Tests (`test_full_workflow.py`)
- **Complete Whisper Workflow**: Install → Configure → Health Check → Delete
- **Complete Ollama Workflow**: Install → Configure → Health Check → Delete
- **Error Scenarios**: Disk space, concurrent access, validation errors
- **Performance Scenarios**: Large catalogs, concurrent requests, memory usage
- **Security Scenarios**: Path traversal, injection protection, resource limits

### Performance Tests (`test_performance.py`)
- **Provider Performance**: Catalog retrieval, concurrent operations, memory usage
- **Service Performance**: Large datasets, concurrent access, filesystem stress
- **API Performance**: Response times, concurrent requests, memory leaks
- **Cache Performance**: Hit/miss ratios, cache efficiency
- **Scalability Limits**: Maximum concurrent operations, large configurations

## Frontend Test Structure

```
frontend/src/app/models/__tests__/
├── models-page.test.tsx
├── model-service.test.ts
└── component-integration.test.tsx
```

### Frontend Coverage Areas

#### Models Page Component (`models-page.test.tsx`)
- **Initial Loading**: Loading states, error handling, data fetching
- **Tab Navigation**: State persistence, content switching
- **Search Functionality**: Filtering, real-time updates
- **Model Installation**: Progress tracking, error handling, cancellation
- **Model Configuration**: Modal interactions, JSON validation, saving
- **Model Deletion**: Confirmation dialogs, optimistic updates
- **Health Monitoring**: Automatic checks, manual triggers, status display
- **Log Viewing**: Window opening, error handling
- **Error Boundaries**: Crash recovery, fallback UI
- **Accessibility**: ARIA labels, keyboard navigation
- **Performance**: Large datasets, memory management

#### Model Service (`model-service.test.ts`)
- **API Integration**: All service methods, parameter handling
- **Error Handling**: Network errors, timeouts, API responses
- **Real-time Updates**: SSE subscriptions, progress tracking
- **Parameter Validation**: Input sanitization, type checking
- **Concurrent Requests**: Race conditions, cleanup
- **Memory Management**: Subscription cleanup, leak prevention

#### Component Integration (`component-integration.test.tsx`)
- **ModelCard Component**: Information display, button interactions, loading states
- **AvailableModelCard**: Installation triggers, metadata display
- **InstallProgressCard**: Progress visualization, cancellation
- **Component Interactions**: Workflow coordination, state management

## Running Tests

### Backend Tests

```bash
# Run all tests
python test_runner.py --type all

# Run specific test types
python test_runner.py --type unit
python test_runner.py --type integration
python test_runner.py --type e2e
python test_runner.py --type performance

# Run specific test file
python test_runner.py --test unit/test_whisper_provider.py

# Run with coverage
python test_runner.py --type all --coverage

# Using pytest directly
pytest unit/ -v --tb=short
pytest integration/ -v --tb=short
pytest e2e/ -v --tb=short
pytest performance/ -v --tb=short
```

### Frontend Tests

```bash
# Run all frontend tests
npm test

# Run tests in watch mode
npm test:watch

# Run tests with coverage
npm test:coverage

# Run specific test file
npm test models-page.test.tsx

# Run tests in UI mode
npm test:ui
```

## Test Data and Fixtures

### Mock Data
- **Sample Models**: Whisper and Ollama model definitions
- **Progress Data**: Installation progress states
- **Health Results**: Model health check responses
- **API Responses**: Complete API response structures
- **Error Scenarios**: Various error conditions and edge cases

### Test Fixtures
- **Temporary Directories**: Isolated filesystem testing
- **Mock Configurations**: Test-specific configurations
- **Provider Mocks**: Filesystem, process, cache adapters
- **Service Instances**: Pre-configured service objects
- **Real-time Clients**: Mock SSE and WebSocket clients

## Coverage Targets

| Component | Target Coverage | Current Status |
|-----------|----------------|----------------|
| Whisper Provider | 95% | ✅ Complete |
| Ollama Provider | 95% | ✅ Complete |
| API Endpoints | 90% | ✅ Complete |
| Service Layer | 90% | ✅ Complete |
| Frontend Components | 85% | ✅ Complete |
| Frontend Services | 90% | ✅ Complete |
| E2E Workflows | 80% | ✅ Complete |
| Performance Tests | 70% | ✅ Complete |

## Key Testing Strategies

1. **Comprehensive Mocking**: All external dependencies mocked for isolation
2. **Real Integration**: Service layer tests use real component interactions
3. **Error Simulation**: Extensive error scenario coverage
4. **Performance Validation**: Load testing and memory monitoring
5. **Security Testing**: Input validation and attack vector testing
6. **Accessibility Testing**: ARIA compliance and keyboard navigation
7. **Browser Compatibility**: Cross-browser testing considerations
8. **Real-time Testing**: SSE and WebSocket functionality

## Continuous Integration

The test suite is designed to run in CI/CD pipelines with:
- Parallel test execution
- Coverage reporting
- Performance benchmarking
- Security scanning
- Accessibility auditing

## Maintenance

- Tests are organized by functionality for easy maintenance
- Mock data is centralized in fixtures
- Test utilities are reusable across test files
- Coverage reports highlight areas needing attention
- Performance benchmarks detect regressions

This comprehensive test suite ensures the model manager feature is robust, performant, and user-friendly while maintaining high code quality standards.
