"""API routes for LONI backend."""
from typing import List, Optional
from fastapi import HTT<PERSON>Exception
from fastapi.responses import J<PERSON><PERSON>esponse

from app.api_models import (
    ModelInfo,
    TranscriptionRequest,
    TranscriptionResponse,
    ErrorResponse,
    HealthResponse
)
from app.config import settings
from app.logger import get_logger, log_model_operation
from app.models.model_manager import ModelManager
from app.services.whisper_service import WhisperService

logger = get_logger("routes")

# Initialize services (lazy loading)
model_manager: Optional[ModelManager] = None
whisper_service: Optional[WhisperService] = None


def get_model_manager() -> ModelManager:
    """Get or create model manager instance."""
    global model_manager
    if model_manager is None:
        model_manager = ModelManager()
    return model_manager


def get_whisper_service() -> WhisperService:
    """Get or create whisper service instance."""
    global whisper_service
    if whisper_service is None:
        whisper_service = WhisperService(model_name=settings.whisper_model)
    return whisper_service


async def health_check() -> HealthResponse:
    """Health check endpoint."""
    return HealthResponse(
        status="ok",
        version=settings.app_version,
        timestamp="2024-01-15T10:00:00Z"  # In production, use actual timestamp
    )


async def root() -> dict:
    """Root endpoint."""
    return {
        "message": "Welcome to LONI Backend API",
        "version": settings.app_version,
        "docs_url": "/docs"
    }


async def list_models() -> List[ModelInfo]:
    """List available Ollama models."""
    try:
        logger.info("Listing available Ollama models")
        models = await get_model_manager().list_models()

        log_model_operation(
            model_name="all",
            operation="list",
            model_count=len(models),
            available_models=models
        )

        # Convert to ModelInfo objects
        model_infos = [
            ModelInfo(
                name=model_name,
                status="installed",
                description=f"Ollama model: {model_name}"
            )
            for model_name in models
        ]

        return model_infos
    except Exception as e:
        logger.error(
            "Failed to list models",
            error=str(e),
            error_type=type(e).__name__
        )
        raise HTTPException(status_code=500, detail="Failed to list models")


async def pull_model(model_name: str) -> dict:
    """Pull an Ollama model."""
    try:
        logger.info("Pulling Ollama model", model_name=model_name)

        import time
        start_time = time.time()
        result = await get_model_manager().pull_model(model_name)
        duration = time.time() - start_time

        log_model_operation(
            model_name=model_name,
            operation="pull",
            duration=duration,
            success=True
        )

        return {"message": f"Successfully pulled model {model_name}", "result": result}
    except Exception as e:
        logger.error(
            "Failed to pull model",
            model_name=model_name,
            error=str(e),
            error_type=type(e).__name__
        )
        raise HTTPException(status_code=500, detail=f"Failed to pull model {model_name}")


async def set_model(model_name: str) -> dict:
    """Set the active Ollama model."""
    try:
        logger.info("Setting active Ollama model", model_name=model_name)

        await get_model_manager().set_model(model_name)

        log_model_operation(
            model_name=model_name,
            operation="set_active",
            success=True
        )

        return {"message": f"Successfully set active model to {model_name}"}
    except Exception as e:
        logger.error(
            "Failed to set active model",
            model_name=model_name,
            error=str(e),
            error_type=type(e).__name__
        )
        raise HTTPException(status_code=500, detail=f"Failed to set model {model_name}")


async def use_model(model_name: str, input_data: str) -> dict:
    """Use an Ollama model for inference."""
    try:
        logger.info(
            "Using Ollama model for inference",
            model_name=model_name,
            input_length=len(input_data)
        )

        import time
        start_time = time.time()
        result = await get_model_manager().use_model(model_name, input_data)
        duration = time.time() - start_time

        log_model_operation(
            model_name=model_name,
            operation="inference",
            duration=duration,
            input_tokens=len(input_data.split()),
            output_tokens=len(result.get("response", "").split()) if isinstance(result, dict) else len(result.split()),
            success=True
        )

        return {"result": result.get("response") if isinstance(result, dict) else result}
    except Exception as e:
        logger.error(
            "Failed to use model for inference",
            model_name=model_name,
            error=str(e),
            error_type=type(e).__name__
        )
        raise HTTPException(status_code=500, detail=f"Failed to use model {model_name}")


async def transcribe_audio(request: TranscriptionRequest) -> TranscriptionResponse:
    """Transcribe audio using Whisper."""
    try:
        transcription = await get_whisper_service().transcribe(request.audio_path)
        return TranscriptionResponse(
            transcription=transcription,
            language=request.language,
            duration=None,  # Could be extracted from audio file
            word_count=len(transcription.split()) if transcription else 0,
            confidence=0.95  # Mock confidence score
        )
    except Exception as e:
        logger.error(
            "Failed to transcribe audio",
            audio_path=request.audio_path,
            error=str(e),
            error_type=type(e).__name__
        )
        raise HTTPException(status_code=500, detail=f"Failed to transcribe audio")
