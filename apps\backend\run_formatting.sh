#!/bin/bash
# Script to run backend code formatting with logging

# Set variables
PROJECT_ROOT="/mnt/e/Projects/lonors/loni"
BACKEND_DIR="$PROJECT_ROOT/apps/backend"
LOG_FILE="$PROJECT_ROOT/apps/data/logs/devtools-backend-format.log"

# Create log file if it doesn't exist
mkdir -p "$PROJECT_ROOT/apps/data/logs"
touch "$LOG_FILE"

# Function to log messages
log_message() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

# Start formatting execution
log_message "Starting backend code formatting execution"

# Change to backend directory
cd "$BACKEND_DIR" || { log_message "ERROR: Failed to change to backend directory"; exit 1; }

# Run formatting with black and isort
log_message "Running black formatter..."
uv run black app/ 2>&1 | tee -a "$LOG_FILE"

if [ ${PIPESTATUS[0]} -eq 0 ]; then
    log_message "Running isort import sorter..."
    uv run isort app/ 2>&1 | tee -a "$LOG_FILE"
    
    if [ ${PIPESTATUS[0]} -eq 0 ]; then
        log_message "Backend code formatting completed successfully"
    else
        log_message "ERROR: isort failed"
        exit 1
    fi
else
    log_message "ERROR: black formatter failed"
    exit 1
fi