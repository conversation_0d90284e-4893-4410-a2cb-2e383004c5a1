"""
Tests for node endpoints.
"""

import pytest
from fastapi.testclient import Test<PERSON><PERSON>
from sqlalchemy.ext.asyncio import AsyncSession
from app.main import create_app
from app.models.user import User
from app.models.canvas import Canvas, Node


@pytest.fixture
def client() -> TestClient:
    """Create a test client for the FastAPI app."""
    app = create_app()
    return TestClient(app)


@pytest.fixture
def auth_headers(client: TestClient, test_user: User) -> dict:
    """Get authentication headers for a test user."""
    # Login to get a token
    response = client.post("/api/v1/auth/login", data={
        "username": "<EMAIL>",
        "password": "password123"
    })
    
    assert response.status_code == 200
    token_data = response.json()
    access_token = token_data["access_token"]
    
    return {"Authorization": f"Bearer {access_token}"}


@pytest.mark.asyncio
async def test_create_node(client: TestClient, auth_headers: dict, test_canvas: Canvas):
    """Test create node endpoint."""
    response = client.post(f"/api/v1/canvas/{test_canvas.id}/nodes", json={
        "type": "ui",
        "title": "Test Node",
        "description": "A test node",
        "position": {"x": 100, "y": 100},
        "size": {"width": 150, "height": 100}
    }, headers=auth_headers)
    
    assert response.status_code == 200
    data = response.json()
    assert data["type"] == "ui"
    assert data["title"] == "Test Node"
    assert data["description"] == "A test node"
    assert data["position"]["x"] == 100
    assert data["position"]["y"] == 100
    assert data["size"]["width"] == 150
    assert data["size"]["height"] == 100
    assert "id" in data


@pytest.mark.asyncio
async def test_update_node(client: TestClient, auth_headers: dict, test_node: Node):
    """Test update node endpoint."""
    response = client.put(f"/api/v1/canvas/nodes/{test_node.id}", json={
        "title": "Updated Node",
        "description": "An updated test node",
        "position": {"x": 200, "y": 200}
    }, headers=auth_headers)
    
    assert response.status_code == 200
    data = response.json()
    assert data["title"] == "Updated Node"
    assert data["description"] == "An updated test node"
    assert data["position"]["x"] == 200
    assert data["position"]["y"] == 200


@pytest.mark.asyncio
async def test_delete_node(client: TestClient, auth_headers: dict, test_node: Node):
    """Test delete node endpoint."""
    response = client.delete(f"/api/v1/canvas/nodes/{test_node.id}", headers=auth_headers)
    
    assert response.status_code == 200
    data = response.json()
    assert data["message"] == "Node deleted successfully"
    
    # Verify node is deleted
    # This would require a get node endpoint, but we can test by trying to update it
    update_response = client.put(f"/api/v1/canvas/nodes/{test_node.id}", json={
        "title": "Should Fail"
    }, headers=auth_headers)
    assert update_response.status_code == 404