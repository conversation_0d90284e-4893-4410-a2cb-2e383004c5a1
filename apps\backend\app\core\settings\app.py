"""
Application configuration settings for the LONI backend application.
"""

from typing import List

from pydantic import AnyHttpUrl, Field, validator
from pydantic_settings import BaseSettings


class AppSettings(BaseSettings):
    """Main application configuration settings."""
    
    # Basic app settings
    PROJECT_NAME: str = Field(env="PROJECT_NAME", default="LONI Backend API")
    VERSION: str = Field(env="VERSION", default="0.1.0")
    DEBUG: bool = Field(env="DEBUG", default=False)
    ENVIRONMENT: str = Field(env="ENVIRONMENT", default="development")
    
    # API settings
    API_V1_STR: str = Field(env="API_V1_STR", default="/api/v1")
    ENABLE_OPENAPI: bool = Field(env="ENABLE_OPENAPI", default=True)
    
    # Server settings
    HOST: str = Field(env="HOST", default="0.0.0.0")
    PORT: int = Field(env="PORT", default=8000)
    
    # CORS settings
    BACKEND_CORS_ORIGINS: List[AnyHttpUrl] | List[str] = Field(
        env="BACKEND_CORS_ORIGINS",
        default=[]
    )
    ALLOWED_HOSTS: List[str] = Field(
        env="ALLOWED_HOSTS",
        default=["localhost", "127.0.0.1"]
    )
    
    # Logging settings
    LOG_LEVEL: str = Field(env="LOG_LEVEL", default="info")
    LOG_JSON: bool = Field(env="LOG_JSON", default=False)
    
    # File upload settings
    MAX_UPLOAD_SIZE: int = Field(env="MAX_UPLOAD_SIZE", default=10 * 1024 * 1024)  # 10MB
    UPLOAD_DIRECTORY: str = Field(env="UPLOAD_DIRECTORY", default="./uploads")
    
    # Canvas settings
    MAX_NODES_PER_CANVAS: int = Field(env="MAX_NODES_PER_CANVAS", default=1000)
    MAX_CONNECTIONS_PER_CANVAS: int = Field(env="MAX_CONNECTIONS_PER_CANVAS", default=2000)
    
    # Model Manager settings
    MODEL_MGR_OLLAMA_API_BASE: str = Field(env="MODEL_MGR_OLLAMA_API_BASE", default="http://localhost:11434")
    MODEL_MGR_OLLAMA_EXECUTABLE: str = Field(env="MODEL_MGR_OLLAMA_EXECUTABLE", default="ollama")
    MODEL_MGR_OLLAMA_DATA_DIR: str = Field(env="MODEL_MGR_OLLAMA_DATA_DIR", default="./data/models/ollama")
    MODEL_MGR_STREAMLIT_PORT: int = Field(env="MODEL_MGR_STREAMLIT_PORT", default=8501)
    MODEL_MGR_WHISPER_USE_CLI: bool = Field(env="MODEL_MGR_WHISPER_USE_CLI", default=True)
    MODEL_MGR_WHISPER_EXECUTABLE: str = Field(env="MODEL_MGR_WHISPER_EXECUTABLE", default="whisper")
    MODEL_MGR_ALLOW_OLLAMA_SYMLINK: bool = Field(env="MODEL_MGR_ALLOW_OLLAMA_SYMLINK", default=False)
    
    @validator("BACKEND_CORS_ORIGINS", pre=True)
    def assemble_cors_origins(cls, v):
        """Parse CORS origins from string or list."""
        if isinstance(v, str) and v:
            return [i.strip() for i in v.split(",") if i.strip()]
        elif isinstance(v, list):
            return v
        # Only allow localhost in development, require explicit configuration for production
        return []
    
    @validator("ALLOWED_HOSTS", pre=True)
    def assemble_allowed_hosts(cls, v):
        """Parse allowed hosts from string or list."""
        if isinstance(v, str) and v:
            return [i.strip() for i in v.split(",") if i.strip()]
        elif isinstance(v, list):
            return v
        return ["localhost", "127.0.0.1"]
    
    class Config:
        env_file = ".env"
        case_sensitive = True