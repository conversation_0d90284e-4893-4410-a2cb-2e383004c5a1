#!/usr/bin/env python3
"""
TDD Workflow Automation Script
Implements comprehensive Test-Driven Development workflow with London School methodology
"""

import argparse
import os
import subprocess
import sys
import json
import time
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import re


class TDDPhase(Enum):
    """TDD cycle phases"""
    RED = "red"      # Write failing test
    GREEN = "green"  # Make test pass
    REFACTOR = "refactor"  # Improve code


@dataclass
class TestResult:
    """Test execution result"""
    passed: bool
    coverage: float
    failed_count: int
    total_count: int
    execution_time: float
    output: str


@dataclass
class TDDConfig:
    """TDD workflow configuration"""
    backend_path: Path
    frontend_path: Path
    min_coverage_backend: float = 80.0
    min_coverage_frontend: float = 75.0
    enable_london_school: bool = True
    auto_discovery: bool = True
    watch_mode: bool = False


class TDDWorkflow:
    """Main TDD workflow orchestrator"""
    
    def __init__(self, config: TDDConfig):
        self.config = config
        self.current_phase = TDDPhase.RED
        self.test_history: List[TestResult] = []
        
    def enforce_test_first(self, file_path: str, content: str) -> bool:
        """Enforce test-first development by checking if tests exist for implementation"""
        if not file_path.endswith(('.py', '.ts', '.tsx', '.js', '.jsx')):
            return True
            
        # Check if this is already a test file
        if any(marker in file_path for marker in ['test_', '.test.', '.spec.', '_test.py']):
            return True
            
        # Find corresponding test file
        test_file = self._find_test_file(file_path)
        
        if not test_file or not os.path.exists(test_file):
            print(f"❌ Test-first violation: No test file found for {file_path}")
            print(f"   Expected test file: {test_file}")
            return False
            
        # Check if test file has tests for the implementation
        if not self._has_corresponding_tests(file_path, test_file, content):
            print(f"❌ Test-first violation: No tests found for new implementation in {file_path}")
            return False
            
        return True
    
    def _find_test_file(self, impl_file: str) -> Optional[str]:
        """Find corresponding test file for implementation"""
        impl_path = Path(impl_file)
        
        # Backend Python tests
        if impl_file.endswith('.py'):
            if 'apps/backend/app' in impl_file:
                rel_path = impl_path.relative_to(Path(impl_file).parent.parent.parent)
                test_path = self.config.backend_path / 'tests' / f"test_{rel_path}"
                return str(test_path)
                
        # Frontend TypeScript/JavaScript tests  
        elif impl_file.endswith(('.ts', '.tsx', '.js', '.jsx')):
            if 'apps/frontend/src' in impl_file:
                # Jest/Vitest style: filename.test.ts
                return impl_file.replace('.ts', '.test.ts').replace('.tsx', '.test.tsx')
                
        return None
    
    def _has_corresponding_tests(self, impl_file: str, test_file: str, content: str) -> bool:
        """Check if test file has tests for implementation content"""
        if not os.path.exists(test_file):
            return False
            
        with open(test_file, 'r') as f:
            test_content = f.read()
            
        # Extract function/class names from implementation
        impl_symbols = self._extract_symbols(impl_file, content)
        
        # Check if test file references these symbols
        for symbol in impl_symbols:
            if symbol in test_content:
                return True
                
        return len(impl_symbols) == 0  # Allow empty files
    
    def _extract_symbols(self, file_path: str, content: str) -> List[str]:
        """Extract function/class names from code"""
        symbols = []
        
        if file_path.endswith('.py'):
            # Python: extract functions and classes
            symbols.extend(re.findall(r'^def\s+(\w+)', content, re.MULTILINE))
            symbols.extend(re.findall(r'^class\s+(\w+)', content, re.MULTILINE))
        elif file_path.endswith(('.ts', '.tsx', '.js', '.jsx')):
            # TypeScript/JavaScript: extract functions and classes
            symbols.extend(re.findall(r'^(?:export\s+)?(?:async\s+)?function\s+(\w+)', content, re.MULTILINE))
            symbols.extend(re.findall(r'^(?:export\s+)?class\s+(\w+)', content, re.MULTILINE))
            symbols.extend(re.findall(r'^(?:export\s+)?const\s+(\w+)\s*=', content, re.MULTILINE))
            
        return symbols
    
    def run_backend_tests(self) -> TestResult:
        """Run backend tests with coverage"""
        start_time = time.time()
        
        try:
            # Change to backend directory
            os.chdir(self.config.backend_path)
            
            # Run tests with coverage
            result = subprocess.run([
                'uv', 'run', 'python', '-m', 'pytest',
                'tests/',
                '--cov=app',
                '--cov-report=json',
                '--cov-report=term-missing',
                '-v'
            ], capture_output=True, text=True, timeout=300)
            
            execution_time = time.time() - start_time
            
            # Parse coverage from coverage.json if it exists
            coverage = 0.0
            coverage_file = self.config.backend_path / 'coverage.json'
            if coverage_file.exists():
                with open(coverage_file) as f:
                    cov_data = json.load(f)
                    coverage = cov_data.get('totals', {}).get('percent_covered', 0.0)
            
            # Parse test results
            output = result.stdout + result.stderr
            failed_count = len(re.findall(r'FAILED', output))
            passed_count = len(re.findall(r'PASSED', output))
            total_count = failed_count + passed_count
            
            return TestResult(
                passed=result.returncode == 0,
                coverage=coverage,
                failed_count=failed_count,
                total_count=total_count,
                execution_time=execution_time,
                output=output
            )
            
        except subprocess.TimeoutExpired:
            return TestResult(
                passed=False,
                coverage=0.0,
                failed_count=0,
                total_count=0,
                execution_time=time.time() - start_time,
                output="Test execution timed out"
            )
        except Exception as e:
            return TestResult(
                passed=False,
                coverage=0.0,
                failed_count=0,
                total_count=0,
                execution_time=time.time() - start_time,
                output=f"Test execution failed: {str(e)}"
            )
    
    def run_frontend_tests(self) -> TestResult:
        """Run frontend tests with coverage"""
        start_time = time.time()
        
        try:
            # Change to frontend directory
            os.chdir(self.config.frontend_path)
            
            # Run tests with coverage using bun
            result = subprocess.run([
                'bun', 'test', '--coverage'
            ], capture_output=True, text=True, timeout=300)
            
            execution_time = time.time() - start_time
            
            # Parse coverage from output (basic parsing)
            coverage = 0.0
            coverage_match = re.search(r'All files.*?(\d+\.?\d*)%', result.stdout)
            if coverage_match:
                coverage = float(coverage_match.group(1))
            
            # Parse test results
            output = result.stdout + result.stderr
            failed_count = len(re.findall(r'fail', output, re.IGNORECASE))
            passed_count = len(re.findall(r'pass', output, re.IGNORECASE))
            total_count = failed_count + passed_count
            
            return TestResult(
                passed=result.returncode == 0,
                coverage=coverage,
                failed_count=failed_count,
                total_count=total_count,
                execution_time=execution_time,
                output=output
            )
            
        except subprocess.TimeoutExpired:
            return TestResult(
                passed=False,
                coverage=0.0,
                failed_count=0,
                total_count=0,
                execution_time=time.time() - start_time,
                output="Test execution timed out"
            )
        except Exception as e:
            return TestResult(
                passed=False,
                coverage=0.0,
                failed_count=0,
                total_count=0,
                execution_time=time.time() - start_time,
                output=f"Test execution failed: {str(e)}"
            )
    
    def validate_quality_gates(self, backend_result: TestResult, frontend_result: TestResult) -> bool:
        """Validate quality gates for both backend and frontend"""
        backend_valid = (
            backend_result.passed and
            backend_result.coverage >= self.config.min_coverage_backend
        )
        
        frontend_valid = (
            frontend_result.passed and 
            frontend_result.coverage >= self.config.min_coverage_frontend
        )
        
        if not backend_valid:
            print(f"❌ Backend quality gate failed:")
            print(f"   Tests passed: {backend_result.passed}")
            print(f"   Coverage: {backend_result.coverage:.1f}% (required: {self.config.min_coverage_backend}%)")
        
        if not frontend_valid:
            print(f"❌ Frontend quality gate failed:")
            print(f"   Tests passed: {frontend_result.passed}")
            print(f"   Coverage: {frontend_result.coverage:.1f}% (required: {self.config.min_coverage_frontend}%)")
            
        return backend_valid and frontend_valid
    
    def run_tdd_cycle(self, component: str = "all") -> bool:
        """Run complete TDD cycle"""
        print(f"🔄 Running TDD cycle for {component}")
        
        if component in ["all", "backend"]:
            print("🐍 Running backend tests...")
            backend_result = self.run_backend_tests()
            self.test_history.append(backend_result)
            
            print(f"   Tests: {backend_result.total_count - backend_result.failed_count}/{backend_result.total_count}")
            print(f"   Coverage: {backend_result.coverage:.1f}%")
            print(f"   Time: {backend_result.execution_time:.2f}s")
            
            if not backend_result.passed:
                print(f"❌ Backend tests failed:")
                print(backend_result.output[-500:])  # Last 500 chars
        
        if component in ["all", "frontend"]:
            print("⚛️  Running frontend tests...")
            frontend_result = self.run_frontend_tests()
            self.test_history.append(frontend_result)
            
            print(f"   Tests: {frontend_result.total_count - frontend_result.failed_count}/{frontend_result.total_count}")
            print(f"   Coverage: {frontend_result.coverage:.1f}%")
            print(f"   Time: {frontend_result.execution_time:.2f}s")
            
            if not frontend_result.passed:
                print(f"❌ Frontend tests failed:")
                print(frontend_result.output[-500:])  # Last 500 chars
        
        # Validate quality gates
        if component == "all":
            backend_result = [r for r in self.test_history if 'python' in r.output or 'pytest' in r.output][-1]
            frontend_result = [r for r in self.test_history if 'bun' in r.output or 'jest' in r.output][-1]
            return self.validate_quality_gates(backend_result, frontend_result)
        
        return self.test_history[-1].passed
    
    def watch_mode(self):
        """Run tests in watch mode"""
        print("👀 Starting TDD watch mode...")
        print("   Watching for file changes...")
        
        try:
            while True:
                # Simple implementation - in production would use file system watchers
                time.sleep(2)
                
                # For now, just run tests periodically
                self.run_tdd_cycle()
                time.sleep(30)
                
        except KeyboardInterrupt:
            print("\n🛑 Stopping watch mode")


def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(description="TDD Workflow Automation")
    parser.add_argument('--component', choices=['all', 'backend', 'frontend'], 
                       default='all', help='Component to test')
    parser.add_argument('--watch', action='store_true', 
                       help='Run in watch mode')
    parser.add_argument('--enforce-test-first', action='store_true',
                       help='Enforce test-first development')
    parser.add_argument('--file', help='File to check for test-first compliance')
    parser.add_argument('--min-coverage-backend', type=float, default=80.0,
                       help='Minimum backend coverage percentage')
    parser.add_argument('--min-coverage-frontend', type=float, default=75.0,
                       help='Minimum frontend coverage percentage')
    
    args = parser.parse_args()
    
    # Setup paths
    script_dir = Path(__file__).parent.parent.parent
    backend_path = script_dir / 'apps' / 'backend'
    frontend_path = script_dir / 'apps' / 'frontend'
    
    config = TDDConfig(
        backend_path=backend_path,
        frontend_path=frontend_path,
        min_coverage_backend=args.min_coverage_backend,
        min_coverage_frontend=args.min_coverage_frontend
    )
    
    workflow = TDDWorkflow(config)
    
    if args.file and args.enforce_test_first:
        # Check single file for test-first compliance
        if os.path.exists(args.file):
            with open(args.file, 'r') as f:
                content = f.read()
            
            if workflow.enforce_test_first(args.file, content):
                print(f"✅ Test-first compliance validated for {args.file}")
                sys.exit(0)
            else:
                print(f"❌ Test-first compliance failed for {args.file}")
                sys.exit(1)
        else:
            print(f"❌ File not found: {args.file}")
            sys.exit(1)
    
    if args.watch:
        workflow.watch_mode()
    else:
        success = workflow.run_tdd_cycle(args.component)
        sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()