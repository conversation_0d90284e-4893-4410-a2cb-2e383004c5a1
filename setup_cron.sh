#!/bin/bash
# Script to setup cron job for log rotation

# Set variables
PROJECT_ROOT="/mnt/e/Projects/lonors/loni"
ROTATE_SCRIPT="$PROJECT_ROOT/rotate_logs.sh"

# Function to log messages
log_message() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1"
}

# Function to setup cron job
setup_cron() {
    log_message "Setting up cron job for log rotation"
    
    # Check if script exists and is executable
    if [[ ! -f "$ROTATE_SCRIPT" ]]; then
        log_message "ERROR: Log rotation script not found at $ROTATE_SCRIPT"
        return 1
    fi
    
    if [[ ! -x "$ROTATE_SCRIPT" ]]; then
        log_message "Making log rotation script executable"
        chmod +x "$ROTATE_SCRIPT"
    fi
    
    # Add cron job to run daily at 2 AM
    # We use a temporary file to avoid potential issues with crontab -e
    local temp_cron=$(mktemp)
    
    # Get current crontab
    crontab -l > "$temp_cron" 2>/dev/null || true
    
    # Remove any existing entries for this script
    sed -i "\|$ROTATE_SCRIPT|d" "$temp_cron"
    
    # Add new entry
    echo "0 2 * * * $ROTATE_SCRIPT" >> "$temp_cron"
    
    # Install the new crontab
    crontab "$temp_cron"
    
    # Clean up
    rm "$temp_cron"
    
    log_message "Cron job setup completed successfully"
    log_message "Log rotation will run daily at 2 AM"
}

# Main execution
main() {
    setup_cron
}

# Run main function
main