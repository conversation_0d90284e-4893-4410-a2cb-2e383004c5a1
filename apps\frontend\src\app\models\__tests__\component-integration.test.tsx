"""
Integration tests for model management UI components.
"""

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import { ModelCard, AvailableModelCard, InstallProgressCard } from '../page';
import { modelService } from '@/shared/services/model-service';
import { useToast } from '@/shared/ui/providers';

// Mock dependencies
vi.mock('@/shared/services/model-service');
vi.mock('@/shared/ui/providers');
vi.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
  },
}));

const mockModelService = vi.mocked(modelService);
const mockAddToast = vi.fn();
const mockUseToast = vi.mocked(useToast);

mockUseToast.mockReturnValue({
  addToast: mockAddToast,
});

describe('ModelCard Integration', () => {
  const sampleModel = {
    id: {
      provider: 'WHISPER',
      name: 'tiny',
      version: '1.0',
    },
    status: 'installed' as const,
    metadata: {
      size_on_disk_bytes: 40960000,
      installed_at: '2024-01-01T12:00:00Z',
      source: 'whisper_sdk',
      description: 'Tiny Whisper model for quick transcription',
      capabilities: ['transcription'],
      requirements: {
        memory_gb: 1,
        disk_gb: 1,
        gpu_required: false,
      },
      extra: {
        catalog_version: '1.0.0',
      },
    },
    healthStatus: {
      status: 'healthy' as const,
      details: {
        model_loaded: true,
        memory_usage: 256,
        response_time_ms: 120,
      },
      timestamp: '2024-01-01T12:00:00Z',
    },
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should render model information correctly', () => {
    const onConfigure = vi.fn();
    const onDelete = vi.fn();
    const onHealthCheck = vi.fn();
    const onViewLogs = vi.fn();

    render(
      <ModelCard
        model={sampleModel}
        onConfigure={onConfigure}
        onDelete={onDelete}
        onHealthCheck={onHealthCheck}
        onViewLogs={onViewLogs}
      />
    );

    // Check basic model info
    expect(screen.getByText('tiny')).toBeInTheDocument();
    expect(screen.getByText('WHISPER • v1.0')).toBeInTheDocument();
    expect(screen.getByText('Tiny Whisper model for quick transcription')).toBeInTheDocument();
    
    // Check metadata
    expect(screen.getByText('39.1 MB')).toBeInTheDocument();
    expect(screen.getByText('1/1/2024')).toBeInTheDocument();
    
    // Check capabilities
    expect(screen.getByText('transcription')).toBeInTheDocument();
    
    // Check health status
    expect(screen.getByText('Healthy')).toBeInTheDocument();
    expect(screen.getByText('Yes')).toBeInTheDocument(); // Model loaded
    expect(screen.getByText('120ms')).toBeInTheDocument(); // Response time
    expect(screen.getByText('256MB')).toBeInTheDocument(); // Memory usage
  });

  it('should handle configure button click', async () => {
    const onConfigure = vi.fn();
    const onDelete = vi.fn();
    const onHealthCheck = vi.fn();
    const onViewLogs = vi.fn();

    render(
      <ModelCard
        model={sampleModel}
        onConfigure={onConfigure}
        onDelete={onDelete}
        onHealthCheck={onHealthCheck}
        onViewLogs={onViewLogs}
      />
    );

    const configureButton = screen.getByText('Configure');
    await userEvent.click(configureButton);

    expect(onConfigure).toHaveBeenCalledWith(sampleModel);
  });

  it('should handle health check button click', async () => {
    const onConfigure = vi.fn();
    const onDelete = vi.fn();
    const onHealthCheck = vi.fn();
    const onViewLogs = vi.fn();

    render(
      <ModelCard
        model={sampleModel}
        onConfigure={onConfigure}
        onDelete={onDelete}
        onHealthCheck={onHealthCheck}
        onViewLogs={onViewLogs}
      />
    );

    const healthButton = screen.getByTitle('Run Health Check');
    await userEvent.click(healthButton);

    expect(onHealthCheck).toHaveBeenCalledWith('WHISPER', 'tiny');
  });

  it('should handle view logs button click', async () => {
    const onConfigure = vi.fn();
    const onDelete = vi.fn();
    const onHealthCheck = vi.fn();
    const onViewLogs = vi.fn();

    render(
      <ModelCard
        model={sampleModel}
        onConfigure={onConfigure}
        onDelete={onDelete}
        onHealthCheck={onHealthCheck}
        onViewLogs={onViewLogs}
      />
    );

    const logsButton = screen.getByTitle('View Logs');
    await userEvent.click(logsButton);

    expect(onViewLogs).toHaveBeenCalledWith('WHISPER', 'tiny');
  });

  it('should handle delete button click', async () => {
    const onConfigure = vi.fn();
    const onDelete = vi.fn();
    const onHealthCheck = vi.fn();
    const onViewLogs = vi.fn();

    render(
      <ModelCard
        model={sampleModel}
        onConfigure={onConfigure}
        onDelete={onDelete}
        onHealthCheck={onHealthCheck}
        onViewLogs={onViewLogs}
      />
    );

    const deleteButton = screen.getByTitle('Delete Model');
    await userEvent.click(deleteButton);

    expect(onDelete).toHaveBeenCalledWith('WHISPER', 'tiny');
  });

  it('should show loading states correctly', () => {
    const loading = {
      configure: true,
      delete: false,
      health: true,
    };

    render(
      <ModelCard
        model={sampleModel}
        onConfigure={vi.fn()}
        onDelete={vi.fn()}
        onHealthCheck={vi.fn()}
        onViewLogs={vi.fn()}
        loading={loading}
      />
    );

    // Configure button should show loading
    expect(screen.getByText('Configure').closest('button')).toBeDisabled();
    
    // Health check button should show loading
    expect(screen.getByTitle('Run Health Check').closest('button')).toBeDisabled();
    
    // Delete button should not be disabled
    expect(screen.getByTitle('Delete Model').closest('button')).not.toBeDisabled();
  });

  it('should handle unhealthy models', () => {
    const unhealthyModel = {
      ...sampleModel,
      healthStatus: {
        status: 'unhealthy' as const,
        details: {
          model_loaded: false,
          error_count: 3,
        },
        timestamp: '2024-01-01T12:00:00Z',
      },
    };

    render(
      <ModelCard
        model={unhealthyModel}
        onConfigure={vi.fn()}
        onDelete={vi.fn()}
        onHealthCheck={vi.fn()}
        onViewLogs={vi.fn()}
      />
    );

    expect(screen.getByText('Unhealthy')).toBeInTheDocument();
    expect(screen.getByText('No')).toBeInTheDocument(); // Model not loaded
  });
});

describe('AvailableModelCard Integration', () => {
  const sampleAvailableModel = {
    provider: 'WHISPER',
    name: 'base',
    version: '1.0',
    metadata: {
      size_bytes: 74000000,
      description: 'Base Whisper model with good accuracy',
      capabilities: ['transcription', 'translation'],
      requirements: {
        memory_gb: 2,
        disk_gb: 1,
        gpu_required: false,
      },
      popularity_score: 85,
      last_updated: '2024-01-01T00:00:00Z',
    },
  };

  it('should render available model information correctly', () => {
    const onInstall = vi.fn();

    render(
      <AvailableModelCard
        model={sampleAvailableModel}
        onInstall={onInstall}
        isInstalling={false}
      />
    );

    // Check basic info
    expect(screen.getByText('base')).toBeInTheDocument();
    expect(screen.getByText('WHISPER • v1.0')).toBeInTheDocument();
    expect(screen.getByText('Base Whisper model with good accuracy')).toBeInTheDocument();
    
    // Check metadata
    expect(screen.getByText('70.6 MB')).toBeInTheDocument();
    expect(screen.getByText('1/1/2024')).toBeInTheDocument();
    expect(screen.getByText('85')).toBeInTheDocument(); // Popularity score
    
    // Check capabilities
    expect(screen.getByText('transcription')).toBeInTheDocument();
    expect(screen.getByText('+1 more')).toBeInTheDocument();
    
    // Check requirements
    expect(screen.getByText('2GB RAM, 1GB Disk')).toBeInTheDocument();
  });

  it('should handle install button click', async () => {
    const onInstall = vi.fn();

    render(
      <AvailableModelCard
        model={sampleAvailableModel}
        onInstall={onInstall}
        isInstalling={false}
      />
    );

    const installButton = screen.getByText('Install');
    await userEvent.click(installButton);

    expect(onInstall).toHaveBeenCalledWith('WHISPER', 'base', '1.0');
  });

  it('should show installing state correctly', () => {
    const onInstall = vi.fn();

    render(
      <AvailableModelCard
        model={sampleAvailableModel}
        onInstall={onInstall}
        isInstalling={true}
      />
    );

    expect(screen.getByText('Installing...')).toBeInTheDocument();
    expect(screen.getByText('Installing...').closest('button')).toBeDisabled();
  });

  it('should handle GPU requirements correctly', () => {
    const gpuModel = {
      ...sampleAvailableModel,
      metadata: {
        ...sampleAvailableModel.metadata,
        requirements: {
          memory_gb: 8,
          disk_gb: 5,
          gpu_required: true,
        },
      },
    };

    render(
      <AvailableModelCard
        model={gpuModel}
        onInstall={vi.fn()}
        isInstalling={false}
      />
    );

    expect(screen.getByText('8GB RAM, 5GB Disk, GPU Required')).toBeInTheDocument();
  });
});

describe('InstallProgressCard Integration', () => {
  const sampleTask = {
    id: 'task-123',
    provider: 'WHISPER',
    name: 'base',
    version: '1.0',
    progress: {
      status: 'downloading' as const,
      progress: 65,
      downloaded_bytes: 650000000,
      total_bytes: **********,
      current_step: 'Downloading model weights',
    },
  };

  it('should render progress information correctly', () => {
    const onCancel = vi.fn();

    render(
      <InstallProgressCard
        task={sampleTask}
        onCancel={onCancel}
      />
    );

    // Check basic info
    expect(screen.getByText('base')).toBeInTheDocument();
    expect(screen.getByText('WHISPER • v1.0')).toBeInTheDocument();
    expect(screen.getByText('downloading')).toBeInTheDocument();
    
    // Check progress
    expect(screen.getByText('65%')).toBeInTheDocument();
    expect(screen.getByText('Downloading model weights')).toBeInTheDocument();
    
    // Check download progress
    expect(screen.getByText('619.9 MB / 953.7 MB')).toBeInTheDocument();
  });

  it('should handle cancel button click', async () => {
    const onCancel = vi.fn();

    render(
      <InstallProgressCard
        task={sampleTask}
        onCancel={onCancel}
      />
    );

    const cancelButton = screen.getByText('Cancel Installation');
    await userEvent.click(cancelButton);

    expect(onCancel).toHaveBeenCalledWith('task-123');
  });

  it('should show error state correctly', () => {
    const errorTask = {
      ...sampleTask,
      progress: {
        status: 'error' as const,
        progress: 0,
        error_message: 'Network connection failed',
      },
    };

    render(
      <InstallProgressCard
        task={errorTask}
        onCancel={vi.fn()}
      />
    );

    expect(screen.getByText('error')).toBeInTheDocument();
    expect(screen.getByText('Network connection failed')).toBeInTheDocument();
  });

  it('should disable cancel button when complete', () => {
    const completeTask = {
      ...sampleTask,
      progress: {
        status: 'complete' as const,
        progress: 100,
      },
    };

    render(
      <InstallProgressCard
        task={completeTask}
        onCancel={vi.fn()}
      />
    );

    const cancelButton = screen.getByText('Cancel Installation');
    expect(cancelButton.closest('button')).toBeDisabled();
  });

  it('should show correct progress bar color based on progress', () => {
    const lowProgressTask = {
      ...sampleTask,
      progress: {
        ...sampleTask.progress,
        progress: 15,
      },
    };

    const { rerender } = render(
      <InstallProgressCard
        task={lowProgressTask}
        onCancel={vi.fn()}
      />
    );

    // Low progress should be red (< 30%)
    expect(screen.getByText('15%')).toBeInTheDocument();

    // Medium progress should be yellow
    const mediumProgressTask = {
      ...sampleTask,
      progress: {
        ...sampleTask.progress,
        progress: 50,
      },
    };

    rerender(
      <InstallProgressCard
        task={mediumProgressTask}
        onCancel={vi.fn()}
      />
    );

    expect(screen.getByText('50%')).toBeInTheDocument();

    // High progress should be green
    const highProgressTask = {
      ...sampleTask,
      progress: {
        ...sampleTask.progress,
        progress: 85,
      },
    };

    rerender(
      <InstallProgressCard
        task={highProgressTask}
        onCancel={vi.fn()}
      />
    );

    expect(screen.getByText('85%')).toBeInTheDocument();
  });
});

describe('Component Interactions', () => {
  it('should handle model card to configuration modal workflow', async () => {
    const sampleModel = {
      id: { provider: 'WHISPER', name: 'tiny', version: '1.0' },
      status: 'installed' as const,
      metadata: {
        size_on_disk_bytes: 40960000,
        extra: { existing_config: { temperature: 0.5 } },
      },
    };

    const onConfigure = vi.fn();

    render(
      <ModelCard
        model={sampleModel}
        onConfigure={onConfigure}
        onDelete={vi.fn()}
        onHealthCheck={vi.fn()}
        onViewLogs={vi.fn()}
      />
    );

    const configureButton = screen.getByText('Configure');
    await userEvent.click(configureButton);

    expect(onConfigure).toHaveBeenCalledWith(sampleModel);
  });

  it('should handle installation flow from available to progress card', async () => {
    const availableModel = {
      provider: 'WHISPER',
      name: 'base',
      version: '1.0',
      metadata: {
        size_bytes: 74000000,
        description: 'Base model',
      },
    };

    const onInstall = vi.fn();

    const { rerender } = render(
      <AvailableModelCard
        model={availableModel}
        onInstall={onInstall}
        isInstalling={false}
      />
    );

    // Click install
    const installButton = screen.getByText('Install');
    await userEvent.click(installButton);

    expect(onInstall).toHaveBeenCalledWith('WHISPER', 'base', '1.0');

    // Simulate installation started
    rerender(
      <AvailableModelCard
        model={availableModel}
        onInstall={onInstall}
        isInstalling={true}
      />
    );

    expect(screen.getByText('Installing...')).toBeInTheDocument();
    expect(screen.getByText('Installing...').closest('button')).toBeDisabled();
  });

  it('should handle real-time health status updates', () => {
    const initialModel = {
      id: { provider: 'WHISPER', name: 'tiny', version: '1.0' },
      status: 'installed' as const,
      metadata: { size_on_disk_bytes: 40960000 },
      healthStatus: undefined,
    };

    const { rerender } = render(
      <ModelCard
        model={initialModel}
        onConfigure={vi.fn()}
        onDelete={vi.fn()}
        onHealthCheck={vi.fn()}
        onViewLogs={vi.fn()}
      />
    );

    // Initially should show unknown health
    expect(screen.getByText('Unknown')).toBeInTheDocument();

    // Update with health status
    const updatedModel = {
      ...initialModel,
      healthStatus: {
        status: 'healthy' as const,
        details: { model_loaded: true },
        timestamp: '2024-01-01T12:00:00Z',
      },
    };

    rerender(
      <ModelCard
        model={updatedModel}
        onConfigure={vi.fn()}
        onDelete={vi.fn()}
        onHealthCheck={vi.fn()}
        onViewLogs={vi.fn()}
      />
    );

    expect(screen.getByText('Healthy')).toBeInTheDocument();
  });
});
