#!/bin/bash
# Dependency checking and security audit script
# This script checks for outdated and vulnerable dependencies

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/../.." && pwd)"
BACKEND_DIR="$PROJECT_ROOT/apps/backend"
FRONTEND_DIR="$PROJECT_ROOT/apps/frontend"
LOG_FILE="$PROJECT_ROOT/apps/data/logs/dependency-check.log"

# Logging function
log_message() {
    local level=$1
    local message=$2
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo -e "${!level}[$level] $timestamp: $message${NC}" | tee -a "$LOG_FILE"
}

info_message() {
    log_message "BLUE" "$1"
}

success_message() {
    log_message "GREEN" "$1"
}

warning_message() {
    log_message "YELLOW" "$1"
}

error_message() {
    log_message "RED" "$1"
}

# Create log directory
mkdir -p "$PROJECT_ROOT/apps/data/logs"

info_message "Starting dependency security and update check..."

# Check Python dependencies
check_python_deps() {
    info_message "Checking Python dependencies..."
    
    cd "$BACKEND_DIR"
    
    # Security scan with safety
    info_message "Running safety security scan..."
    if uv run safety check --json > safety_report.json 2>/dev/null; then
        local vulnerabilities=$(cat safety_report.json | jq length 2>/dev/null || echo "0")
        if [[ "$vulnerabilities" -gt 0 ]]; then
            warning_message "Found $vulnerabilities security vulnerabilities in Python dependencies"
            cat safety_report.json | jq -r '.[] | "- \(.package_name) \(.installed_version): \(.vulnerability_id) - \(.advisory)"' 2>/dev/null || cat safety_report.json
        else
            success_message "No security vulnerabilities found in Python dependencies"
        fi
        rm -f safety_report.json
    else
        warning_message "Safety scan failed or returned errors"
    fi
    
    # Check for outdated packages
    info_message "Checking for outdated Python packages..."
    if outdated_output=$(uv pip list --outdated 2>/dev/null); then
        if [[ -n "$outdated_output" && "$outdated_output" != *"Package"* ]]; then
            warning_message "Outdated Python packages found:"
            echo "$outdated_output"
        else
            success_message "All Python packages are up to date"
        fi
    else
        info_message "Could not check for outdated Python packages"
    fi
    
    # Audit with bandit
    info_message "Running bandit security analysis..."
    if uv run bandit -r app/ -f json -o bandit_report.json 2>/dev/null; then
        local issues=$(cat bandit_report.json | jq '.results | length' 2>/dev/null || echo "0")
        if [[ "$issues" -gt 0 ]]; then
            warning_message "Found $issues potential security issues in Python code"
            cat bandit_report.json | jq -r '.results[] | "- \(.filename):\(.line_number) - \(.test_name): \(.issue_text)"' 2>/dev/null || cat bandit_report.json
        else
            success_message "No security issues found in Python code"
        fi
        rm -f bandit_report.json
    else
        info_message "Bandit analysis completed with warnings or errors"
    fi
}

# Check JavaScript dependencies
check_js_deps() {
    info_message "Checking JavaScript dependencies..."
    
    cd "$FRONTEND_DIR"
    
    # Security audit with npm
    info_message "Running npm security audit..."
    if npm audit --json > npm_audit.json 2>/dev/null; then
        local vulnerabilities=$(cat npm_audit.json | jq '.metadata.vulnerabilities.total' 2>/dev/null || echo "0")
        if [[ "$vulnerabilities" -gt 0 ]]; then
            warning_message "Found $vulnerabilities security vulnerabilities in JavaScript dependencies"
            cat npm_audit.json | jq -r '.advisories[] | "- \(.module_name) \(.vulnerable_versions): \(.severity) - \(.title)"' 2>/dev/null || cat npm_audit.json
            
            info_message "Attempting to fix vulnerabilities automatically..."
            npm audit fix --dry-run
        else
            success_message "No security vulnerabilities found in JavaScript dependencies"
        fi
        rm -f npm_audit.json
    else
        # Try with bun audit if npm audit fails
        info_message "Trying bun audit..."
        if bun audit 2>&1; then
            success_message "Bun audit completed"
        else
            warning_message "Could not run security audit on JavaScript dependencies"
        fi
    fi
    
    # Check for outdated packages
    info_message "Checking for outdated JavaScript packages..."
    if bun outdated 2>/dev/null; then
        info_message "Outdated package check completed"
    else
        if npm outdated 2>/dev/null; then
            info_message "Outdated package check completed with npm"
        else
            warning_message "Could not check for outdated JavaScript packages"
        fi
    fi
}

# Check Docker images
check_docker_images() {
    if command -v docker &> /dev/null; then
        info_message "Checking Docker images for vulnerabilities..."
        
        # Check if docker scout is available
        if docker scout version &>/dev/null; then
            info_message "Running Docker Scout security scan..."
            
            # Scan backend image
            if docker scout quickview "$PROJECT_ROOT/apps/backend/Dockerfile" 2>/dev/null; then
                success_message "Backend Docker image scan completed"
            else
                warning_message "Could not scan backend Docker image"
            fi
            
            # Scan frontend image
            if docker scout quickview "$PROJECT_ROOT/apps/frontend/Dockerfile" 2>/dev/null; then
                success_message "Frontend Docker image scan completed"
            else
                warning_message "Could not scan frontend Docker image"
            fi
        else
            info_message "Docker Scout not available. Skipping container vulnerability scanning."
        fi
    else
        info_message "Docker not available. Skipping container checks."
    fi
}

# Generate dependency report
generate_report() {
    local report_file="$PROJECT_ROOT/apps/data/logs/dependency-report-$(date +%Y%m%d-%H%M%S).json"
    
    info_message "Generating dependency report..."
    
    {
        echo "{"
        echo "  \"timestamp\": \"$(date -u +%Y-%m-%dT%H:%M:%SZ)\","
        echo "  \"python\": {"
        
        cd "$BACKEND_DIR"
        echo "    \"installed\": ["
        uv pip list --format=json 2>/dev/null | jq -c '.[]' | sed 's/^/      /' | sed '$!s/$/,/'
        echo "    ],"
        
        echo "    \"outdated\": ["
        uv pip list --outdated --format=json 2>/dev/null | jq -c '.[]' | sed 's/^/      /' | sed '$!s/$/,/' || echo ""
        echo "    ]"
        echo "  },"
        
        cd "$FRONTEND_DIR"
        echo "  \"javascript\": {"
        echo "    \"installed\": ["
        if [[ -f package.json ]]; then
            jq -r '.dependencies // {} | to_entries[] | {name: .key, version: .value}' package.json | sed 's/^/      /' | sed '$!s/$/,/'
        fi
        echo "    ]"
        echo "  }"
        echo "}"
    } > "$report_file"
    
    success_message "Dependency report generated: $report_file"
}

# Update dependencies (optional)
update_dependencies() {
    if [[ "${1:-}" == "--update" ]]; then
        warning_message "Updating dependencies (use with caution)..."
        
        # Update Python dependencies
        cd "$BACKEND_DIR"
        info_message "Updating Python dependencies..."
        uv sync --upgrade
        
        # Update JavaScript dependencies
        cd "$FRONTEND_DIR"
        info_message "Updating JavaScript dependencies..."
        bun update
        
        success_message "Dependencies updated. Please test thoroughly!"
    fi
}

# Print summary
print_summary() {
    info_message "Dependency check completed!"
    echo ""
    info_message "Summary:"
    echo "  - Check the log file for details: $LOG_FILE"
    echo "  - Review any security warnings above"
    echo "  - Consider updating outdated packages"
    echo "  - Run tests after any updates"
    echo ""
    echo "To update dependencies automatically (use with caution):"
    echo "  $0 --update"
    echo ""
    echo "To run individual security scans:"
    echo "  Backend: cd apps/backend && uv run safety check"
    echo "  Frontend: cd apps/frontend && npm audit"
}

# Main execution
main() {
    check_python_deps
    check_js_deps
    check_docker_images
    generate_report
    update_dependencies "$@"
    print_summary
    
    success_message "Dependency security check completed! 🔒"
}

# Run main function
main "$@"