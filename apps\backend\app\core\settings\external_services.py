"""
External services configuration settings for the LONI backend application.
"""

from pydantic import Field
from pydantic_settings import BaseSettings


class ExternalServicesSettings(BaseSettings):
    """External services configuration settings."""
    
    # Qdrant settings
    QDRANT_HOST: str = Field(env="QDRANT_HOST", default="localhost")
    QDRANT_PORT: int = Field(env="QDRANT_PORT", default=6333)
    QDRANT_GRPC_PORT: int = Field(env="QDRANT_GRPC_PORT", default=6334)
    QDRANT_TIMEOUT: int = Field(env="QDRANT_TIMEOUT", default=30)
    
    # Redis settings
    REDIS_HOST: str = Field(env="REDIS_HOST", default="localhost")
    REDIS_PORT: int = Field(env="REDIS_PORT", default=6379)
    REDIS_DB: int = Field(env="REDIS_DB", default=0)
    REDIS_PASSWORD: str = Field(env="REDIS_PASSWORD", default="")
    REDIS_TIMEOUT: int = Field(env="REDIS_TIMEOUT", default=30)
    
    # AI/ML settings
    EMBEDDING_MODEL: str = Field(env="EMBEDDING_MODEL", default="all-MiniLM-L6-v2")
    EMBEDDING_DIMENSION: int = Field(env="EMBEDDING_DIMENSION", default=384)
    
    # Celery settings
    CELERY_BROKER_URL: str = Field(env="CELERY_BROKER_URL", default="")
    CELERY_RESULT_BACKEND: str = Field(env="CELERY_RESULT_BACKEND", default="")
    
    @property
    def qdrant_url(self) -> str:
        """Construct Qdrant URL."""
        return f"http://{self.QDRANT_HOST}:{self.QDRANT_PORT}"
    
    @property
    def redis_url(self) -> str:
        """Construct Redis URL from components."""
        if self.REDIS_PASSWORD:
            return f"redis://:{self.REDIS_PASSWORD}@{self.REDIS_HOST}:{self.REDIS_PORT}/{self.REDIS_DB}"
        return f"redis://{self.REDIS_HOST}:{self.REDIS_PORT}/{self.REDIS_DB}"
    
    @property
    def celery_broker_url(self) -> str:
        """Get Celery broker URL, fallback to Redis URL."""
        return self.CELERY_BROKER_URL or self.redis_url
    
    @property
    def celery_result_backend(self) -> str:
        """Get Celery result backend URL, fallback to Redis URL."""
        return self.CELERY_RESULT_BACKEND or self.redis_url
    
    class Config:
        env_file = ".env"
        case_sensitive = True