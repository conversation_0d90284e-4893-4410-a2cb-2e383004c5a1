// Application constants and configuration

export const API_ENDPOINTS = {
  HEALTH: '/health',
  MODELS: {
    LIST: '/api/v1/models',
    PULL: '/api/v1/models/pull',
    SET: '/api/v1/models/set',
    USE: '/api/v1/models/use',
  },
  TRANSCRIPTION: '/api/v1/transcribe',
  PROJECT_SCANNER: {
    SCAN: '/api/v1/scanner/scan',
    STATUS: '/api/v1/scanner/status',
    RESULTS: '/api/v1/scanner/results',
    HISTORY: '/api/v1/scanner/history',
    EXPORT: '/api/v1/scanner/export',
    CANCEL: '/api/v1/scanner/cancel',
    STATS: '/api/v1/scanner/stats',
  },
} as const;

export const ROUTES = {
  HOME: '/',
  DASHBOARD: '/dashboard',
  MODELS: '/models',
  TRANSCRIBE: '/transcribe',
  DESIGN: '/design',
  PROJECT_SCANNER: '/project-scanner',
  API: '/api',
} as const;

export const ANIMATION_CONFIG = {
  DURATION: {
    FAST: 0.2,
    NORMAL: 0.3,
    SLOW: 0.5,
  },
  EASING: {
    EASE_IN_OUT: 'ease-in-out',
    EASE_OUT: 'ease-out',
    EASE_IN: 'ease-in',
    SPRING: 'spring',
  },
} as const;

export const THEME_CONFIG = {
  STORAGE_KEY: 'loni-theme',
  DEFAULT_THEME: 'system' as const,
  THEMES: ['light', 'dark', 'system'] as const,
};

export const UI_CONFIG = {
  TOAST_DURATION: 5000,
  DEBOUNCE_DELAY: 300,
  PAGINATION_LIMIT: 20,
  MAX_FILE_SIZE: 10 * 1024 * 1024, // 10MB
};

export const MODEL_PROVIDERS = {
  OLLAMA: 'OLLAMA',
  OPENAI: 'OPENAI',
  ANTHROPIC: 'ANTHROPIC',
  LOCAL: 'LOCAL',
} as const;

export const SUPPORTED_AUDIO_FORMATS = [
  'mp3',
  'wav',
  'flac',
  'm4a',
  'ogg',
  'webm',
] as const;

export const ERROR_MESSAGES = {
  NETWORK_ERROR: 'Network connection failed. Please check your internet connection.',
  SERVER_ERROR: 'Server error occurred. Please try again later.',
  VALIDATION_ERROR: 'Please check your input and try again.',
  UNAUTHORIZED: 'You are not authorized to perform this action.',
  NOT_FOUND: 'The requested resource was not found.',
  RATE_LIMITED: 'Too many requests. Please wait a moment and try again.',
} as const;

export const SUCCESS_MESSAGES = {
  MODEL_PULLED: 'Model pulled successfully',
  MODEL_SET: 'Active model updated successfully',
  TRANSCRIPTION_COMPLETED: 'Audio transcription completed',
  CONFIG_SAVED: 'Configuration saved successfully',
} as const;

export const FEATURE_FLAGS = {
  COPILOT_ENABLED: process.env.NEXT_PUBLIC_COPILOT_ENABLED === 'true',
  ANALYTICS_ENABLED: process.env.NEXT_PUBLIC_ANALYTICS_ENABLED === 'true',
  DEBUG_MODE: process.env.NODE_ENV === 'development',
} as const;

export const BREAKPOINTS = {
  SM: 640,
  MD: 768,
  LG: 1024,
  XL: 1280,
  '2XL': 1536,
} as const;
