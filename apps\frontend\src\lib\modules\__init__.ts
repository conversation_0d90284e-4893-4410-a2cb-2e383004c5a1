/**
 * Frontend Module System - React component nodes for n8n-like architecture.
 *
 * This module provides a modular, component-based architecture where each UI
 * component is a self-contained "node" with clear interfaces and composability.
 */

// Core types for the modular architecture
export enum ModuleType {
  UI_COMPONENT = 'ui_component',
  DATA_PROVIDER = 'data_provider',
  STATE_MANAGER = 'state_manager',
  API_CLIENT = 'api_client',
  UTILITY = 'utility'
}

export enum ModuleStatus {
  INITIALIZING = 'initializing',
  READY = 'ready',
  RUNNING = 'running',
  ERROR = 'error',
  DESTROYED = 'destroyed'
}

export interface ModuleConfig<T = any> {
  name: string;
  type: ModuleType;
  enabled: boolean;
  priority: number;
  dependencies: string[];
  settings: T;
}

export interface ModuleInput<T = any> {
  data: T;
  metadata?: Record<string, any>;
  context?: Record<string, any>;
}

export interface ModuleOutput<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  metadata?: Record<string, any>;
}

export interface ModuleInterface<TInput = any, TOutput = any> {
  name: string;
  type: ModuleType;
  status: ModuleStatus;
  config: ModuleConfig;

  initialize(config: ModuleConfig): Promise<boolean>;
  execute(input: ModuleInput<TInput>): Promise<ModuleOutput<TOutput>>;
  validate(input: ModuleInput<TInput>): boolean;
  destroy(): Promise<void>;
  getDependencies(): string[];
}

// Base class for React component modules
export abstract class BaseModule<TProps = any, TInput = any, TOutput = any>
  implements ModuleInterface<TInput, TOutput>
{
  public name: string;
  public type: ModuleType;
  public status: ModuleStatus;
  public config: ModuleConfig;

  protected dependencies: Map<string, BaseModule> = new Map();

  constructor(name: string, type: ModuleType) {
    this.name = name;
    this.type = type;
    this.status = ModuleStatus.INITIALIZING;
  }

  async initialize(config: ModuleConfig): Promise<boolean> {
    this.config = config;
    this.status = ModuleStatus.READY;
    return true;
  }

  abstract execute(input: ModuleInput<TInput>): Promise<ModuleOutput<TOutput>>;

  validate(input: ModuleInput<TInput>): boolean {
    return true;
  }

  async destroy(): Promise<void> {
    this.status = ModuleStatus.DESTROYED;
    this.dependencies.clear();
  }

  getDependencies(): string[] {
    return this.config?.dependencies || [];
  }

  setDependency(name: string, module: BaseModule): void {
    this.dependencies.set(name, module);
  }

  getDependency<T extends BaseModule>(name: string): T {
    const module = this.dependencies.get(name);
    if (!module) {
      throw new Error(`Dependency '${name}' not found`);
    }
    return module as T;
  }
}

// Module registry for managing all modules
export class ModuleRegistry {
  private modules: Map<string, BaseModule> = new Map();
  private configs: Map<string, ModuleConfig> = new Map();

  register(module: BaseModule): void {
    this.modules.set(module.name, module);
  }

  unregister(name: string): void {
    this.modules.delete(name);
  }

  get<T extends BaseModule>(name: string): T | undefined {
    return this.modules.get(name) as T;
  }

  listByType(type: ModuleType): BaseModule[] {
    return Array.from(this.modules.values()).filter(module => module.type === type);
  }

  listAll(): BaseModule[] {
    return Array.from(this.modules.values());
  }

  setConfig(name: string, config: ModuleConfig): void {
    this.configs.set(name, config);
  }

  getConfig(name: string): ModuleConfig | undefined {
    return this.configs.get(name);
  }

  async initializeAll(): Promise<Record<string, boolean>> {
    const results: Record<string, boolean> = {};

    // Sort by priority for proper initialization order
    const sortedModules = Array.from(this.modules.entries()).sort(
      ([, a], [, b]) => (this.configs.get(a.name)?.priority || 100) - (this.configs.get(b.name)?.priority || 100)
    );

    for (const [name, module] of sortedModules) {
      const config = this.configs.get(name);
      if (config) {
        results[name] = await module.initialize(config);
      } else {
        results[name] = false;
      }
    }

    return results;
  }

  async destroyAll(): Promise<void> {
    for (const module of this.modules.values()) {
      await module.destroy();
    }
    this.modules.clear();
    this.configs.clear();
  }
}

// Global module registry instance
export const registry = new ModuleRegistry();

// Module factory for creating module instances
export class ModuleFactory {
  static createModule<T extends BaseModule>(
    type: ModuleType,
    name: string,
    ModuleClass: new (name: string) => T
  ): T {
    return new ModuleClass(name);
  }

  static createConfig<T = any>(
    name: string,
    type: ModuleType,
    settings: T,
    options?: Partial<Pick<ModuleConfig<T>, 'enabled' | 'priority' | 'dependencies'>>
  ): ModuleConfig<T> {
    return {
      name,
      type,
      enabled: options?.enabled ?? true,
      priority: options?.priority ?? 100,
      dependencies: options?.dependencies ?? [],
      settings
    };
  }
}

// React hook for using modules in components
export function useModule<T extends BaseModule>(name: string): T | undefined {
  return registry.get<T>(name);
}

// Hook for module status and data
export function useModuleState<T extends BaseModule>(name: string) {
  const module = useModule<T>(name);

  return {
    module,
    status: module?.status,
    config: module?.config,
    isReady: module?.status === ModuleStatus.READY,
    isRunning: module?.status === ModuleStatus.RUNNING,
    hasError: module?.status === ModuleStatus.ERROR
  };
}

// Hook for executing module operations
export function useModuleExecutor<T extends BaseModule>(
  name: string,
  operation: string
) {
  const module = useModule<T>(name);

  const execute = async (input: ModuleInput): Promise<ModuleOutput> => {
    if (!module) {
      throw new Error(`Module '${name}' not found`);
    }

    if (module.status !== ModuleStatus.READY) {
      throw new Error(`Module '${name}' is not ready`);
    }

    return await module.execute(input);
  };

  return { execute, module };
}
