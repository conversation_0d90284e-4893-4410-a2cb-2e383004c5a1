
# Build artifacts and cache
**/__pycache__/
*.py[cod]
*$py.class
*.so
.Python
*.egg-info/
dist/
build/
.coverage
coverage.xml
htmlcov/
.tox/
.pytest_cache/
.mypy_cache/
.ruff_cache/

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.npm
.next/
*.tsbuildinfo

# Environment files
.env
.env.local
.env.*.local

# Dependencies and locks
package-lock.json
yarn.lock

# AI tool artifacts (to prevent future pollution)
.claude/
.hive-mind/
.roo/
.swarm/
.mcp.json
claude-flow*
hive-mind-prompt-*.txt
coordination/
memory/claude-flow-data.json
*.db
*.db-journal
*.db-wal
*.sqlite
*.sqlite-journal
*.sqlite-wal
