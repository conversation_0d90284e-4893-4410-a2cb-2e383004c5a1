"""
Performance Detector for the Project Scanner system.

This module detects performance issues including large files, inefficient algorithms,
memory leaks, and database performance problems.
"""

import ast
import re
from pathlib import Path
from typing import Any, Dict, List, Optional, Set, Tuple

from ..base_detector import BaseDetector
from ..models import (
    DetectorResult,
    Issue,
    IssueSeverity,
    IssueType,
    RemediationSuggestion
)


class PerformanceDetector(BaseDetector):
    """
    Detects performance issues including:
    - Large files that impact load times
    - Inefficient algorithms and patterns
    - Potential memory leaks
    - Database performance issues
    - Resource-intensive operations
    """
    
    def __init__(self, config):
        super().__init__("Performance", config)
    
    def get_supported_file_types(self) -> Set[str]:
        """Get supported file extensions."""
        return {'.py', '.js', '.ts', '.jsx', '.tsx', '.sql', '.json', '.yaml', '.yml'}
    
    def detect(self, project_path: Path) -> DetectorResult:
        """Detect performance issues."""
        self.start_detection()
        
        try:
            files = self.get_project_files(project_path)
            
            for file_path in files:
                content = self.read_file_safely(file_path)
                if content:
                    self._analyze_file(file_path, content, project_path)
                    self.files_processed += 1
            
            return self.finish_detection()
            
        except Exception as e:
            return self.finish_detection(success=False, error_message=str(e))
    
    def _analyze_file(self, file_path: Path, content: str, project_path: Path) -> None:
        """Analyze a single file for performance issues."""
        relative_path = str(file_path.relative_to(project_path))
        
        # Check file size first
        self._check_large_files(file_path, relative_path)
        
        # File type specific analysis
        if file_path.suffix == '.py':
            self._analyze_python_performance(relative_path, content)
        elif file_path.suffix in {'.js', '.ts', '.jsx', '.tsx'}:
            self._analyze_javascript_performance(relative_path, content)
        elif file_path.suffix == '.sql':
            self._analyze_sql_performance(relative_path, content)
        elif file_path.suffix in {'.json', '.yaml', '.yml'}:
            self._analyze_config_performance(relative_path, content, file_path.suffix)
    
    def _check_large_files(self, file_path: Path, relative_path: str) -> None:
        """Check for files that are too large."""
        try:
            file_size = file_path.stat().st_size
            
            # Define thresholds based on file type
            size_thresholds = {
                '.py': 50 * 1024,      # 50KB for Python files
                '.js': 100 * 1024,     # 100KB for JavaScript
                '.ts': 100 * 1024,     # 100KB for TypeScript
                '.json': 10 * 1024,    # 10KB for JSON
                '.sql': 20 * 1024,     # 20KB for SQL
                'default': 100 * 1024   # 100KB default
            }
            
            threshold = size_thresholds.get(file_path.suffix, size_thresholds['default'])
            
            if file_size > threshold:
                severity = IssueSeverity.HIGH if file_size > threshold * 2 else IssueSeverity.MEDIUM
                
                self.add_issue(self.create_issue(
                    IssueType.LARGE_FILE,
                    severity,
                    f"Large file: {file_path.name} ({file_size // 1024}KB)",
                    f"File is {file_size // 1024}KB, which may impact performance and maintainability.",
                    relative_path,
                    rule_id='large_file',
                    impact_score=60 if severity == IssueSeverity.HIGH else 40,
                    likelihood_score=80,
                    remediation=RemediationSuggestion(
                        title="Consider splitting large file",
                        description="Break large files into smaller, more focused modules.",
                        effort_estimate="2-4 hours"
                    )
                ))
        
        except OSError:
            pass  # Skip if can't read file stats
    
    def _analyze_python_performance(self, file_path: str, content: str) -> None:
        """Analyze Python file for performance issues."""
        try:
            tree = ast.parse(content)
            
            # Analyze AST for performance patterns
            for node in ast.walk(tree):
                if isinstance(node, ast.For):
                    self._check_nested_loops(node, file_path)
                elif isinstance(node, ast.ListComp):
                    self._check_complex_list_comprehension(node, file_path)
                elif isinstance(node, ast.Call):
                    self._check_inefficient_function_calls(node, file_path)
        
        except SyntaxError:
            pass  # Skip files with syntax errors
        
        # Pattern-based checks
        self._check_python_performance_patterns(file_path, content)
    
    def _check_nested_loops(self, node: ast.For, file_path: str, depth: int = 0) -> None:
        """Check for deeply nested loops."""
        depth += 1
        
        for child in ast.walk(node):
            if isinstance(child, ast.For) and child != node:
                if depth >= 3:  # 3+ nested loops
                    self.add_issue(self.create_issue(
                        IssueType.INEFFICIENT_ALGORITHM,
                        IssueSeverity.MEDIUM,
                        f"Deeply nested loops ({depth} levels)",
                        f"Found {depth} levels of nested loops, which can have O(n^{depth}) complexity.",
                        file_path,
                        line_number=getattr(node, 'lineno', None),
                        rule_id='deeply_nested_loops',
                        impact_score=60,
                        likelihood_score=70,
                        remediation=RemediationSuggestion(
                            title="Optimize nested loops",
                            description="Consider using more efficient algorithms, caching, or breaking loops early.",
                            effort_estimate="1-2 hours"
                        )
                    ))
                    return
                
                self._check_nested_loops(child, file_path, depth)
    
    def _check_complex_list_comprehension(self, node: ast.ListComp, file_path: str) -> None:
        """Check for overly complex list comprehensions."""
        # Count generators and conditions
        generator_count = len(node.generators)
        condition_count = sum(len(gen.ifs) for gen in node.generators)
        
        if generator_count > 2 or condition_count > 2:
            self.add_issue(self.create_issue(
                IssueType.INEFFICIENT_ALGORITHM,
                IssueSeverity.LOW,
                "Complex list comprehension",
                f"List comprehension with {generator_count} generators and {condition_count} conditions may be hard to read and inefficient.",
                file_path,
                line_number=getattr(node, 'lineno', None),
                rule_id='complex_list_comprehension',
                impact_score=30,
                likelihood_score=60,
                remediation=RemediationSuggestion(
                    title="Simplify list comprehension",
                    description="Consider breaking complex comprehensions into multiple steps or using regular loops.",
                    effort_estimate="20 minutes"
                )
            ))
    
    def _check_inefficient_function_calls(self, node: ast.Call, file_path: str) -> None:
        """Check for inefficient function calls."""
        if isinstance(node.func, ast.Name):
            func_name = node.func.id
            
            # Check for inefficient patterns
            inefficient_patterns = {
                'len': 'Using len() in loops or conditions can be inefficient',
                'type': 'Using type() instead of isinstance() is less efficient',
            }
            
            if func_name in inefficient_patterns:
                # Additional context checks would go here
                pass
        
        elif isinstance(node.func, ast.Attribute):
            if isinstance(node.func.value, ast.Name):
                obj_name = node.func.value.id
                method_name = node.func.attr
                
                # Check for inefficient string operations
                if obj_name == 'str' or method_name in ['join', 'replace', 'split']:
                    if self._is_in_loop(node):
                        self.add_issue(self.create_issue(
                            IssueType.INEFFICIENT_ALGORITHM,
                            IssueSeverity.LOW,
                            f"String operation in loop: {method_name}",
                            f"String method '{method_name}' in loop can be inefficient for large datasets.",
                            file_path,
                            line_number=getattr(node, 'lineno', None),
                            rule_id=f'string_operation_in_loop_{method_name}',
                            impact_score=25,
                            likelihood_score=50
                        ))
    
    def _is_in_loop(self, node: ast.AST) -> bool:
        """Check if a node is inside a loop (simplified check)."""
        # This is a simplified implementation
        # In practice, you'd walk up the AST tree
        return False  # Placeholder
    
    def _check_python_performance_patterns(self, file_path: str, content: str) -> None:
        """Check for performance anti-patterns using regex."""
        lines = content.splitlines()
        
        performance_patterns = {
            r'\.append\s*\(.*\)\s*for.*in': {
                'severity': IssueSeverity.LOW,
                'title': 'List append in loop',
                'description': 'Using append() in loops can be slow. Consider list comprehension or pre-allocating.'
            },
            r'global\s+\w+': {
                'severity': IssueSeverity.LOW,
                'title': 'Global variable usage',
                'description': 'Global variables can impact performance and make code harder to test.'
            },
            r'import\s+.*\*': {
                'severity': IssueSeverity.LOW,
                'title': 'Wildcard import',
                'description': 'Wildcard imports can slow startup time and pollute namespace.'
            },
            r'time\.sleep\s*\(\s*[0-9]+\s*\)': {
                'severity': IssueSeverity.MEDIUM,
                'title': 'Long sleep in code',
                'description': 'Long sleep statements can significantly impact performance.'
            }
        }
        
        for line_num, line in enumerate(lines, 1):
            for pattern, config in performance_patterns.items():
                if re.search(pattern, line):
                    self.add_issue(self.create_issue(
                        IssueType.INEFFICIENT_ALGORITHM,
                        config['severity'],
                        config['title'],
                        config['description'],
                        file_path,
                        line_number=line_num,
                        rule_id=pattern.replace('\\', '').replace(' ', '_')[:20],
                        impact_score=40 if config['severity'] == IssueSeverity.MEDIUM else 20,
                        likelihood_score=60
                    ))
    
    def _analyze_javascript_performance(self, file_path: str, content: str) -> None:
        """Analyze JavaScript/TypeScript file for performance issues."""
        lines = content.splitlines()
        
        js_performance_patterns = {
            r'document\.getElementById\s*\(': {
                'severity': IssueSeverity.LOW,
                'title': 'Repeated DOM queries',
                'description': 'Repeated DOM queries can be slow. Consider caching elements.'
            },
            r'for\s*\(\s*var\s+\w+\s*=\s*0\s*;.*\.length\s*;': {
                'severity': IssueSeverity.LOW,
                'title': 'Array length in loop condition',
                'description': 'Accessing array.length in loop condition can be optimized by caching the length.'
            },
            r'eval\s*\(': {
                'severity': IssueSeverity.HIGH,
                'title': 'Use of eval()',
                'description': 'eval() is slow and poses security risks.'
            },
            r'innerHTML\s*\+\s*=': {
                'severity': IssueSeverity.MEDIUM,
                'title': 'Inefficient DOM manipulation',
                'description': 'Using innerHTML += forces browser to reparse entire content.'
            }
        }
        
        for line_num, line in enumerate(lines, 1):
            for pattern, config in js_performance_patterns.items():
                if re.search(pattern, line):
                    self.add_issue(self.create_issue(
                        IssueType.INEFFICIENT_ALGORITHM,
                        config['severity'],
                        config['title'],
                        config['description'],
                        file_path,
                        line_number=line_num,
                        rule_id=f"js_{pattern[:20].replace(' ', '_')}",
                        impact_score=70 if config['severity'] == IssueSeverity.HIGH else 40,
                        likelihood_score=60
                    ))
    
    def _analyze_sql_performance(self, file_path: str, content: str) -> None:
        """Analyze SQL file for performance issues."""
        content_upper = content.upper()
        lines = content.splitlines()
        
        sql_performance_patterns = {
            r'SELECT\s+\*\s+FROM': {
                'severity': IssueSeverity.MEDIUM,
                'title': 'SELECT * query',
                'description': 'SELECT * can be inefficient. Specify only needed columns.'
            },
            r'WHERE.*LIKE\s+["\']%.*%["\']': {
                'severity': IssueSeverity.HIGH,
                'title': 'Leading wildcard in LIKE',
                'description': 'Leading wildcards in LIKE queries prevent index usage.'
            },
            r'WHERE.*OR.*OR': {
                'severity': IssueSeverity.MEDIUM,
                'title': 'Multiple OR conditions',
                'description': 'Multiple OR conditions can be slow. Consider using IN or UNION.'
            },
            r'ORDER\s+BY.*RAND\(\)': {
                'severity': IssueSeverity.HIGH,
                'title': 'ORDER BY RAND()',
                'description': 'ORDER BY RAND() is extremely slow on large tables.'
            }
        }
        
        for line_num, line in enumerate(lines, 1):
            line_upper = line.upper()
            for pattern, config in sql_performance_patterns.items():
                if re.search(pattern, line_upper):
                    self.add_issue(self.create_issue(
                        IssueType.DATABASE_PERFORMANCE,
                        config['severity'],
                        config['title'],
                        config['description'],
                        file_path,
                        line_number=line_num,
                        rule_id=f"sql_{pattern[:20].replace(' ', '_')}",
                        impact_score=80 if config['severity'] == IssueSeverity.HIGH else 50,
                        likelihood_score=70
                    ))
    
    def _analyze_config_performance(self, file_path: str, content: str, file_ext: str) -> None:
        """Analyze configuration files for performance issues."""
        # Check for large configuration files
        line_count = len(content.splitlines())
        
        if line_count > 1000:
            self.add_issue(self.create_issue(
                IssueType.LARGE_FILE,
                IssueSeverity.MEDIUM,
                f"Large configuration file ({line_count} lines)",
                f"Configuration file has {line_count} lines, which may slow application startup.",
                file_path,
                rule_id='large_config_file',
                impact_score=40,
                likelihood_score=70,
                remediation=RemediationSuggestion(
                    title="Split configuration file",
                    description="Consider splitting large config files into smaller, more focused files.",
                    effort_estimate="1 hour"
                )
            ))
        
        # Check for duplicated values
        if file_ext == '.json':
            self._check_json_performance(file_path, content)
    
    def _check_json_performance(self, file_path: str, content: str) -> None:
        """Check JSON files for performance issues."""
        try:
            import json
            data = json.loads(content)
            
            # Check for deeply nested objects
            max_depth = self._calculate_json_depth(data)
            if max_depth > 10:
                self.add_issue(self.create_issue(
                    IssueType.INEFFICIENT_ALGORITHM,
                    IssueSeverity.LOW,
                    f"Deeply nested JSON ({max_depth} levels)",
                    f"JSON structure is {max_depth} levels deep, which can impact parsing performance.",
                    file_path,
                    rule_id='deep_json_nesting',
                    impact_score=25,
                    likelihood_score=50
                ))
            
            # Check for large arrays
            self._check_large_json_arrays(data, file_path)
            
        except json.JSONDecodeError:
            pass  # Invalid JSON, skip analysis
    
    def _calculate_json_depth(self, obj, current_depth=0) -> int:
        """Calculate the maximum depth of a JSON object."""
        if not isinstance(obj, (dict, list)):
            return current_depth
        
        if isinstance(obj, dict):
            if not obj:
                return current_depth
            return max(self._calculate_json_depth(value, current_depth + 1) for value in obj.values())
        
        if isinstance(obj, list):
            if not obj:
                return current_depth
            return max(self._calculate_json_depth(item, current_depth + 1) for item in obj)
        
        return current_depth
    
    def _check_large_json_arrays(self, obj, file_path: str, path: str = '') -> None:
        """Check for large arrays in JSON that might impact performance."""
        if isinstance(obj, list):
            if len(obj) > 1000:
                self.add_issue(self.create_issue(
                    IssueType.INEFFICIENT_ALGORITHM,
                    IssueSeverity.MEDIUM,
                    f"Large JSON array ({len(obj)} items)",
                    f"JSON array at '{path}' has {len(obj)} items, which may impact parsing performance.",
                    file_path,
                    rule_id='large_json_array',
                    impact_score=50,
                    likelihood_score=60,
                    remediation=RemediationSuggestion(
                        title="Consider pagination or chunking",
                        description="For large datasets, consider implementing pagination or breaking into smaller chunks.",
                        effort_estimate="1-2 hours"
                    )
                ))
        
        elif isinstance(obj, dict):
            for key, value in obj.items():
                new_path = f"{path}.{key}" if path else key
                self._check_large_json_arrays(value, file_path, new_path)