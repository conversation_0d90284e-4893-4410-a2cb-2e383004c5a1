#!/bin/bash
# Script to implement optimized Docker configurations

# Set variables
PROJECT_ROOT="/mnt/e/Projects/lonors/loni"
INFRA_DIR="$PROJECT_ROOT/apps/infra"
BACKEND_DIR="$PROJECT_ROOT/apps/backend"
FRONTEND_DIR="$PROJECT_ROOT/apps/frontend"
LOG_FILE="$PROJECT_ROOT/apps/data/logs/devtools-docker-optimization.log"

# Create log file if it doesn't exist
mkdir -p "$PROJECT_ROOT/apps/data/logs"
touch "$LOG_FILE"

# Function to log messages
log_message() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

# Start optimization process
log_message "Starting Docker configuration optimization"

# Change to infra directory
cd "$INFRA_DIR" || { log_message "ERROR: Failed to change to infra directory"; exit 1; }

# Backup original files
log_message "Backing up original Docker configuration files"
cp docker-compose.yml docker-compose.yml.backup
cp ../backend/Dockerfile ../backend/Dockerfile.backup
cp ../frontend/Dockerfile ../frontend/Dockerfile.backup

# Copy optimized files
log_message "Implementing optimized Docker configuration files"
cp docker-compose.optimized.yml docker-compose.yml
cp ../backend/Dockerfile.optimized ../backend/Dockerfile
cp ../frontend/Dockerfile.optimized ../frontend/Dockerfile

log_message "Docker configuration optimization completed successfully"