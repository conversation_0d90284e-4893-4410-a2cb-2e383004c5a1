# Hadolint configuration for Docker best practices
# Documentation: https://github.com/hadolint/hadolint

ignored:
  - DL3008  # Pin versions in apt-get install
  - DL3009  # Delete apt-get lists after installing
  - DL3015  # Avoid additional packages in apt-get
  - SC2039  # POSIX compliance for shell

trustedRegistries:
  - docker.io
  - gcr.io
  - registry.hub.docker.com
  - python
  - oven
  - node
  - alpine

override:
  warning:
    - DL3020  # Use COPY instead of ADD
    - DL3033  # Specify version for last command
  error:
    - DL3001  # Avoid sudo usage
    - DL3003  # Use WORKDIR to switch to directory
    - DL3004  # Use absolute WORKDIR
    - DL3007  # Use :latest tag
    - DL3025  # Use JSON format for CMD and ENTRYPOINT
    - DL4006  # Set SHELL option -o pipefail

format: json