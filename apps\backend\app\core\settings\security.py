"""
Security configuration settings for the LONI backend application.
"""

from typing import List

from pydantic import Field, validator
from pydantic_settings import BaseSettings


class SecuritySettings(BaseSettings):
    """Security configuration settings."""
    
    SECRET_KEY: str = Field(env="SECRET_KEY", default="dev-secret-key-change-in-production-minimum-32-chars")
    ACCESS_TOKEN_EXPIRE_MINUTES: int = Field(env="ACCESS_TOKEN_EXPIRE_MINUTES", default=60 * 24 * 8)  # 8 days
    
    # JWT settings
    ALGORITHM: str = Field(env="JWT_ALGORITHM", default="HS256")
    
    # Password settings
    PWD_CONTEXT_SCHEMES: List[str] = Field(default=["bcrypt"])
    PWD_CONTEXT_DEPRECATED: str = Field(default="auto")
    
    @validator("SECRET_KEY")
    def validate_secret_key(cls, v):
        """Validate that SECRET_KEY is provided and secure."""
        if not v:
            raise ValueError("SECRET_KEY must be provided")
        if len(v) < 32:
            raise ValueError("SECRET_KEY must be at least 32 characters long")
        if v == "your-secret-key-change-in-production":
            raise ValueError("SECRET_KEY must be changed from default value")
        return v
    
    class Config:
        env_file = ".env"
        case_sensitive = True