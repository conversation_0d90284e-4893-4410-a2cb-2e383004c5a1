"""FastAPI application configuration and lifecycle management."""
from contextlib import asynccontextmanager
from typing import Optional
from fastapi import <PERSON><PERSON><PERSON>, HTTPException
from fastapi.responses import JSONResponse
from fastapi.middleware.base import BaseHTTPMiddleware
from fastapi.middleware.cors import CORSMiddleware

from app.api_models import HealthResponse
from app.config import settings
from app.logger import get_logger, LoggerContext
from app.middleware.logging_middleware import (
    correlation_id_middleware,
    performance_logging_middleware,
    security_logging_middleware
)
from app.routes import (
    health_check,
    root,
    list_models,
    pull_model,
    set_model,
    use_model,
    transcribe_audio
)
from app.scanner.api import router as scanner_router

logger = get_logger("app")


def create_app() -> FastAPI:
    """Create and configure the FastAPI application."""
    app = FastAPI(
        title=settings.app_name,
        version=settings.app_version,
        description="Backend API for LONI project - AI-powered audio transcription and model management",
        debug=settings.debug,
        docs_url="/docs",
        redoc_url="/redoc",
        openapi_url="/openapi.json",
        lifespan=lifespan,
        contact={
            "name": "LONI Team",
            "description": "Professional AI-powered platform for audio transcription and Ollama model management"
        },
        license_info={
            "name": "MIT License",
            "url": "https://opensource.org/licenses/MIT"
        }
    )

    # Add CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.cors_origins or ["http://localhost:3000"],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

    # Add logging middleware
    app.middleware("http")(correlation_id_middleware)
    app.middleware("http")(performance_logging_middleware)
    app.middleware("http")(security_logging_middleware)

    # Register routes
    _register_routes(app)

    # Include scanner router
    app.include_router(scanner_router)

    # Register exception handlers
    _register_exception_handlers(app)

    return app


def _register_routes(app: FastAPI) -> None:
    """Register API routes."""
    @app.get("/health", response_model=HealthResponse)
    async def _health_check():
        return await health_check()

    @app.get("/")
    async def _root():
        return await root()

    @app.get("/api/v1/models", response_model=list)
    async def _list_models():
        return await list_models()

    @app.post("/api/v1/models/pull")
    async def _pull_model(model_name: str):
        return await pull_model(model_name)

    @app.post("/api/v1/models/set")
    async def _set_model(model_name: str):
        return await set_model(model_name)

    @app.post("/api/v1/models/use")
    async def _use_model(model_name: str, input_data: str):
        return await use_model(model_name, input_data)

    @app.post("/api/v1/transcribe", response_model=dict)
    async def _transcribe_audio(request=dict):
        return await transcribe_audio(request)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan events with structured logging."""
    # Startup
    logger.info(
        "Application startup",
        app_name=settings.app_name,
        app_version=settings.app_version,
        environment=settings.environment,
        debug=settings.debug,
        ollama_host=settings.ollama_host,
        ollama_model=settings.ollama_model,
        whisper_model=settings.whisper_model,
        api_host=settings.api_host,
        api_port=settings.api_port
    )

    # Log available models on startup (optional)
    try:
        from app.routes import get_model_manager
        available_models = await get_model_manager().list_models()
        logger.info(
            "Available Ollama models",
            model_count=len(available_models),
            models=available_models
        )
    except Exception as e:
        logger.warning(
            "Could not list available models on startup",
            error=str(e)
        )
    
    yield
    
    # Shutdown
    logger.info(
        "Application shutdown",
        app_name=settings.app_name,
        app_version=settings.app_version
    )


def _register_exception_handlers(app: FastAPI) -> None:
    """Register global exception handlers."""

    @app.exception_handler(HTTPException)
    async def http_exception_handler(request, exc: HTTPException):
        """Handle HTTP exceptions with proper error responses."""
        logger.warning(
            "HTTP exception",
            status_code=exc.status_code,
            detail=exc.detail,
            url=str(request.url),
            method=request.method
        )

        return JSONResponse(
            status_code=exc.status_code,
            content={
                "message": exc.detail,
                "error_id": f"http_{exc.status_code}",
                "status_code": exc.status_code
            }
        )

    @app.exception_handler(ValueError)
    async def value_error_handler(request, exc: ValueError):
        """Handle validation errors."""
        logger.warning(
            "Validation error",
            error=str(exc),
            url=str(request.url),
            method=request.method
        )

        return JSONResponse(
            status_code=400,
            content={
                "message": f"Validation error: {str(exc)}",
                "error_id": "validation_error"
            }
        )

    @app.exception_handler(FileNotFoundError)
    async def file_not_found_handler(request, exc: FileNotFoundError):
        """Handle file not found errors."""
        logger.warning(
            "File not found",
            error=str(exc),
            url=str(request.url),
            method=request.method
        )

        return JSONResponse(
            status_code=404,
            content={
                "message": f"File not found: {str(exc)}",
                "error_id": "file_not_found"
            }
        )

    @app.exception_handler(PermissionError)
    async def permission_error_handler(request, exc: PermissionError):
        """Handle permission errors."""
        logger.warning(
            "Permission denied",
            error=str(exc),
            url=str(request.url),
            method=request.method
        )

        return JSONResponse(
            status_code=403,
            content={
                "message": "Permission denied",
                "error_id": "permission_denied"
            }
        )

    @app.exception_handler(Exception)
    async def general_exception_handler(request, exc):
        """Handle general exceptions with structured logging."""
        import traceback

        logger.error(
            "Unhandled exception",
            exception_type=type(exc).__name__,
            exception_message=str(exc),
            traceback=traceback.format_exc(),
            url=str(request.url),
            method=request.method,
            headers=dict(request.headers)
        )

        return JSONResponse(
            status_code=500,
            content={
                "message": "Internal server error",
                "error_id": "internal_error",
                "detail": str(exc) if settings.debug else None
            }
        )
