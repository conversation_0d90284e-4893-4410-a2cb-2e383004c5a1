'use client';

import { Card } from '@/components/common/Card';
import { Button } from '@/components/common/Button';
import { Badge } from '@/components/ui/badge';
import { 
  ChevronDown, 
  ChevronRight, 
  ExternalLink, 
  Eye, 
  FileText, 
  Code,
  AlertTriangle,
  XCircle,
  AlertCircle,
  Info,
  Shield,
  Zap,
  CheckCircle
} from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { useState } from 'react';
import type { ScanIssue, IssueSeverity, IssueCategory } from '@/lib/types';

interface IssueCardProps {
  issue: ScanIssue;
  onViewInEditor?: (file: string, line?: number) => void;
  onMarkAsFixed?: (issueId: string) => void;
  onIgnore?: (issueId: string) => void;
  className?: string;
}

const severityColors = {
  critical: 'destructive',
  high: 'destructive',
  medium: 'default',
  low: 'secondary',
  info: 'outline'
} as const;

const severityIcons = {
  critical: XCircle,
  high: AlertTriangle,
  medium: AlertCircle,
  low: Info,
  info: Info
};

const categoryIcons = {
  security: Shield,
  performance: Zap,
  maintainability: Code,
  reliability: CheckCircle,
  style: FileText,
  dependency: FileText,
  documentation: FileText
};

export function IssueCard({ 
  issue, 
  onViewInEditor, 
  onMarkAsFixed, 
  onIgnore,
  className 
}: IssueCardProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  
  const SeverityIcon = severityIcons[issue.severity];
  const CategoryIcon = categoryIcons[issue.category];
  
  const handleToggleExpanded = () => {
    setIsExpanded(!isExpanded);
  };

  const handleViewInEditor = () => {
    if (onViewInEditor) {
      onViewInEditor(issue.file, issue.line);
    }
  };

  const handleMarkAsFixed = () => {
    if (onMarkAsFixed) {
      onMarkAsFixed(issue.id);
    }
  };

  const handleIgnore = () => {
    if (onIgnore) {
      onIgnore(issue.id);
    }
  };

  return (
    <Card className={`overflow-hidden ${className}`}>
      <div 
        className="p-4 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors"
        onClick={handleToggleExpanded}
      >
        <div className="flex items-start justify-between">
          <div className="flex items-start gap-3 flex-1">
            <SeverityIcon className={`h-5 w-5 mt-0.5 ${
              issue.severity === 'critical' || issue.severity === 'high' 
                ? 'text-red-500' 
                : issue.severity === 'medium' 
                  ? 'text-yellow-500' 
                  : 'text-blue-500'
            }`} />
            
            <div className="flex-1 min-w-0">
              <div className="flex items-center gap-2 mb-1 flex-wrap">
                <h4 className="font-medium truncate">{issue.title}</h4>
                <Badge variant={severityColors[issue.severity]} className="text-xs">
                  {issue.severity}
                </Badge>
                <Badge variant="outline" className="text-xs">
                  <CategoryIcon className="h-3 w-3 mr-1" />
                  {issue.category}
                </Badge>
              </div>
              
              <p className="text-sm text-muted-foreground mb-2 line-clamp-2">
                {issue.description}
              </p>
              
              <div className="flex items-center gap-4 text-xs text-muted-foreground flex-wrap">
                <span className="flex items-center gap-1">
                  <FileText className="h-3 w-3" />
                  <span className="truncate max-w-[200px]">{issue.file}</span>
                </span>
                {issue.line && (
                  <span>Line {issue.line}</span>
                )}
                {issue.column && (
                  <span>Col {issue.column}</span>
                )}
                <span className="flex items-center gap-1">
                  <Code className="h-3 w-3" />
                  {issue.rule}
                </span>
              </div>
            </div>
          </div>
          
          <div className="flex items-center gap-2 ml-2">
            {issue.ruleUrl && (
              <Button 
                variant="ghost" 
                size="sm"
                onClick={(e) => {
                  e.stopPropagation();
                  window.open(issue.ruleUrl, '_blank');
                }}
              >
                <ExternalLink className="h-4 w-4" />
              </Button>
            )}
            
            <motion.div
              animate={{ rotate: isExpanded ? 90 : 0 }}
              transition={{ duration: 0.2 }}
            >
              <ChevronRight className="h-4 w-4 text-muted-foreground" />
            </motion.div>
          </div>
        </div>
      </div>
      
      <AnimatePresence>
        {isExpanded && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: 'auto', opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{ duration: 0.3 }}
            className="border-t bg-gray-50 dark:bg-gray-800/25"
          >
            <div className="p-4 space-y-4">
              {issue.codeSnippet && (
                <div>
                  <h5 className="text-sm font-medium mb-2 flex items-center gap-2">
                    <Code className="h-4 w-4" />
                    Code Snippet
                  </h5>
                  <div className="relative">
                    <pre className="bg-gray-900 text-gray-100 p-3 rounded-md text-sm overflow-x-auto">
                      <code>{issue.codeSnippet}</code>
                    </pre>
                    {issue.line && (
                      <div className="absolute top-2 right-2 text-xs text-gray-400 bg-gray-800 px-2 py-1 rounded">
                        Line {issue.line}
                      </div>
                    )}
                  </div>
                </div>
              )}
              
              {issue.affectedCode && (
                <div>
                  <h5 className="text-sm font-medium mb-2">Affected Code Block</h5>
                  <div className="text-xs text-muted-foreground mb-2">
                    Lines {issue.affectedCode.startLine} - {issue.affectedCode.endLine}
                  </div>
                  <pre className="bg-gray-900 text-gray-100 p-3 rounded-md text-sm overflow-x-auto">
                    <code>{issue.affectedCode.content}</code>
                  </pre>
                </div>
              )}
              
              {issue.suggestion && (
                <div>
                  <h5 className="text-sm font-medium mb-2 flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    Suggested Fix
                  </h5>
                  <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-md p-3">
                    <p className="text-sm text-green-800 dark:text-green-200">
                      {issue.suggestion}
                    </p>
                  </div>
                </div>
              )}
              
              {issue.metadata && Object.keys(issue.metadata).length > 0 && (
                <div>
                  <h5 className="text-sm font-medium mb-2">Additional Information</h5>
                  <div className="bg-gray-100 dark:bg-gray-800 rounded-md p-3">
                    <dl className="space-y-1">
                      {Object.entries(issue.metadata).map(([key, value]) => (
                        <div key={key} className="flex justify-between text-sm">
                          <dt className="text-muted-foreground">{key}:</dt>
                          <dd className="font-medium">{String(value)}</dd>
                        </div>
                      ))}
                    </dl>
                  </div>
                </div>
              )}
              
              <div className="flex justify-between items-center pt-2 border-t">
                <div className="flex gap-2">
                  {onViewInEditor && (
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={handleViewInEditor}
                    >
                      <Eye className="h-4 w-4 mr-2" />
                      View in Editor
                    </Button>
                  )}
                  
                  {onMarkAsFixed && (
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={handleMarkAsFixed}
                    >
                      <CheckCircle className="h-4 w-4 mr-2" />
                      Mark as Fixed
                    </Button>
                  )}
                </div>
                
                {onIgnore && (
                  <Button 
                    variant="ghost" 
                    size="sm"
                    onClick={handleIgnore}
                    className="text-muted-foreground hover:text-foreground"
                  >
                    Ignore
                  </Button>
                )}
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </Card>
  );
}