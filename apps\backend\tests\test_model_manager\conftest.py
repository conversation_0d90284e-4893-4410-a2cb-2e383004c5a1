"""
Test configuration and fixtures for model manager tests.
"""

import os
import tempfile
import shutil
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock
import pytest
from typing import Dict, Any, Generator

from app.model_manager.config.index import AppConfig
from app.model_manager.infra.fs.filesystem_adapter import FilesystemAdapter
from app.model_manager.infra.cache.cache_adapter import CacheA<PERSON>pter
from app.model_manager.infra.process.process_adapter import ProcessAdapter
from app.model_manager.infra.providers.whisper_provider import WhisperProviderImpl
from app.model_manager.infra.providers.ollama_provider import OllamaProviderImpl


@pytest.fixture
def temp_dir() -> Generator[str, None, None]:
    """Create a temporary directory for testing."""
    temp_path = tempfile.mkdtemp()
    yield temp_path
    shutil.rmtree(temp_path, ignore_errors=True)


@pytest.fixture
def mock_config(temp_dir: str) -> AppConfig:
    """Create a mock configuration for testing."""
    config = Mock(spec=AppConfig)
    
    # Paths
    config.paths = Mock()
    config.paths.data_root = temp_dir
    config.paths.models_root = os.path.join(temp_dir, "models")
    config.paths.cache_root = os.path.join(temp_dir, "cache")
    
    # Providers
    config.providers = Mock()
    config.providers.whisper_enabled = True
    config.providers.ollama_enabled = True
    
    # Whisper config
    config.whisper = Mock()
    config.whisper.use_cli = False
    config.whisper.executable = "whisper"
    config.whisper.version = "1.0.0"
    config.whisper.models = [
        {
            "name": "tiny",
            "size": "39 MB",
            "size_bytes": 40960000,
            "vram_required_mb": 100,
            "multilingual": True,
            "gpu_recommended": False,
            "languages": ["en", "es", "fr"],
            "notes": "Fastest model",
            "performance_tier": "fast"
        },
        {
            "name": "base",
            "size": "74 MB",
            "size_bytes": 77594624,
            "vram_required_mb": 200,
            "multilingual": True,
            "gpu_recommended": False,
            "languages": ["en", "es", "fr", "de"],
            "notes": "Good balance",
            "performance_tier": "balanced"
        },
        {
            "name": "large",
            "size": "1550 MB",
            "size_bytes": 1625292800,
            "vram_required_mb": 2000,
            "multilingual": True,
            "gpu_recommended": True,
            "languages": ["en", "es", "fr", "de", "it", "pt", "ru", "ja", "zh"],
            "notes": "Best quality",
            "performance_tier": "quality"
        }
    ]
    
    # Ollama config
    config.ollama = Mock()
    config.ollama.executable = "ollama"
    config.ollama.catalog_source = "web"
    config.ollama.catalog_cache_key = "ollama_catalog"
    
    # Timeouts
    config.timeouts = Mock()
    config.timeouts.install_lock_ttl_sec = 300
    config.timeouts.process_timeout_sec = 600
    config.timeouts.cache_ttl_sec = 3600
    
    return config


@pytest.fixture
def mock_filesystem(temp_dir: str, mock_config: AppConfig) -> Mock:
    """Create a mock filesystem adapter."""
    fs = Mock(spec=FilesystemAdapter)
    
    # Setup basic filesystem operations
    fs.exists.return_value = True
    fs.ensure_dir.return_value = None
    fs.size_on_disk.return_value = 1024000
    fs.join_data_path.side_effect = lambda *args: os.path.join(temp_dir, *args)
    fs.write_json_atomic.return_value = None
    fs.read_json.return_value = {}
    fs.remove_dir_safe.return_value = None
    fs.acquire_lock.return_value = True
    fs.release_lock.return_value = None
    
    return fs


@pytest.fixture
def mock_cache() -> Mock:
    """Create a mock cache adapter."""
    cache = Mock(spec=CacheAdapter)
    cache.get.return_value = None
    cache.set.return_value = None
    cache.stats.return_value = {
        "hit_count": 10,
        "miss_count": 5,
        "total_size": 1024
    }
    return cache


@pytest.fixture
def mock_process() -> Mock:
    """Create a mock process adapter."""
    process = Mock(spec=ProcessAdapter)
    
    # Mock successful command execution
    result = Mock()
    result.returncode = 0
    result.exit_code = 0
    result.stdout = "Success"
    result.stderr = ""
    
    process.run.return_value = result
    process.which.return_value = "/usr/bin/whisper"
    
    return process


@pytest.fixture
def whisper_provider(mock_config: AppConfig, mock_filesystem: Mock, mock_process: Mock, mock_cache: Mock) -> WhisperProviderImpl:
    """Create a Whisper provider instance for testing."""
    return WhisperProviderImpl(mock_config, mock_filesystem, mock_process, mock_cache)


@pytest.fixture
def ollama_provider(mock_config: AppConfig, mock_filesystem: Mock, mock_process: Mock, mock_cache: Mock) -> OllamaProviderImpl:
    """Create an Ollama provider instance for testing."""
    return OllamaProviderImpl(mock_config, mock_filesystem, mock_process, mock_cache)


@pytest.fixture
def mock_torch():
    """Mock PyTorch for testing device detection."""
    with patch('app.model_manager.infra.providers.whisper_provider.torch') as mock:
        mock.cuda.is_available.return_value = True
        mock.cuda.get_device_properties.return_value = Mock(total_memory=**********)  # 8GB
        mock.backends.mps.is_available.return_value = False
        yield mock


@pytest.fixture
def mock_whisper_module():
    """Mock the whisper module for testing SDK integration."""
    with patch('app.model_manager.infra.providers.whisper_provider.whisper') as mock:
        model_mock = Mock()
        model_mock.dims = Mock()
        model_mock.encoder = Mock()
        model_mock.transcribe.return_value = {'text': 'test transcription'}
        mock.load_model.return_value = model_mock
        yield mock


@pytest.fixture
def sample_manifest() -> Dict[str, Any]:
    """Sample model manifest for testing."""
    return {
        "provider": "WHISPER",
        "name": "tiny",
        "version": None,
        "installed_at": "2024-01-01T12:00:00Z",
        "source": "whisper_sdk",
        "checksum": None,
        "catalog_version": "1.0.0",
        "device": "cpu",
        "device_compatibility": "CPU compatible",
        "model_metadata": {
            "size": "39 MB",
            "multilingual": True,
            "gpu_recommended": False
        },
        "installation_validated": True
    }


@pytest.fixture
def sample_ollama_models() -> str:
    """Sample Ollama list output."""
    return """NAME                    ID              SIZE    MODIFIED
llama3:latest           a6990ed6be41    4.7 GB  2 hours ago
mistral:7b              f974a74358d6    4.1 GB  1 day ago
codellama:13b           8fdf8f752f6e    7.3 GB  3 days ago
"""


@pytest.fixture
def sample_ollama_html() -> str:
    """Sample HTML from ollama.com/library."""
    return """
<!DOCTYPE html>
<html>
<head><title>Ollama Library</title></head>
<body>
    <div class="models">
        <a href="/library/llama3">Llama 3</a>
        <a href="/library/mistral">Mistral</a>
        <a href="/library/codellama">Code Llama</a>
        <a href="/library/phi">Phi</a>
        <a href="/library/gemma">Gemma</a>
    </div>
</body>
</html>
"""


class MockSSEClient:
    """Mock Server-Sent Events client for testing real-time updates."""
    
    def __init__(self):
        self.callbacks = []
        self.error_callbacks = []
        self.closed = False
    
    def send_event(self, data: Dict[str, Any]):
        """Simulate sending an SSE event."""
        if not self.closed:
            for callback in self.callbacks:
                callback(data)
    
    def send_error(self, error: Exception):
        """Simulate sending an SSE error."""
        if not self.closed:
            for callback in self.error_callbacks:
                callback(error)
    
    def add_callback(self, callback):
        self.callbacks.append(callback)
    
    def add_error_callback(self, callback):
        self.error_callbacks.append(callback)
    
    def close(self):
        self.closed = True


@pytest.fixture
def mock_sse_client() -> MockSSEClient:
    """Create a mock SSE client for testing real-time updates."""
    return MockSSEClient()
