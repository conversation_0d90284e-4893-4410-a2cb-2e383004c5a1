#!/bin/bash
"""
TDD Workflow Installation Script
Installs and configures the complete TDD automation system
"""

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Helper functions
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check if we're in the right directory
check_project_root() {
    if [[ ! -f "scripts/tdd/install.sh" ]]; then
        log_error "Please run this script from the project root directory"
        exit 1
    fi
    
    if [[ ! -d "apps/backend" ]] || [[ ! -d "apps/frontend" ]]; then
        log_error "Backend or frontend directory not found"
        exit 1
    fi
}

# Install Python dependencies for backend TDD
install_backend_dependencies() {
    log_info "Installing backend TDD dependencies..."
    
    cd apps/backend
    
    # Check if uv is available
    if ! command -v uv &> /dev/null; then
        log_error "uv not found. Please install uv first: https://github.com/astral-sh/uv"
        exit 1
    fi
    
    # Add TDD-specific dependencies to pyproject.toml
    log_info "Adding TDD dependencies to pyproject.toml..."
    
    # Check if dependencies are already added
    if ! grep -q "pytest-mock" pyproject.toml; then
        cat >> pyproject.toml << 'EOF'

# TDD-specific dependencies
[project.optional-dependencies.tdd]
pytest-mock = ">=3.11.1"
pytest-benchmark = ">=4.0.0"
pytest-xdist = ">=3.3.1"
pytest-timeout = ">=2.1.0"
freezegun = ">=1.2.2"
factory-boy = ">=3.3.0"
responses = ">=0.23.3"
EOF
    fi
    
    # Install dependencies
    uv sync --extra tdd
    
    log_success "Backend TDD dependencies installed"
    cd ../..
}

# Install frontend testing dependencies
install_frontend_dependencies() {
    log_info "Setting up frontend testing infrastructure..."
    
    cd apps/frontend
    
    # Check if bun is available
    if ! command -v bun &> /dev/null; then
        log_error "Bun not found. Please install bun first: https://bun.sh/"
        exit 1
    fi
    
    # Run the frontend setup script
    node ../../scripts/tdd/setup-frontend-testing.js
    
    log_success "Frontend testing infrastructure setup complete"
    cd ../..
}

# Setup pre-commit hooks
setup_pre_commit_hooks() {
    log_info "Setting up pre-commit hooks..."
    
    # Make sure the hook is executable
    chmod +x scripts/tdd/hooks/pre-commit-tdd
    
    # Install the hook
    if [[ -d ".git" ]]; then
        cp scripts/tdd/hooks/pre-commit-tdd .git/hooks/pre-commit
        chmod +x .git/hooks/pre-commit
        log_success "Pre-commit TDD hook installed"
    else
        log_warning "Not a git repository - pre-commit hook not installed"
    fi
    
    # Optionally setup pre-commit framework
    if command -v pre-commit &> /dev/null; then
        cat > .pre-commit-config.yaml << 'EOF'
repos:
  - repo: local
    hooks:
      - id: tdd-validation
        name: TDD Validation
        entry: python3 scripts/tdd/hooks/pre-commit-tdd
        language: system
        stages: [commit]
        verbose: true
      
      - id: backend-tests
        name: Backend Quick Tests
        entry: bash -c "cd apps/backend && uv run python -m pytest tests/ -v --tb=short -x"
        language: system
        files: '^apps/backend/.*\.py$'
        stages: [push]
        
      - id: frontend-tests  
        name: Frontend Tests
        entry: bash -c "cd apps/frontend && bun test"
        language: system
        files: '^apps/frontend/.*\.(ts|tsx|js|jsx)$'
        stages: [push]
EOF
        
        pre-commit install
        pre-commit install --hook-type pre-push
        log_success "Pre-commit framework configured"
    fi
}

# Create TDD configuration files
create_config_files() {
    log_info "Creating TDD configuration files..."
    
    # Backend pytest configuration enhancement
    cd apps/backend
    
    if [[ -f "pyproject.toml" ]]; then
        # Add TDD-specific pytest configuration
        if ! grep -q "\[tool.pytest.ini_options\]" pyproject.toml; then
            cat >> pyproject.toml << 'EOF'

[tool.pytest.ini_options]
minversion = "7.0"
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*", "*Tests"]
python_functions = ["test_*"]
addopts = [
    "--strict-markers",
    "--strict-config",
    "--verbose",
    "--tb=short",
    "--cov=app",
    "--cov-report=term-missing",
    "--cov-report=json",
    "--cov-report=html:coverage_html",
    "--cov-fail-under=80",
    "--maxfail=10",
    "--disable-warnings",
]
markers = [
    "unit: Unit tests",
    "integration: Integration tests", 
    "london_school: London School TDD tests",
    "behavior: Behavior verification tests",
    "contract: Contract tests",
    "slow: Slow tests",
    "performance: Performance tests",
]
filterwarnings = [
    "ignore::DeprecationWarning",
    "ignore::PendingDeprecationWarning",
]
EOF
        fi
        log_success "Backend pytest configuration updated"
    fi
    
    cd ../..
    
    # Frontend test configuration
    cd apps/frontend
    
    # Create test configuration if it doesn't exist
    if [[ ! -f "vitest.config.ts" ]] && [[ ! -f "jest.config.js" ]]; then
        log_info "Creating frontend test configuration..."
        # The setup-frontend-testing.js script handles this
        node ../../scripts/tdd/setup-frontend-testing.js
    fi
    
    cd ../..
    
    # Create TDD runner scripts
    cat > tdd-run.sh << 'EOF'
#!/bin/bash
# TDD Workflow Runner Script

# Colors
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m'

case "$1" in
    "backend")
        echo -e "${YELLOW}🧪 Running backend TDD workflow...${NC}"
        python3 scripts/tdd/tdd-workflow.py --component backend
        ;;
    "frontend")
        echo -e "${YELLOW}🧪 Running frontend TDD workflow...${NC}"  
        python3 scripts/tdd/tdd-workflow.py --component frontend
        ;;
    "all"|"")
        echo -e "${YELLOW}🧪 Running complete TDD workflow...${NC}"
        python3 scripts/tdd/tdd-workflow.py --component all
        ;;
    "watch")
        echo -e "${YELLOW}👀 Starting TDD watch mode...${NC}"
        python3 scripts/tdd/tdd-workflow.py --watch
        ;;
    "coverage")
        echo -e "${YELLOW}📊 Generating coverage report...${NC}"
        python3 scripts/tdd/coverage-reporter.py --output tdd-coverage-report.html
        ;;
    "london")
        echo -e "${YELLOW}🎭 Running London School TDD analysis...${NC}"
        if [[ -n "$2" ]]; then
            python3 scripts/tdd/london-school-tdd.py --analyze "$2"
        else
            echo "Usage: $0 london <file-path>"
            exit 1
        fi
        ;;
    *)
        echo "TDD Workflow Runner"
        echo ""
        echo "Usage: $0 [command] [args]"
        echo ""
        echo "Commands:"
        echo "  backend     Run backend TDD workflow"
        echo "  frontend    Run frontend TDD workflow" 
        echo "  all         Run complete TDD workflow (default)"
        echo "  watch       Run in watch mode"
        echo "  coverage    Generate coverage report"
        echo "  london      Run London School analysis on file"
        echo ""
        echo "Examples:"
        echo "  $0                                    # Run all tests"
        echo "  $0 backend                            # Backend only"
        echo "  $0 watch                              # Watch mode"
        echo "  $0 coverage                           # Coverage report"
        echo "  $0 london apps/backend/app/main.py    # Analyze file"
        ;;
esac
EOF
    
    chmod +x tdd-run.sh
    log_success "TDD runner script created"
}

# Create development scripts
create_dev_scripts() {
    log_info "Creating development helper scripts..."
    
    # Backend TDD helper
    cat > apps/backend/tdd-backend.sh << 'EOF'
#!/bin/bash
# Backend TDD Helper Script

case "$1" in
    "test")
        echo "🧪 Running backend tests..."
        uv run python -m pytest tests/ -v
        ;;
    "test-watch")
        echo "👀 Running tests in watch mode..."
        uv run python -m pytest tests/ -v --watch
        ;;
    "coverage")
        echo "📊 Running tests with coverage..."
        uv run python -m pytest tests/ --cov=app --cov-report=term-missing --cov-report=html
        ;;
    "london")
        if [[ -n "$2" ]]; then
            echo "🎭 London School analysis for $2..."
            python3 ../../scripts/tdd/london-school-tdd.py --analyze "$2"
        else
            echo "Usage: $0 london <file-path>"
        fi
        ;;
    "generate")
        if [[ -n "$2" ]]; then
            echo "🏗️ Generating tests for $2..."
            python3 ../../scripts/tdd/london-school-tdd.py --generate "$2" --output tests/
        else
            echo "Usage: $0 generate <file-path>"
        fi
        ;;
    *)
        echo "Backend TDD Helper"
        echo "Usage: $0 [command] [args]"
        echo ""
        echo "Commands:"
        echo "  test        Run tests"
        echo "  test-watch  Run tests in watch mode"
        echo "  coverage    Run with coverage"
        echo "  london      Analyze file with London School"
        echo "  generate    Generate test template"
        ;;
esac
EOF
    
    chmod +x apps/backend/tdd-backend.sh
    
    # Frontend TDD helper
    cat > apps/frontend/tdd-frontend.sh << 'EOF'
#!/bin/bash
# Frontend TDD Helper Script

case "$1" in
    "test")
        echo "🧪 Running frontend tests..."
        bun test
        ;;
    "test-watch")
        echo "👀 Running tests in watch mode..."
        bun test --watch
        ;;
    "coverage")
        echo "📊 Running tests with coverage..."
        bun test --coverage
        ;;
    "ui")
        echo "🖥️ Running tests in UI mode..."
        bun test --ui
        ;;
    *)
        echo "Frontend TDD Helper"
        echo "Usage: $0 [command]"
        echo ""
        echo "Commands:"
        echo "  test        Run tests"
        echo "  test-watch  Run tests in watch mode"
        echo "  coverage    Run with coverage"
        echo "  ui          Run with UI"
        ;;
esac
EOF
    
    chmod +x apps/frontend/tdd-frontend.sh
    
    log_success "Development helper scripts created"
}

# Setup IDE integration
setup_ide_integration() {
    log_info "Setting up IDE integration..."
    
    # VS Code settings
    if [[ ! -d ".vscode" ]]; then
        mkdir -p .vscode
    fi
    
    cat > .vscode/settings.json << 'EOF'
{
  "python.testing.pytestEnabled": true,
  "python.testing.unittestEnabled": false,
  "python.testing.pytestArgs": [
    "apps/backend/tests"
  ],
  "python.testing.cwd": "apps/backend",
  "python.defaultInterpreterPath": "apps/backend/.venv/bin/python",
  
  "typescript.preferences.includePackageJsonAutoImports": "auto",
  "jest.jestCommandLine": "cd apps/frontend && bun test",
  "jest.autoRun": "watch",
  
  "files.associations": {
    "*.test.ts": "typescript",
    "*.spec.ts": "typescript",
    "*.test.tsx": "typescriptreact",
    "*.spec.tsx": "typescriptreact"
  },
  
  "editor.codeActionsOnSave": {
    "source.organizeImports": true
  },
  
  "coverage-gutters.coverageFileNames": [
    "apps/backend/coverage.json",
    "apps/frontend/coverage/coverage-final.json"
  ]
}
EOF
    
    # VS Code tasks
    cat > .vscode/tasks.json << 'EOF'
{
  "version": "2.0.0",
  "tasks": [
    {
      "label": "TDD: Run All Tests",
      "type": "shell",
      "command": "./tdd-run.sh",
      "args": ["all"],
      "group": "test",
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "shared"
      }
    },
    {
      "label": "TDD: Watch Mode",
      "type": "shell", 
      "command": "./tdd-run.sh",
      "args": ["watch"],
      "group": "test",
      "isBackground": true
    },
    {
      "label": "TDD: Coverage Report",
      "type": "shell",
      "command": "./tdd-run.sh", 
      "args": ["coverage"],
      "group": "test"
    },
    {
      "label": "Backend: Run Tests",
      "type": "shell",
      "command": "./tdd-backend.sh",
      "args": ["test"],
      "options": {
        "cwd": "apps/backend"
      },
      "group": "test"
    },
    {
      "label": "Frontend: Run Tests",
      "type": "shell",
      "command": "./tdd-frontend.sh",
      "args": ["test"],
      "options": {
        "cwd": "apps/frontend"
      },
      "group": "test"
    }
  ]
}
EOF
    
    log_success "VS Code integration configured"
}

# Validate installation
validate_installation() {
    log_info "Validating TDD installation..."
    
    # Check backend setup
    cd apps/backend
    if uv run python -c "import pytest, pytest_cov, pytest_mock" 2>/dev/null; then
        log_success "Backend TDD dependencies validated"
    else
        log_error "Backend TDD dependencies validation failed"
        return 1
    fi
    cd ../..
    
    # Check frontend setup  
    cd apps/frontend
    if [[ -f "package.json" ]]; then
        if grep -q "@testing-library" package.json; then
            log_success "Frontend testing dependencies validated"
        else
            log_warning "Frontend testing dependencies may be incomplete"
        fi
    fi
    cd ../..
    
    # Check scripts
    if [[ -x "scripts/tdd/tdd-workflow.py" ]]; then
        log_success "TDD workflow script validated"
    else
        log_error "TDD workflow script validation failed"
        return 1
    fi
    
    # Test run
    log_info "Running validation test..."
    if python3 scripts/tdd/tdd-workflow.py --component backend; then
        log_success "TDD workflow validation test passed"
    else
        log_warning "TDD workflow validation test had issues (this may be expected for new projects)"
    fi
    
    return 0
}

# Main installation process
main() {
    echo -e "${BLUE}"
    echo "================================================================"
    echo "🎯 TDD Workflow Automation Installation"
    echo "================================================================"
    echo -e "${NC}"
    echo ""
    echo "This will install and configure:"
    echo "  • London School TDD methodology"
    echo "  • Pre-commit hooks for test-first enforcement"
    echo "  • Coverage reporting with quality gates"
    echo "  • Integration with uv (backend) and Bun (frontend)"
    echo "  • Development tools and IDE integration"
    echo ""
    read -p "Continue with installation? (y/N) " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "Installation cancelled."
        exit 0
    fi
    
    check_project_root
    
    install_backend_dependencies
    install_frontend_dependencies
    setup_pre_commit_hooks
    create_config_files
    create_dev_scripts
    setup_ide_integration
    
    if validate_installation; then
        echo ""
        echo -e "${GREEN}================================================================${NC}"
        echo -e "${GREEN}✅ TDD Workflow Automation Installation Complete!${NC}"
        echo -e "${GREEN}================================================================${NC}"
        echo ""
        echo "🚀 Quick Start:"
        echo "  ./tdd-run.sh                    # Run all tests"
        echo "  ./tdd-run.sh watch              # Watch mode"
        echo "  ./tdd-run.sh coverage           # Coverage report"
        echo ""
        echo "📚 Documentation:"
        echo "  cat scripts/tdd/README.md       # Full documentation"
        echo ""
        echo "🔧 Individual components:"
        echo "  cd apps/backend && ./tdd-backend.sh test"
        echo "  cd apps/frontend && ./tdd-frontend.sh test"
        echo ""
        echo "Happy TDD coding! 🧪✨"
    else
        log_error "Installation validation failed. Please check the errors above."
        exit 1
    fi
}

# Run main installation
main "$@"