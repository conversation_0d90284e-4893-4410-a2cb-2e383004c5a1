"""
Tests for canvas endpoints.
"""

import pytest
from fastapi.testclient import <PERSON><PERSON>lient
from sqlalchemy.ext.asyncio import AsyncSession
from app.main import create_app
from app.models.user import User
from app.models.canvas import Canvas
from uuid import UUID


@pytest.fixture
def client() -> TestClient:
    """Create a test client for the FastAPI app."""
    app = create_app()
    return TestClient(app)


@pytest.fixture
def auth_headers(client: TestClient, test_user: User) -> dict:
    """Get authentication headers for a test user."""
    # Login to get a token
    response = client.post("/api/v1/auth/login", data={
        "username": "<EMAIL>",
        "password": "password123"
    })
    
    assert response.status_code == 200
    token_data = response.json()
    access_token = token_data["access_token"]
    
    return {"Authorization": f"Bearer {access_token}"}


@pytest.mark.asyncio
async def test_create_canvas(client: TestClient, auth_headers: dict):
    """Test create canvas endpoint."""
    response = client.post("/api/v1/canvas/", json={
        "name": "Test Canvas",
        "description": "A test canvas",
        "is_public": False,
        "tags": ["test"]
    }, headers=auth_headers)
    
    assert response.status_code == 200
    data = response.json()
    assert data["name"] == "Test Canvas"
    assert data["description"] == "A test canvas"
    assert data["is_public"] == False
    assert "id" in data
    assert "owner_id" in data


@pytest.mark.asyncio
async def test_get_canvas(client: TestClient, auth_headers: dict, test_canvas: Canvas):
    """Test get canvas endpoint."""
    response = client.get(f"/api/v1/canvas/{test_canvas.id}", headers=auth_headers)
    
    assert response.status_code == 200
    data = response.json()
    assert data["id"] == str(test_canvas.id)
    assert data["name"] == test_canvas.name


@pytest.mark.asyncio
async def test_list_canvases(client: TestClient, auth_headers: dict, test_canvas: Canvas):
    """Test list canvases endpoint."""
    response = client.get("/api/v1/canvas/", headers=auth_headers)
    
    assert response.status_code == 200
    data = response.json()
    assert isinstance(data, list)
    assert len(data) > 0
    # Find our test canvas in the list
    canvas_ids = [canvas["id"] for canvas in data]
    assert str(test_canvas.id) in canvas_ids


@pytest.mark.asyncio
async def test_update_canvas(client: TestClient, auth_headers: dict, test_canvas: Canvas):
    """Test update canvas endpoint."""
    response = client.put(f"/api/v1/canvas/{test_canvas.id}", json={
        "name": "Updated Canvas",
        "description": "An updated test canvas"
    }, headers=auth_headers)
    
    assert response.status_code == 200
    data = response.json()
    assert data["name"] == "Updated Canvas"
    assert data["description"] == "An updated test canvas"


@pytest.mark.asyncio
async def test_delete_canvas(client: TestClient, auth_headers: dict, test_canvas: Canvas):
    """Test delete canvas endpoint."""
    response = client.delete(f"/api/v1/canvas/{test_canvas.id}", headers=auth_headers)
    
    assert response.status_code == 200
    data = response.json()
    assert data["message"] == "Canvas deleted successfully"
    
    # Verify canvas is deleted
    get_response = client.get(f"/api/v1/canvas/{test_canvas.id}", headers=auth_headers)
    assert get_response.status_code == 404