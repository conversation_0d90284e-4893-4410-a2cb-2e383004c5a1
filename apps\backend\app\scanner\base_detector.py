"""
Base detector class for the Project Scanner system.

This module defines the abstract base class that all detectors must implement,
providing common functionality and ensuring consistent interfaces.
"""

import hashlib
import time
from abc import ABC, abstractmethod
from pathlib import Path
from typing import Any, Dict, List, Optional, Set

from .models import (
    DetectorResult,
    Issue,
    IssueLocation,
    IssueSeverity,
    IssueType,
    RemediationSuggestion,
    ScanConfiguration
)


class BaseDetector(ABC):
    """
    Abstract base class for all issue detectors.
    
    Provides common functionality like file filtering, issue creation,
    and metrics collection that all detectors can use.
    """
    
    def __init__(self, name: str, config: ScanConfiguration):
        """
        Initialize the detector.
        
        Args:
            name: Name of the detector
            config: Scan configuration
        """
        self.name = name
        self.config = config
        self.issues: List[Issue] = []
        self.files_processed = 0
        self.start_time = 0.0
        self.execution_time = 0.0
    
    @abstractmethod
    def detect(self, project_path: Path) -> DetectorResult:
        """
        Detect issues in the project.
        
        Args:
            project_path: Path to the project to analyze
            
        Returns:
            DetectorResult containing found issues and metrics
        """
        pass
    
    @abstractmethod
    def get_supported_file_types(self) -> Set[str]:
        """
        Get file extensions that this detector can analyze.
        
        Returns:
            Set of file extensions (e.g., {'.py', '.js', '.ts'})
        """
        pass
    
    def should_analyze_file(self, file_path: Path) -> bool:
        """
        Determine if a file should be analyzed by this detector.
        
        Args:
            file_path: Path to the file
            
        Returns:
            True if file should be analyzed
        """
        # Check file size
        try:
            if file_path.stat().st_size > self.config.max_file_size:
                return False
        except (OSError, IOError):
            return False
        
        # Check if file type is supported
        if file_path.suffix.lower() not in self.get_supported_file_types():
            return False
        
        # Check include patterns
        file_str = str(file_path)
        included = any(file_path.match(pattern) for pattern in self.config.include_patterns)
        if not included:
            return False
        
        # Check exclude patterns
        excluded = any(file_path.match(pattern) for pattern in self.config.exclude_patterns)
        if excluded:
            return False
        
        return True
    
    def get_project_files(self, project_path: Path) -> List[Path]:
        """
        Get all files in the project that should be analyzed.
        
        Args:
            project_path: Path to the project
            
        Returns:
            List of file paths to analyze
        """
        files = []
        supported_extensions = self.get_supported_file_types()
        
        for ext in supported_extensions:
            # Remove the dot from extension for glob pattern
            pattern = f"**/*{ext}"
            files.extend(project_path.glob(pattern))
        
        # Filter files based on configuration
        return [f for f in files if self.should_analyze_file(f)]
    
    def create_issue(
        self,
        issue_type: IssueType,
        severity: IssueSeverity,
        title: str,
        description: str,
        file_path: str,
        line_number: Optional[int] = None,
        column_number: Optional[int] = None,
        line_end: Optional[int] = None,
        function_name: Optional[str] = None,
        class_name: Optional[str] = None,
        rule_id: Optional[str] = None,
        confidence: float = 1.0,
        impact_score: int = 50,
        likelihood_score: int = 50,
        remediation: Optional[RemediationSuggestion] = None,
        tags: Optional[Set[str]] = None,
        cwe_id: Optional[str] = None,
        owasp_category: Optional[str] = None
    ) -> Issue:
        """
        Create a new issue with consistent formatting.
        
        Args:
            issue_type: Type of issue
            severity: Severity level
            title: Brief title
            description: Detailed description
            file_path: Path to file containing issue
            line_number: Line number (optional)
            column_number: Column number (optional)
            line_end: End line number for multi-line issues (optional)
            function_name: Function name if applicable (optional)
            class_name: Class name if applicable (optional)
            rule_id: Specific rule that triggered (optional)
            confidence: Confidence level 0.0-1.0 (default: 1.0)
            impact_score: Impact score 0-100 (default: 50)
            likelihood_score: Likelihood score 0-100 (default: 50)
            remediation: Remediation suggestion (optional)
            tags: Tags for categorization (optional)
            cwe_id: CWE ID if applicable (optional)
            owasp_category: OWASP category if applicable (optional)
            
        Returns:
            Created Issue object
        """
        # Generate unique ID
        issue_id = self._generate_issue_id(file_path, line_number, rule_id, title)
        
        location = IssueLocation(
            file_path=file_path,
            line_number=line_number,
            column_number=column_number,
            line_end=line_end,
            function_name=function_name,
            class_name=class_name
        )
        
        issue = Issue(
            id=issue_id,
            type=issue_type,
            severity=severity,
            title=title,
            description=description,
            location=location,
            detector=self.name,
            rule_id=rule_id,
            confidence=confidence,
            impact_score=impact_score,
            likelihood_score=likelihood_score,
            remediation=remediation,
            tags=tags or set(),
            cwe_id=cwe_id,
            owasp_category=owasp_category
        )
        
        return issue
    
    def _generate_issue_id(
        self,
        file_path: str,
        line_number: Optional[int],
        rule_id: Optional[str],
        title: str
    ) -> str:
        """
        Generate a unique ID for an issue.
        
        Args:
            file_path: File path
            line_number: Line number
            rule_id: Rule ID
            title: Issue title
            
        Returns:
            Unique issue ID
        """
        # Create a deterministic hash based on issue characteristics
        content = f"{self.name}:{file_path}:{line_number or 0}:{rule_id or ''}:{title}"
        return hashlib.md5(content.encode()).hexdigest()[:12]
    
    def add_issue(self, issue: Issue) -> None:
        """
        Add an issue to the detector's issue list.
        
        Args:
            issue: Issue to add
        """
        # Check if issue severity meets minimum threshold
        severity_order = {
            IssueSeverity.INFO: 0,
            IssueSeverity.LOW: 1,
            IssueSeverity.MEDIUM: 2,
            IssueSeverity.HIGH: 3,
            IssueSeverity.CRITICAL: 4
        }
        
        if severity_order[issue.severity] >= severity_order[self.config.min_severity]:
            self.issues.append(issue)
    
    def start_detection(self) -> None:
        """Start the detection process and begin timing."""
        self.start_time = time.time()
        self.issues = []
        self.files_processed = 0
    
    def finish_detection(self, success: bool = True, error_message: Optional[str] = None) -> DetectorResult:
        """
        Finish the detection process and return results.
        
        Args:
            success: Whether detection completed successfully
            error_message: Error message if detection failed
            
        Returns:
            DetectorResult with issues and metrics
        """
        self.execution_time = time.time() - self.start_time
        
        return DetectorResult(
            detector_name=self.name,
            issues=self.issues[:self.config.max_issues_per_type],
            execution_time=self.execution_time,
            files_processed=self.files_processed,
            success=success,
            error_message=error_message
        )
    
    def read_file_safely(self, file_path: Path) -> Optional[str]:
        """
        Safely read a file's content, handling encoding issues.
        
        Args:
            file_path: Path to file
            
        Returns:
            File content as string, or None if read failed
        """
        encodings = ['utf-8', 'utf-8-sig', 'latin-1', 'cp1252']
        
        for encoding in encodings:
            try:
                with open(file_path, 'r', encoding=encoding) as f:
                    return f.read()
            except (UnicodeDecodeError, IOError):
                continue
        
        return None
    
    def get_file_lines(self, file_path: Path) -> Optional[List[str]]:
        """
        Get file content as list of lines.
        
        Args:
            file_path: Path to file
            
        Returns:
            List of lines, or None if read failed
        """
        content = self.read_file_safely(file_path)
        if content is not None:
            return content.splitlines()
        return None


class RuleBasedDetector(BaseDetector):
    """
    Base class for detectors that use pattern matching rules.
    
    Provides common functionality for rule-based detection systems.
    """
    
    def __init__(self, name: str, config: ScanConfiguration):
        super().__init__(name, config)
        self.rules = self._load_rules()
    
    @abstractmethod
    def _load_rules(self) -> Dict[str, Any]:
        """
        Load detection rules for this detector.
        
        Returns:
            Dictionary of rules keyed by rule ID
        """
        pass
    
    def apply_rules_to_file(self, file_path: Path, content: str) -> List[Issue]:
        """
        Apply all rules to a file's content.
        
        Args:
            file_path: Path to the file
            content: File content
            
        Returns:
            List of issues found
        """
        issues = []
        
        for rule_id, rule in self.rules.items():
            try:
                rule_issues = self._apply_rule(rule_id, rule, file_path, content)
                issues.extend(rule_issues)
            except Exception as e:
                # Log rule application error but continue with other rules
                print(f"Warning: Error applying rule {rule_id} to {file_path}: {e}")
        
        return issues
    
    @abstractmethod
    def _apply_rule(self, rule_id: str, rule: Dict[str, Any], file_path: Path, content: str) -> List[Issue]:
        """
        Apply a specific rule to file content.
        
        Args:
            rule_id: ID of the rule
            rule: Rule configuration
            file_path: Path to the file
            content: File content
            
        Returns:
            List of issues found by this rule
        """
        pass