{"semi": true, "trailingComma": "es5", "singleQuote": true, "printWidth": 80, "tabWidth": 2, "useTabs": false, "bracketSpacing": true, "bracketSameLine": false, "arrowParens": "avoid", "endOfLine": "lf", "quoteProps": "as-needed", "jsxSingleQuote": true, "overrides": [{"files": ["*.json", "*.jsonc"], "options": {"printWidth": 120, "tabWidth": 2}}, {"files": ["*.md"], "options": {"printWidth": 100, "proseWrap": "always"}}, {"files": ["*.yaml", "*.yml"], "options": {"tabWidth": 2, "singleQuote": false}}]}