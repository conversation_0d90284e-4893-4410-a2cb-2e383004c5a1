import { useCallback, useState } from 'react';
import { ERROR_MESSAGES } from '../constants';
import { ApiResponse, LoadingState } from '../types';

interface UseApiOptions<T = unknown> {
  showToast?: boolean;
  onSuccess?: (data: T) => void;
  onError?: (error: string) => void;
}

export function useApi<T = unknown>(options: UseApiOptions<T> = {}) {
  const [state, setState] = useState<LoadingState>('idle');
  const [data, setData] = useState<T | null>(null);
  const [error, setError] = useState<string | null>(null);

  const execute = useCallback(async (
    url: string,
    method: 'GET' | 'POST' | 'PUT' | 'DELETE' = 'GET',
    body?: unknown
  ): Promise<ApiResponse<T>> => {
    setState('loading');
    setError(null);

    try {
      const baseUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';

      const response = await fetch(`${baseUrl}${url}`, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: body ? JSON.stringify(body) : undefined,
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || ERROR_MESSAGES.SERVER_ERROR);
      }

      const result = await response.json();

      setData(result);
      setState('success');

      if (options.onSuccess) {
        options.onSuccess(result);
      }

      return { data: result, success: true };
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : ERROR_MESSAGES.NETWORK_ERROR;
      setError(errorMessage);
      setState('error');

      if (options.onError) {
        options.onError(errorMessage);
      }

      return { error: errorMessage, success: false };
    }
  }, [options]);

  const reset = useCallback(() => {
    setState('idle');
    setData(null);
    setError(null);
  }, []);

  return {
    execute,
    reset,
    state,
    data,
    error,
    isLoading: state === 'loading',
    isSuccess: state === 'success',
    isError: state === 'error',
  };
}
