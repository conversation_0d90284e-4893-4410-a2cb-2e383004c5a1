{"_expires_at": **********.02906, "data": {"items": [{"provider": "OLLAMA", "name": "<PERSON><PERSON>", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "all-minilm", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "athene-v2", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "aya", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "aya-expanse", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "b<PERSON><PERSON><PERSON>", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "bespoke-minicheck", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "bge-large", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "bge-m3", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "codebooga", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "codegeex4", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "codegemma", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "codellama", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "codeqwen", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "codestral", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "codeup", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "cogito", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "command-a", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "command-r", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "command-r-plus", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "command-r7b", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "command-r7b-arabic", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "dbrx", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "deepcoder", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "deepscaler", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "deepseek-coder", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "deepseek-coder-v2", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "deepseek-llm", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "deepseek-r1", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "deepseek-v2", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "deepseek-v2.5", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "deepseek-v3", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "dev<PERSON><PERSON>", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "dolphin-llama3", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "dolphin-mistral", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "dolphin-mixtral", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "dolphin-phi", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "dolphin3", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "dolphincoder", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "duckdb-nsql", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "everythinglm", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "exaone-deep", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "exaone3.5", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "falcon", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "falcon2", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "falcon3", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "firefunction-v2", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "gemma", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "gemma2", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "gemma3", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "gemma3n", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "glm4", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "<PERSON><PERSON><PERSON>", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "gpt-oss", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "granite-code", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "granite-embedding", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "granite3-dense", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "granite3-guardian", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "granite3-moe", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "granite3.1-dense", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "granite3.1-moe", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "granite3.2", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "granite3.2-vision", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "granite3.3", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "hermes3", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "internlm2", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "llama-guard3", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "llama-pro", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "llama2", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "llama2-chinese", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "llama2-uncensored", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "llama3", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "llama3-chat<PERSON>", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "llama3-gradient", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "llama3-groq-tool-use", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "llama3.1", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "llama3.2", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "llama3.2-vision", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "llama3.3", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "llama4", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "llava", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "llava-llama3", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "llava-phi3", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "magicoder", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "magistral", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "marco-o1", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "mathstral", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "meditron", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "medllama2", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "megadolphin", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "minicpm-v", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "mistral", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "mistral-large", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "mistral-nemo", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "mistral-openorca", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "mistral-small", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "mistral-small3.1", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "mistral-small3.2", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "mistrallite", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "mixtral", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "moondream", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "mxbai-embed-large", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "nemotron", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "nemotron-mini", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "neural-chat", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "nexusraven", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "nomic-embed-text", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "notus", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "notux", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "nous-hermes", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "nous-hermes2", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "nous-hermes2-mixtral", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "nuextract", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "olmo2", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "open-orca-platypus2", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "openchat", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "opencoder", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "openhermes", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "openthinker", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "orca-mini", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "orca2", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "paraphrase-multilingual", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "phi", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "phi3", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "phi3.5", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "phi4", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "phi4-mini", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "phi4-mini-reasoning", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "phi4-reasoning", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "phind-codellama", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "qwen", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "qwen2", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "qwen2-math", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "qwen2.5", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "qwen2.5-coder", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "qwen2.5vl", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "qwen3", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "qwen3-coder", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "qwq", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "r1-1776", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "reader-lm", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "reflection", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "sailor2", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "samantha-mistral", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "shieldgemma", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "smallthinker", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "smollm", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "smollm2", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "snowflake-arctic-embed", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "snowflake-arctic-embed2", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "solar", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "solar-pro", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "sqlcoder", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "stable-beluga", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "stable-code", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "stablelm-zephyr", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "stablelm2", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "starcoder", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "starcoder2", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "starling-lm", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "tinydolphin", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "tinyllama", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "tulu3", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "vicuna", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "wizard-math", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "wizard-vicuna", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "wizard-vicuna-uncensored", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "wizardcoder", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "wizard<PERSON>", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "wizardlm-uncensored", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "wizardlm2", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "xwinlm", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "yarn-llama2", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "yarn-mistral", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "yi", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "yi-coder", "version": null, "metadata": {"source": "web"}}, {"provider": "OLLAMA", "name": "zephyr", "version": null, "metadata": {"source": "web"}}]}}