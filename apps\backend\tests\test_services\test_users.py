"""
Tests for user service.
"""

import pytest
from sqlalchemy.ext.asyncio import AsyncSession
from app.services.users import UserService
from app.models.user import User
from app.schemas.users import UserCreate


@pytest.mark.asyncio
async def test_create_user(db_session: AsyncSession):
    """Test creating a user through the service."""
    user_service = UserService(db_session)
    
    user_data = UserCreate(
        email="<EMAIL>",
        username="servicetest",
        hashed_password="hashed_password",
        full_name="Service Test User"
    )
    
    user = await user_service.create_user(user_data)
    
    assert user.email == "<EMAIL>"
    assert user.username == "servicetest"
    assert user.full_name == "Service Test User"
    assert isinstance(user.id, uuid.UUID)


@pytest.mark.asyncio
async def test_get_user_by_email(db_session: AsyncSession, test_user: User):
    """Test getting a user by email through the service."""
    user_service = UserService(db_session)
    
    user = await user_service.get_user_by_email("<EMAIL>")
    
    assert user is not None
    assert user.email == "<EMAIL>"
    assert user.username == "testuser"


@pytest.mark.asyncio
async def test_get_user_by_username(db_session: AsyncSession, test_user: User):
    """Test getting a user by username through the service."""
    user_service = UserService(db_session)
    
    user = await user_service.get_user_by_username("testuser")
    
    assert user is not None
    assert user.email == "<EMAIL>"
    assert user.username == "testuser"


@pytest.mark.asyncio
async def test_update_user(db_session: AsyncSession, test_user: User):
    """Test updating a user through the service."""
    user_service = UserService(db_session)
    
    updated_user = await user_service.update_user(
        test_user.id,
        {"full_name": "Updated Test User"}
    )
    
    assert updated_user is not None
    assert updated_user.full_name == "Updated Test User"


@pytest.mark.asyncio
async def test_delete_user(db_session: AsyncSession, test_user: User):
    """Test deleting a user through the service."""
    user_service = UserService(db_session)
    
    # First verify user exists
    user = await user_service.get_user_by_id(test_user.id)
    assert user is not None
    
    # Delete the user
    success = await user_service.delete_user(test_user.id)
    assert success is True
    
    # Verify user is deleted
    deleted_user = await user_service.get_user_by_id(test_user.id)
    assert deleted_user is None