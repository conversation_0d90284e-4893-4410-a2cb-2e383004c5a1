"""
Tests for canvas service.
"""

import pytest
from sqlalchemy.ext.asyncio import AsyncSession
from app.services.canvas import CanvasService, NodeService, ConnectionService
from app.models.user import User
from app.models.canvas import Canvas, Node, Connection
from app.schemas.canvas import CanvasC<PERSON>, CanvasUpdate, NodeCreate, NodeUpdate, ConnectionCreate
from uuid import UUID


@pytest.mark.asyncio
async def test_create_canvas(db_session: AsyncSession, test_user: User):
    """Test creating a canvas through the service."""
    canvas_service = CanvasService(db_session)
    
    canvas_data = CanvasCreate(
        name="Service Test Canvas",
        description="A test canvas created through service",
        is_public=False,
        tags=["service", "test"]
    )
    
    canvas = await canvas_service.create_canvas(canvas_data, test_user.id)
    
    assert canvas.name == "Service Test Canvas"
    assert canvas.description == "A test canvas created through service"
    assert canvas.is_public == False
    assert canvas.tags == ["service", "test"]
    assert isinstance(canvas.id, UUID)
    assert canvas.owner_id == test_user.id


@pytest.mark.asyncio
async def test_get_canvas(db_session: AsyncSession, test_canvas: Canvas):
    """Test getting a canvas through the service."""
    canvas_service = CanvasService(db_session)
    
    canvas = await canvas_service.get(test_canvas.id)
    
    assert canvas is not None
    assert canvas.id == test_canvas.id
    assert canvas.name == test_canvas.name


@pytest.mark.asyncio
async def test_update_canvas(db_session: AsyncSession, test_canvas: Canvas):
    """Test updating a canvas through the service."""
    canvas_service = CanvasService(db_session)
    
    canvas_update = CanvasUpdate(
        name="Updated Test Canvas",
        description="An updated test canvas"
    )
    
    updated_canvas = await canvas_service.update_canvas(
        test_canvas.id,
        canvas_update,
        test_canvas.owner_id
    )
    
    assert updated_canvas is not None
    assert updated_canvas.name == "Updated Test Canvas"
    assert updated_canvas.description == "An updated test canvas"


@pytest.mark.asyncio
async def test_delete_canvas(db_session: AsyncSession, test_canvas: Canvas):
    """Test deleting a canvas through the service."""
    canvas_service = CanvasService(db_session)
    
    # First verify canvas exists
    canvas = await canvas_service.get(test_canvas.id)
    assert canvas is not None
    
    # Delete the canvas
    success = await canvas_service.delete_canvas(test_canvas.id, test_canvas.owner_id)
    assert success is True
    
    # Verify canvas is deleted
    deleted_canvas = await canvas_service.get(test_canvas.id)
    assert deleted_canvas is None


@pytest.mark.asyncio
async def test_create_node(db_session: AsyncSession, test_canvas: Canvas):
    """Test creating a node through the service."""
    node_service = NodeService(db_session)
    
    node_data = NodeCreate(
        type="ui",
        title="Service Test Node",
        description="A test node created through service",
        position={"x": 100, "y": 100},
        size={"width": 150, "height": 100}
    )
    
    node = await node_service.create_node(test_canvas.id, node_data, test_canvas.owner_id)
    
    assert node.type == "ui"
    assert node.title == "Service Test Node"
    assert node.description == "A test node created through service"
    assert node.position["x"] == 100
    assert node.position["y"] == 100
    assert node.size["width"] == 150
    assert node.size["height"] == 100
    assert isinstance(node.id, UUID)
    assert node.canvas_id == test_canvas.id


@pytest.mark.asyncio
async def test_update_node(db_session: AsyncSession, test_node: Node):
    """Test updating a node through the service."""
    node_service = NodeService(db_session)
    
    node_update = NodeUpdate(
        title="Updated Test Node",
        description="An updated test node",
        position={"x": 200, "y": 200}
    )
    
    updated_node = await node_service.update_node(
        test_node.id,
        node_update,
        test_node.canvas.owner_id
    )
    
    assert updated_node is not None
    assert updated_node.title == "Updated Test Node"
    assert updated_node.description == "An updated test node"
    assert updated_node.position["x"] == 200
    assert updated_node.position["y"] == 200


@pytest.mark.asyncio
async def test_delete_node(db_session: AsyncSession, test_node: Node):
    """Test deleting a node through the service."""
    node_service = NodeService(db_session)
    
    # First verify node exists
    node = await node_service.get(test_node.id)
    assert node is not None
    
    # Delete the node
    success = await node_service.delete_node(test_node.id, test_node.canvas.owner_id)
    assert success is True
    
    # Verify node is deleted
    deleted_node = await node_service.get(test_node.id)
    assert deleted_node is None


@pytest.mark.asyncio
async def test_create_connection(db_session: AsyncSession, test_canvas: Canvas, test_node: Node):
    """Test creating a connection through the service."""
    # First create a second node
    node_service = NodeService(db_session)
    node_data = NodeCreate(
        type="logic",
        title="Second Node",
        description="Another test node",
        position={"x": 300, "y": 100},
        size={"width": 150, "height": 100}
    )
    second_node = await node_service.create_node(test_canvas.id, node_data, test_canvas.owner_id)
    
    # Now create connection
    connection_service = ConnectionService(db_session)
    connection_data = ConnectionCreate(
        source_node_id=test_node.id,
        target_node_id=second_node.id,
        connection_type="default",
        label="Test Connection"
    )
    
    connection = await connection_service.create_connection(
        test_canvas.id,
        connection_data,
        test_canvas.owner_id
    )
    
    assert connection.connection_type == "default"
    assert connection.label == "Test Connection"
    assert connection.source_node_id == test_node.id
    assert connection.target_node_id == second_node.id
    assert isinstance(connection.id, UUID)
    assert connection.canvas_id == test_canvas.id


@pytest.mark.asyncio
async def test_delete_connection(db_session: AsyncSession, test_connection: Connection):
    """Test deleting a connection through the service."""
    connection_service = ConnectionService(db_session)
    
    # First verify connection exists
    connection = await connection_service.get(test_connection.id)
    assert connection is not None
    
    # Delete the connection
    success = await connection_service.delete_connection(
        test_connection.id,
        test_connection.canvas.owner_id
    )
    assert success is True
    
    # Verify connection is deleted
    deleted_connection = await connection_service.get(test_connection.id)
    assert deleted_connection is None