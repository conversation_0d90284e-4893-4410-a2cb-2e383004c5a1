"""
Validation exception classes for the LONI backend application.
"""

from typing import Any, Dict, Optional

from .base import AppException


class ValidationException(AppException):
    """
    Exception raised when request validation fails.
    
    Used for input validation errors, schema validation failures,
    and other data validation issues.
    """
    
    def __init__(
        self,
        message: str = "Validation failed",
        field: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        """Initialize validation exception."""
        super().__init__(
            message=message,
            error_code="VALIDATION_ERROR", 
            details=details,
            status_code=400
        )
        self.field = field