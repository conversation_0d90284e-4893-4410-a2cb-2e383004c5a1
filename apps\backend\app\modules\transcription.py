"""
Transcription Module - Modular audio transcription service.

This module provides self-contained audio transcription functionality
using Whisper that can be easily composed with other modules.
"""

from typing import Dict, List, Any, Optional
import asyncio
import os
import time
from concurrent.futures import ThreadPoolExecutor
from pathlib import Path

from . import (
    BaseModule,
    ModuleInput,
    ModuleOutput,
    ModuleType,
    ModuleConfig,
    ModuleStatus
)
import whisper
from ..logger import get_logger


logger = get_logger("modules.transcription")


class WhisperTranscriptionModule(BaseModule):
    """Modular Whisper transcription service with self-contained functionality."""

    def __init__(self, name: str):
        super().__init__(name, ModuleType.TRANSCRIPTION)
        self.model_name: Optional[str] = None
        self.device: str = 'cpu'
        self.cache_dir: Optional[str] = None
        self.model: Optional[whisper.Whisper] = None
        self._executor = ThreadPoolExecutor(max_workers=2, thread_name_prefix="whisper")
        self._model_cache: Dict[str, whisper.Whisper] = {}

    async def initialize(self, config: ModuleConfig) -> bool:
        """Initialize the transcription module."""
        try:
            self._status = ModuleStatus.INITIALIZING

            # Get settings from config
            settings = config.settings or {}
            self.model_name = settings.get('model_name', 'base')
            self.device = settings.get('device', 'cpu')
            self.cache_dir = settings.get('cache_dir')

            # Load the Whisper model
            self._load_model()

            self._status = ModuleStatus.READY
            logger.info(f"Transcription module '{self.name}' initialized with model: {self.model_name}")
            return True

        except Exception as e:
            self._status = ModuleStatus.ERROR
            logger.error(f"Failed to initialize transcription module '{self.name}': {e}")
            return False

    def _load_model(self) -> None:
        """Load the Whisper model with caching."""
        cache_key = f"{self.model_name}_{self.device}"

        try:
            # Check if model is already cached
            if cache_key in self._model_cache:
                logger.debug(f"Using cached Whisper model: {cache_key}")
                self.model = self._model_cache[cache_key]
                return

            logger.info(f"Loading Whisper model: {self.model_name} on {self.device}")

            # Load model with options
            load_options = {"device": self.device}
            if self.cache_dir:
                load_options["download_root"] = self.cache_dir

            self.model = whisper.load_model(self.model_name, **load_options)

            # Cache the model
            self._model_cache[cache_key] = self.model

            logger.info(f"Successfully loaded Whisper model: {cache_key}")

        except Exception as e:
            logger.error(f"Failed to load Whisper model {self.model_name}: {e}")
            raise

    async def validate(self, input_data: ModuleInput) -> bool:
        """Validate input data for transcription."""
        audio_path = input_data.data.get('audio_path')
        if not audio_path:
            logger.error("No audio_path specified in input data")
            return False

        if not os.path.exists(audio_path):
            logger.error(f"Audio file not found: {audio_path}")
            return False

        if not self.model:
            logger.error("Whisper model not loaded")
            return False

        return True

    async def execute(self, input_data: ModuleInput) -> ModuleOutput:
        """Execute transcription operation."""
        if not await self.validate(input_data):
            return ModuleOutput(
                success=False,
                error="Invalid input data"
            )

        audio_path = input_data.data.get('audio_path')
        language = input_data.data.get('language')
        verbose = input_data.data.get('verbose', False)
        options = input_data.data.get('options', {})

        self._status = ModuleStatus.RUNNING

        try:
            start_time = time.time()

            # Perform transcription
            if verbose or options:
                result = await self._transcribe_with_options(audio_path, language, verbose, options)
            else:
                result = await self._transcribe_simple(audio_path)

            duration = time.time() - start_time

            # Extract transcription text
            transcription = result.get("text", "").strip()

            return ModuleOutput(
                success=True,
                data={
                    'transcription': transcription,
                    'language': language,
                    'duration': duration,
                    'word_count': len(transcription.split()) if transcription else 0,
                    'confidence': 0.95,  # Mock confidence score
                    'full_result': result
                },
                metadata={
                    'audio_path': audio_path,
                    'model_name': self.model_name,
                    'device': self.device,
                    'duration': duration,
                    'module': self.name
                }
            )

        except Exception as e:
            logger.error(f"Error transcribing audio {audio_path}: {e}")
            return ModuleOutput(
                success=False,
                error=str(e),
                metadata={'audio_path': audio_path, 'module': self.name}
            )
        finally:
            self._status = ModuleStatus.READY

    async def _transcribe_simple(self, audio_path: str) -> Dict[str, Any]:
        """Simple transcription without additional options."""
        loop = asyncio.get_event_loop()
        result = await loop.run_in_executor(
            self._executor,
            lambda: self.model.transcribe(audio_path)
        )
        return result

    async def _transcribe_with_options(self, audio_path: str, language: Optional[str] = None,
                                    verbose: bool = False, options: Dict[str, Any] = None) -> Dict[str, Any]:
        """Transcribe with additional options."""
        # Prepare transcription options
        transcription_options = options.copy() if options else {}

        if language:
            transcription_options["language"] = language
        if verbose:
            transcription_options["verbose"] = verbose

        loop = asyncio.get_event_loop()
        result = await loop.run_in_executor(
            self._executor,
            lambda: self.model.transcribe(audio_path, **transcription_options)
        )
        return result

    async def batch_transcribe(self, input_data: ModuleInput) -> ModuleOutput:
        """Execute batch transcription operation."""
        audio_paths = input_data.data.get('audio_paths', [])
        if not audio_paths:
            return ModuleOutput(
                success=False,
                error="No audio_paths specified"
            )

        self._status = ModuleStatus.RUNNING

        try:
            start_time = time.time()
            results = []

            # Process files concurrently with limited parallelism
            semaphore = asyncio.Semaphore(2)  # Limit concurrent transcriptions

            async def transcribe_single(audio_path: str) -> Dict[str, Any]:
                async with semaphore:
                    try:
                        input_data_single = ModuleInput(data={'audio_path': audio_path})
                        output = await self.execute(input_data_single)
                        return {
                            'audio_path': audio_path,
                            'success': output.success,
                            'data': output.data if output.success else None,
                            'error': output.error if not output.success else None
                        }
                    except Exception as e:
                        return {
                            'audio_path': audio_path,
                            'success': False,
                            'error': str(e)
                        }

            tasks = [transcribe_single(path) for path in audio_paths]
            results = await asyncio.gather(*tasks, return_exceptions=True)

            # Handle exceptions in results
            processed_results = []
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    processed_results.append({
                        'audio_path': audio_paths[i],
                        'success': False,
                        'error': str(result)
                    })
                else:
                    processed_results.append(result)

            duration = time.time() - start_time
            success_count = sum(1 for r in processed_results if r.get('success', False))

            return ModuleOutput(
                success=True,
                data={
                    'results': processed_results,
                    'total_files': len(audio_paths),
                    'successful': success_count,
                    'failed': len(audio_paths) - success_count,
                    'total_duration': duration
                },
                metadata={
                    'batch_size': len(audio_paths),
                    'successful': success_count,
                    'duration': duration,
                    'module': self.name
                }
            )

        except Exception as e:
            logger.error(f"Error in batch transcription: {e}")
            return ModuleOutput(
                success=False,
                error=str(e),
                metadata={'module': self.name}
            )
        finally:
            self._status = ModuleStatus.READY

    def change_model(self, model_name: str) -> bool:
        """Change the Whisper model."""
        try:
            logger.info(f"Changing Whisper model from {self.model_name} to {model_name}")
            self.model_name = model_name
            self._load_model()
            logger.info(f"Successfully changed Whisper model to {model_name}")
            return True
        except Exception as e:
            logger.error(f"Failed to change Whisper model to {model_name}: {e}")
            return False

    def get_model_info(self) -> Dict[str, Any]:
        """Get comprehensive information about the current model."""
        cache_key = f"{self.model_name}_{self.device}"
        cache_info = {
            "cached": cache_key in self._model_cache,
            "cache_size": len(self._model_cache),
            "cached_models": list(self._model_cache.keys())
        }

        return {
            "model_name": self.model_name,
            "device": self.device,
            "cache_dir": self.cache_dir,
            "model_loaded": self.model is not None,
            "available_models": whisper.available_models(),
            "cache_info": cache_info,
            "executor_info": {
                "max_workers": self._executor._max_workers,
                "active_threads": len(self._executor._threads) if hasattr(self._executor, '_threads') else 0
            }
        }

    def unload_model(self) -> None:
        """Unload the current model to free memory."""
        try:
            if self.model:
                cache_key = f"{self.model_name}_{self.device}"

                logger.info(f"Unloading Whisper model: {cache_key}")

                # Remove from cache if present
                if cache_key in self._model_cache:
                    del self._model_cache[cache_key]
                    logger.debug(f"Removed model from cache: {cache_key}")

                # Set to None to allow garbage collection
                self.model = None

                logger.info(f"Successfully unloaded Whisper model: {cache_key}")

        except Exception as e:
            logger.error(f"Error unloading Whisper model: {e}")
            raise

    def clear_model_cache(self) -> None:
        """Clear all cached models."""
        try:
            logger.info(f"Clearing Whisper model cache (size: {len(self._model_cache)})")
            self._model_cache.clear()
            self.model = None  # Also unload current model
            logger.info("Successfully cleared Whisper model cache")
        except Exception as e:
            logger.error(f"Error clearing model cache: {e}")
            raise

    def preload_models(self, model_names: List[str]) -> Dict[str, bool]:
        """Preload multiple models into cache."""
        results = {}
        try:
            logger.info(f"Preloading Whisper models: {model_names}")

            for model_name in model_names:
                cache_key = f"{model_name}_{self.device}"
                if cache_key not in self._model_cache:
                    try:
                        # Create a temporary instance to load the model
                        temp_options = {"device": self.device}
                        if self.cache_dir:
                            temp_options["download_root"] = self.cache_dir

                        model = whisper.load_model(model_name, **temp_options)
                        self._model_cache[cache_key] = model
                        results[model_name] = True
                        logger.debug(f"Preloaded model: {model_name}")
                    except Exception as e:
                        results[model_name] = False
                        logger.error(f"Failed to preload model {model_name}: {e}")
                else:
                    results[model_name] = True  # Already cached

            logger.info(f"Successfully preloaded {sum(results.values())}/{len(model_names)} models")
            return results

        except Exception as e:
            logger.error(f"Error preloading models: {e}")
            return results

    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        return {
            "cached_models_count": len(self._model_cache),
            "cached_models": list(self._model_cache.keys()),
            "current_model": self.model_name,
            "device": self.device,
            "executor_stats": {
                "max_workers": self._executor._max_workers,
                "active_count": self._executor._work_queue.qsize() if hasattr(self._executor, '_work_queue') else 0
            }
        }

    async def cleanup(self) -> None:
        """Cleanup module resources."""
        try:
            self.clear_model_cache()
            self._executor.shutdown(wait=True)
            await super().cleanup()
            logger.info(f"Transcription module '{self.name}' cleaned up")
        except Exception as e:
            logger.error(f"Error cleaning up transcription module: {e}")
