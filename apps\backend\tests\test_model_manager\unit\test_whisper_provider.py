"""
Unit tests for Whisper provider implementation.
"""

import pytest
import os
from unittest.mock import Mock, patch, call
from datetime import datetime

from app.model_manager.domain.entities import Provider, ModelStatus, ModelId
from app.model_manager.infra.providers.whisper_provider import WhisperProviderImpl
from app.model_manager.utils.errors import ValidationError, ProviderError, NotFoundError


class TestWhisperProviderCatalog:
    """Test Whisper provider catalog operations."""
    
    def test_get_catalog_when_enabled(self, whisper_provider, mock_config):
        """Test catalog retrieval when Whisper is enabled."""
        mock_config.providers.whisper_enabled = True
        
        catalog = whisper_provider.get_catalog()
        
        assert len(catalog) == 3
        assert catalog[0].provider == Provider.WHISPER
        assert catalog[0].name == "tiny"
        assert catalog[0].metadata["size"] == "39 MB"
        assert catalog[0].metadata["multilingual"] is True
        
    def test_get_catalog_when_disabled(self, whisper_provider, mock_config):
        """Test catalog retrieval when Whisper is disabled."""
        mock_config.providers.whisper_enabled = False
        
        catalog = whisper_provider.get_catalog()
        
        assert len(catalog) == 0
    
    def test_catalog_includes_metadata(self, whisper_provider):
        """Test that catalog includes comprehensive metadata."""
        catalog = whisper_provider.get_catalog()
        
        tiny_model = next(m for m in catalog if m.name == "tiny")
        assert tiny_model.metadata["size_bytes"] == 40960000
        assert tiny_model.metadata["vram_required_mb"] == 100
        assert tiny_model.metadata["performance_tier"] == "fast"
        assert "en" in tiny_model.metadata["languages"]
        
        large_model = next(m for m in catalog if m.name == "large")
        assert large_model.metadata["gpu_recommended"] is True
        assert large_model.metadata["performance_tier"] == "quality"


class TestWhisperProviderValidation:
    """Test Whisper provider validation methods."""
    
    def test_validate_name_valid(self, whisper_provider):
        """Test validation with valid model names."""
        # Should not raise
        whisper_provider._validate_name("tiny")
        whisper_provider._validate_name("base")
        whisper_provider._validate_name("large-v2")
        whisper_provider._validate_name("model_name")
    
    def test_validate_name_invalid(self, whisper_provider):
        """Test validation with invalid model names."""
        with pytest.raises(ValidationError) as exc_info:
            whisper_provider._validate_name("")
        assert "Invalid model name" in str(exc_info.value)
        
        with pytest.raises(ValidationError) as exc_info:
            whisper_provider._validate_name("model with spaces")
        assert "Invalid model name" in str(exc_info.value)
        
        with pytest.raises(ValidationError) as exc_info:
            whisper_provider._validate_name("model/with/slashes")
        assert "Invalid model name" in str(exc_info.value)
    
    def test_is_supported_valid_models(self, whisper_provider):
        """Test checking if models are supported."""
        assert whisper_provider._is_supported("tiny") is True
        assert whisper_provider._is_supported("base") is True
        assert whisper_provider._is_supported("large") is True
    
    def test_is_supported_invalid_models(self, whisper_provider):
        """Test checking unsupported models."""
        assert whisper_provider._is_supported("nonexistent") is False
        assert whisper_provider._is_supported("custom-model") is False
    
    def test_detect_device_cpu_only(self, whisper_provider):
        """Test device detection when only CPU is available."""
        with patch('app.model_manager.infra.providers.whisper_provider.TORCH_AVAILABLE', False):
            device = whisper_provider._detect_device()
            assert device == "cpu"
    
    def test_detect_device_cuda_available(self, whisper_provider, mock_torch):
        """Test device detection when CUDA is available."""
        mock_torch.cuda.is_available.return_value = True
        mock_torch.backends.mps.is_available.return_value = False
        
        device = whisper_provider._detect_device()
        assert device == "cuda"
    
    def test_detect_device_mps_available(self, whisper_provider, mock_torch):
        """Test device detection when MPS is available."""
        mock_torch.cuda.is_available.return_value = False
        mock_torch.backends.mps.is_available.return_value = True
        
        device = whisper_provider._detect_device()
        assert device == "mps"
    
    def test_device_compatibility_validation_cpu(self, whisper_provider):
        """Test device compatibility validation for CPU."""
        whisper_provider._device = "cpu"
        
        # CPU should handle all models
        compatible, msg = whisper_provider._validate_device_compatibility("tiny")
        assert compatible is True
        assert "CPU compatible" in msg
        
        # Large model should warn about GPU recommendation
        compatible, msg = whisper_provider._validate_device_compatibility("large")
        assert compatible is True
        assert "GPU recommended" in msg
    
    def test_device_compatibility_validation_cuda_sufficient_memory(self, whisper_provider, mock_torch):
        """Test CUDA compatibility with sufficient memory."""
        whisper_provider._device = "cuda"
        mock_torch.cuda.get_device_properties.return_value = Mock(total_memory=**********)  # 8GB
        
        compatible, msg = whisper_provider._validate_device_compatibility("tiny")
        assert compatible is True
        assert "CUDA compatible" in msg
    
    def test_device_compatibility_validation_cuda_insufficient_memory(self, whisper_provider, mock_torch):
        """Test CUDA compatibility with insufficient memory."""
        whisper_provider._device = "cuda"
        mock_torch.cuda.get_device_properties.return_value = Mock(total_memory=536870912)  # 512MB
        
        compatible, msg = whisper_provider._validate_device_compatibility("large")
        assert compatible is False
        assert "Insufficient VRAM" in msg


class TestWhisperProviderInstallation:
    """Test Whisper provider installation methods."""
    
    def test_install_sdk_mode_success(self, whisper_provider, mock_filesystem, mock_config, mock_whisper_module):
        """Test successful installation using SDK mode."""
        mock_config.whisper.use_cli = False
        mock_filesystem.exists.return_value = False  # No existing manifest
        mock_filesystem.acquire_lock.return_value = True
        
        # Mock numpy for dummy audio data
        with patch('app.model_manager.infra.providers.whisper_provider.np') as mock_np:
            mock_np.zeros.return_value = [0.0] * 16000
            
            model = whisper_provider.install("tiny")
        
        assert model.id.provider == Provider.WHISPER
        assert model.id.name == "tiny"
        assert model.status == ModelStatus.INSTALLED
        mock_whisper_module.load_model.assert_called_once_with("tiny", device="cpu")
        mock_filesystem.write_json_atomic.assert_called_once()
    
    def test_install_cli_mode_success(self, whisper_provider, mock_filesystem, mock_config, mock_process):
        """Test successful installation using CLI mode."""
        mock_config.whisper.use_cli = True
        mock_config.whisper.executable = "whisper"
        mock_filesystem.exists.return_value = False
        mock_filesystem.acquire_lock.return_value = True
        mock_process.which.return_value = "/usr/bin/whisper"
        
        with patch.object(whisper_provider, '_create_test_audio') as mock_audio:
            model = whisper_provider.install("tiny")
        
        assert model.id.provider == Provider.WHISPER
        assert model.id.name == "tiny"
        assert model.status == ModelStatus.INSTALLED
        mock_process.run.assert_called_once()
        mock_audio.assert_called_once()
    
    def test_install_cli_mode_missing_executable(self, whisper_provider, mock_config, mock_process, mock_filesystem):
        """Test installation failure when CLI executable is missing."""
        mock_config.whisper.use_cli = True
        mock_filesystem.acquire_lock.return_value = True
        mock_process.which.return_value = None
        
        with pytest.raises(ProviderError) as exc_info:
            whisper_provider.install("tiny")
        
        assert "Whisper CLI not available" in str(exc_info.value)
        assert exc_info.value.cause == "ENOENT"
    
    def test_install_invalid_model(self, whisper_provider):
        """Test installation of invalid model."""
        with pytest.raises(NotFoundError) as exc_info:
            whisper_provider.install("nonexistent")
        
        assert "Whisper model not in catalog" in str(exc_info.value)
        assert exc_info.value.cause == "CATALOG"
    
    def test_install_device_incompatible(self, whisper_provider, mock_filesystem, mock_torch):
        """Test installation failure due to device incompatibility."""
        mock_filesystem.acquire_lock.return_value = True
        mock_torch.cuda.get_device_properties.return_value = Mock(total_memory=268435456)  # 256MB
        whisper_provider._device = "cuda"
        
        with pytest.raises(ProviderError) as exc_info:
            whisper_provider.install("large")
        
        assert "Device incompatible" in str(exc_info.value)
        assert exc_info.value.cause == "DEVICE_INCOMPATIBLE"
    
    def test_install_lock_unavailable(self, whisper_provider, mock_filesystem):
        """Test installation failure when lock is unavailable."""
        mock_filesystem.acquire_lock.return_value = False
        
        with pytest.raises(ProviderError) as exc_info:
            whisper_provider.install("tiny")
        
        assert "Model is locked" in str(exc_info.value)
        assert exc_info.value.cause == "LOCKED"
    
    def test_install_idempotent_already_installed(self, whisper_provider, mock_filesystem, sample_manifest):
        """Test idempotent installation when model is already installed."""
        mock_filesystem.exists.return_value = True
        mock_filesystem.acquire_lock.return_value = True
        mock_filesystem.read_json.return_value = sample_manifest
        
        with patch.object(whisper_provider, 'validate', return_value=(True, "ok")):
            model = whisper_provider.install("tiny")
        
        assert model.status == ModelStatus.INSTALLED
        # Should not call SDK or CLI installation
        mock_filesystem.write_json_atomic.assert_not_called()
    
    def test_install_sdk_out_of_memory(self, whisper_provider, mock_filesystem, mock_whisper_module):
        """Test installation failure due to out of memory."""
        mock_filesystem.acquire_lock.return_value = True
        mock_whisper_module.load_model.side_effect = RuntimeError("CUDA out of memory")
        
        with pytest.raises(ProviderError) as exc_info:
            whisper_provider.install("tiny")
        
        assert "Insufficient memory" in str(exc_info.value)
        assert exc_info.value.cause == "OUT_OF_MEMORY"
    
    def test_install_sdk_cuda_unavailable(self, whisper_provider, mock_filesystem, mock_whisper_module):
        """Test installation failure when CUDA is unavailable."""
        mock_filesystem.acquire_lock.return_value = True
        mock_whisper_module.load_model.side_effect = RuntimeError("CUDA is unavailable")
        
        with pytest.raises(ProviderError) as exc_info:
            whisper_provider.install("tiny")
        
        assert "CUDA unavailable" in str(exc_info.value)
        assert exc_info.value.cause == "CUDA_UNAVAILABLE"


class TestWhisperProviderConfiguration:
    """Test Whisper provider configuration methods."""
    
    def test_configure_valid_config(self, whisper_provider, mock_filesystem):
        """Test configuring model with valid configuration."""
        config = {
            "device": "cpu",
            "language": "en",
            "temperature": 0.7,
            "beam_size": 5,
            "fp16": False,
            "task": "transcribe"
        }
        
        whisper_provider.configure("tiny", config)
        
        mock_filesystem.write_json_atomic.assert_called_once()
        call_args = mock_filesystem.write_json_atomic.call_args
        saved_config = call_args[0][1]
        
        assert saved_config["provider"] == "WHISPER"
        assert saved_config["name"] == "tiny"
        assert saved_config["config"]["device"] == "cpu"
        assert saved_config["config"]["language"] == "en"
    
    def test_configure_invalid_device(self, whisper_provider):
        """Test configuration with invalid device."""
        config = {"device": "invalid_device"}
        
        with pytest.raises(ValidationError) as exc_info:
            whisper_provider.configure("tiny", config)
        
        assert "Invalid device" in str(exc_info.value)
        assert exc_info.value.cause == "INVALID_DEVICE"
    
    def test_configure_invalid_language(self, whisper_provider):
        """Test configuration with invalid language."""
        config = {"language": "invalid_lang_code"}
        
        with pytest.raises(ValidationError) as exc_info:
            whisper_provider.configure("tiny", config)
        
        assert "Invalid language" in str(exc_info.value)
        assert exc_info.value.cause == "INVALID_LANGUAGE"
    
    def test_configure_invalid_temperature(self, whisper_provider):
        """Test configuration with invalid temperature."""
        config = {"temperature": 1.5}  # > 1.0
        
        with pytest.raises(ValidationError) as exc_info:
            whisper_provider.configure("tiny", config)
        
        assert "Invalid temperature" in str(exc_info.value)
        assert exc_info.value.cause == "INVALID_TEMPERATURE"
    
    def test_configure_invalid_beam_size(self, whisper_provider):
        """Test configuration with invalid beam size."""
        config = {"beam_size": 15}  # > 10
        
        with pytest.raises(ValidationError) as exc_info:
            whisper_provider.configure("tiny", config)
        
        assert "Invalid beam_size" in str(exc_info.value)
        assert exc_info.value.cause == "INVALID_BEAM_SIZE"
    
    def test_configure_invalid_task(self, whisper_provider):
        """Test configuration with invalid task."""
        config = {"task": "invalid_task"}
        
        with pytest.raises(ValidationError) as exc_info:
            whisper_provider.configure("tiny", config)
        
        assert "Invalid task" in str(exc_info.value)
        assert exc_info.value.cause == "INVALID_TASK"
    
    def test_configure_auto_device_selection(self, whisper_provider, mock_filesystem):
        """Test automatic device selection."""
        whisper_provider._device = "cuda"
        config = {"device": "auto"}
        
        whisper_provider.configure("tiny", config)
        
        call_args = mock_filesystem.write_json_atomic.call_args
        saved_config = call_args[0][1]
        assert saved_config["config"]["device"] == "cuda"


class TestWhisperProviderValidationAndUninstall:
    """Test Whisper provider validation and uninstall methods."""
    
    def test_validate_success_sdk_mode(self, whisper_provider, mock_filesystem, mock_config, mock_whisper_module, sample_manifest):
        """Test successful validation in SDK mode."""
        mock_config.whisper.use_cli = False
        mock_filesystem.exists.return_value = True
        mock_filesystem.read_json.return_value = sample_manifest
        
        valid, message = whisper_provider.validate("tiny")
        
        assert valid is True
        assert "validation_passed" in message
        mock_whisper_module.load_model.assert_called_once_with("tiny", device="cpu")
    
    def test_validate_success_cli_mode(self, whisper_provider, mock_filesystem, mock_config, mock_process, sample_manifest):
        """Test successful validation in CLI mode."""
        mock_config.whisper.use_cli = True
        mock_config.whisper.executable = "whisper"
        mock_filesystem.exists.return_value = True
        mock_filesystem.read_json.return_value = sample_manifest
        mock_process.which.return_value = "/usr/bin/whisper"
        
        valid, message = whisper_provider.validate("tiny")
        
        assert valid is True
        assert "validation_passed" in message
    
    def test_validate_missing_files(self, whisper_provider, mock_filesystem):
        """Test validation failure when files are missing."""
        mock_filesystem.exists.return_value = False
        
        valid, message = whisper_provider.validate("tiny")
        
        assert valid is False
        assert "missing_manifest_or_dir" in message
    
    def test_validate_not_validated_installation(self, whisper_provider, mock_filesystem, sample_manifest):
        """Test validation failure when installation was not validated."""
        manifest = sample_manifest.copy()
        manifest["installation_validated"] = False
        
        mock_filesystem.exists.return_value = True
        mock_filesystem.read_json.return_value = manifest
        
        valid, message = whisper_provider.validate("tiny")
        
        assert valid is False
        assert "installation_not_validated" in message
    
    def test_validate_sdk_import_error(self, whisper_provider, mock_filesystem, mock_config, sample_manifest):
        """Test validation failure when SDK is not available."""
        mock_config.whisper.use_cli = False
        mock_filesystem.exists.return_value = True
        mock_filesystem.read_json.return_value = sample_manifest
        
        with patch('app.model_manager.infra.providers.whisper_provider.whisper', side_effect=ImportError()):
            valid, message = whisper_provider.validate("tiny")
        
        assert valid is False
        assert "whisper_sdk_not_available" in message
    
    def test_validate_cli_missing(self, whisper_provider, mock_filesystem, mock_config, mock_process, sample_manifest):
        """Test validation failure when CLI is missing."""
        mock_config.whisper.use_cli = True
        mock_filesystem.exists.return_value = True
        mock_filesystem.read_json.return_value = sample_manifest
        mock_process.which.return_value = None
        
        valid, message = whisper_provider.validate("tiny")
        
        assert valid is False
        assert "whisper_cli_not_available" in message
    
    def test_uninstall_success(self, whisper_provider, mock_filesystem):
        """Test successful model uninstallation."""
        mock_filesystem.acquire_lock.return_value = True
        mock_filesystem.exists.return_value = True
        
        whisper_provider.uninstall("tiny")
        
        mock_filesystem.remove_dir_safe.assert_called_once()
        mock_filesystem.release_lock.assert_called_once()
    
    def test_uninstall_locked(self, whisper_provider, mock_filesystem):
        """Test uninstall failure when model is locked."""
        mock_filesystem.acquire_lock.return_value = False
        
        with pytest.raises(ProviderError) as exc_info:
            whisper_provider.uninstall("tiny")
        
        assert "Model is locked" in str(exc_info.value)
        assert exc_info.value.cause == "LOCKED"
    
    def test_uninstall_idempotent(self, whisper_provider, mock_filesystem):
        """Test that uninstall is idempotent (no error if model doesn't exist)."""
        mock_filesystem.acquire_lock.return_value = True
        mock_filesystem.exists.return_value = False
        
        # Should not raise an error
        whisper_provider.uninstall("tiny")
        
        mock_filesystem.release_lock.assert_called_once()


class TestWhisperProviderHelpers:
    """Test Whisper provider helper methods."""
    
    def test_create_test_audio_success(self, whisper_provider, temp_dir):
        """Test successful test audio creation."""
        audio_path = os.path.join(temp_dir, "test.wav")
        
        whisper_provider._create_test_audio(audio_path)
        
        assert os.path.exists(audio_path)
        assert os.path.getsize(audio_path) > 0
    
    def test_create_test_audio_fallback(self, whisper_provider, temp_dir):
        """Test test audio creation fallback when wave module fails."""
        audio_path = os.path.join(temp_dir, "test.wav")
        
        with patch('wave.open', side_effect=Exception("Wave error")):
            whisper_provider._create_test_audio(audio_path)
        
        # Should create empty file as fallback
        assert os.path.exists(audio_path)
    
    def test_test_model_inference_success(self, whisper_provider, mock_whisper_module):
        """Test successful model inference test."""
        model_mock = Mock()
        model_mock.transcribe.return_value = {'text': 'test transcription'}
        
        with patch('app.model_manager.infra.providers.whisper_provider.np') as mock_np:
            mock_np.zeros.return_value = [0.0] * 16000
            
            # Should not raise
            whisper_provider._test_model_inference(model_mock, "tiny")
    
    def test_test_model_inference_failure(self, whisper_provider, mock_whisper_module):
        """Test model inference test failure."""
        model_mock = Mock()
        model_mock.transcribe.side_effect = RuntimeError("Inference failed")
        
        with patch('app.model_manager.infra.providers.whisper_provider.np') as mock_np:
            mock_np.zeros.return_value = [0.0] * 16000
            
            with pytest.raises(ProviderError) as exc_info:
                whisper_provider._test_model_inference(model_mock, "tiny")
            
            assert "Model inference test failed" in str(exc_info.value)
            assert exc_info.value.cause == "INFERENCE_TEST_FAIL"
    
    def test_metadata_from_manifest(self, whisper_provider, mock_filesystem, sample_manifest):
        """Test metadata extraction from manifest."""
        mock_filesystem.read_json.return_value = sample_manifest
        
        metadata = whisper_provider._metadata_from_manifest("/path/to/manifest.json", 1024000)
        
        assert metadata.size_on_disk_bytes == 1024000
        assert metadata.source == "whisper_sdk"
        assert metadata.extra["catalog_version"] == "1.0.0"
        assert metadata.extra["device"] == "cpu"
        assert metadata.extra["installation_validated"] is True
