'use client';

import { AppLayout } from '@/components/layout/AppLayout';
import { Card } from '@/components/common/Card';
import { Button } from '@/components/common/Button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { ScanProgressCard } from '@/components/features/project-scanner/ScanProgressCard';
import { IssueCard } from '@/components/features/project-scanner/IssueCard';
import { ScanResultsChart } from '@/components/features/project-scanner/ScanResultsChart';
import { ProjectUploadCard } from '@/components/features/project-scanner/ProjectUploadCard';
import { useProjectScanner } from '@/lib/hooks/useProjectScanner';
import { 
  Search, 
  Upload, 
  Play, 
  Pause, 
  Download, 
  Filter, 
  AlertTriangle, 
  Shield, 
  Zap, 
  Code, 
  FileText,
  BarChart3,
  PieChart,
  TrendingUp,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  Info,
  Settings,
  RefreshCw,
  Eye,
  ChevronDown,
  ChevronRight,
  ExternalLink,
  Github,
  Folder
} from 'lucide-react';
import { motion } from 'framer-motion';
import { useState, useEffect } from 'react';
import type { 
  ScanResult, 
  ScanProgress, 
  ScanFilter, 
  IssueSeverity, 
  IssueCategory, 
  DashboardStats,
  ScanIssue 
} from '@/lib/types';

// Mock data for demonstration
const mockStats: DashboardStats = {
  totalScans: 45,
  completedScans: 42,
  averageScanTime: 145,
  totalIssuesFound: 1247,
  criticalIssuesResolved: 23,
  projectsScanned: 12,
  lastScanDate: new Date('2024-01-15T10:30:00')
};

const mockProgress: ScanProgress = {
  scanId: 'scan-123',
  status: 'scanning',
  currentPhase: 'Analyzing dependencies',
  progress: 65,
  estimatedTimeRemaining: 120,
  filesScanned: 234,
  totalFiles: 456,
  startedAt: new Date(),
  updatedAt: new Date()
};

const mockRecentScans: ScanResult[] = [
  {
    id: 'result-1',
    scanId: 'scan-1',
    projectName: 'E-commerce Frontend',
    scanType: 'comprehensive',
    summary: {
      totalIssues: 23,
      issuesBySeverity: { critical: 2, high: 5, medium: 10, low: 6, info: 0 },
      issuesByCategory: { security: 3, performance: 8, maintainability: 7, reliability: 3, style: 2, dependency: 0, documentation: 0 },
      filesScanned: 127,
      linesOfCode: 15420,
      technicalDebt: '3.2 days',
      score: 72
    },
    issues: [],
    metrics: {
      scanDuration: 186,
      performanceMetrics: {
        cyclomatic_complexity: 12.5,
        cognitive_complexity: 8.2,
        maintainability_index: 68
      },
      dependencyMetrics: {
        outdated: 5,
        vulnerable: 2,
        total: 89
      }
    },
    recommendations: ['Update outdated dependencies', 'Refactor complex functions', 'Add error boundaries'],
    completedAt: new Date('2024-01-15T09:45:00')
  }
];

const mockIssues: ScanIssue[] = [
  {
    id: 'issue-1',
    scanId: 'scan-1',
    severity: 'critical',
    category: 'security',
    title: 'SQL Injection vulnerability',
    description: 'Direct string concatenation in SQL query without parameterization',
    file: 'src/api/users.ts',
    line: 45,
    column: 12,
    rule: 'sql-injection',
    ruleUrl: 'https://rules.example.com/sql-injection',
    codeSnippet: 'const query = `SELECT * FROM users WHERE id = ${userId}`;',
    suggestion: 'Use parameterized queries or ORM methods',
    affectedCode: {
      startLine: 43,
      endLine: 47,
      content: '// Affected code block here'
    }
  },
  {
    id: 'issue-2',
    scanId: 'scan-1',
    severity: 'high',
    category: 'performance',
    title: 'Large bundle size detected',
    description: 'Bundle size exceeds recommended threshold',
    file: 'src/components/Dashboard.tsx',
    line: 1,
    rule: 'bundle-size',
    suggestion: 'Consider code splitting and lazy loading'
  }
];

const severityColors = {
  critical: 'destructive',
  high: 'destructive',
  medium: 'default',
  low: 'secondary',
  info: 'outline'
} as const;

const severityIcons = {
  critical: XCircle,
  high: AlertTriangle,
  medium: AlertCircle,
  low: Info,
  info: Info
};

const categoryIcons = {
  security: Shield,
  performance: Zap,
  maintainability: Code,
  reliability: CheckCircle,
  style: FileText,
  dependency: Package,
  documentation: FileText
};

export default function ProjectScannerPage() {
  const [activeTab, setActiveTab] = useState('overview');
  const [selectedScan, setSelectedScan] = useState<ScanResult | null>(null);
  const [filter, setFilter] = useState<ScanFilter>({
    severities: [],
    categories: [],
    files: [],
    searchQuery: '',
    sortBy: 'severity',
    sortOrder: 'desc'
  });

  const {
    activeScans,
    scanHistory,
    dashboardStats,
    loading,
    error,
    startScan,
    cancelScan,
    exportScanResults,
    filterIssues,
    isScanning
  } = useProjectScanner();

  const handleStartScan = async (request: Omit<ProjectScanRequest, 'id' | 'createdAt' | 'status'>) => {
    try {
      await startScan(request);
    } catch (err) {
      console.error('Failed to start scan:', err);
    }
  };

  const handleCancelScan = async (scanId: string) => {
    try {
      await cancelScan(scanId);
    } catch (err) {
      console.error('Failed to cancel scan:', err);
    }
  };

  const handleExportResults = async (scanId: string, format: 'json' | 'csv' | 'pdf' | 'html') => {
    try {
      await exportScanResults(scanId, {
        format,
        includeCodeSnippets: true,
        groupBy: 'severity',
        filters: filter
      });
    } catch (err) {
      console.error('Failed to export results:', err);
    }
  };

  // Use real data if available, fallback to mock data
  const stats = dashboardStats || mockStats;
  const recentScans = scanHistory.length > 0 ? scanHistory : mockRecentScans;
  const currentActiveScans = activeScans.length > 0 ? activeScans : (isScanning ? [mockProgress] : []);
  const currentIssues = selectedScan?.issues || mockIssues;

  return (
    <AppLayout>
      <div className="space-y-8">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold">Project Scanner</h1>
            <p className="text-muted-foreground">
              Comprehensive code analysis and security scanning
            </p>
          </div>
          <div className="flex gap-3">
            <Dialog>
              <DialogTrigger asChild>
                <Button variant="outline">
                  <Settings className="h-4 w-4 mr-2" />
                  Settings
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Scanner Settings</DialogTitle>
                </DialogHeader>
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="defaultScanType">Default Scan Type</Label>
                    <Select>
                      <SelectTrigger>
                        <SelectValue placeholder="Select scan type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="security">Security</SelectItem>
                        <SelectItem value="quality">Quality</SelectItem>
                        <SelectItem value="performance">Performance</SelectItem>
                        <SelectItem value="comprehensive">Comprehensive</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="autoScan"
                      className="rounded"
                    />
                    <Label htmlFor="autoScan">Enable auto-scan on file changes</Label>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
            
            <Button gradient>
              <Upload className="h-4 w-4 mr-2" />
              New Scan
            </Button>
          </div>
        </div>

        {/* Stats Overview */}
        <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-6">
          <Card className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Total Scans</p>
                <p className="text-2xl font-bold">{stats.totalScans}</p>
              </div>
              <BarChart3 className="h-8 w-8 text-primary" />
            </div>
          </Card>
          
          <Card className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Issues Found</p>
                <p className="text-2xl font-bold">{stats.totalIssuesFound}</p>
              </div>
              <AlertTriangle className="h-8 w-8 text-orange-500" />
            </div>
          </Card>
          
          <Card className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Critical Fixed</p>
                <p className="text-2xl font-bold">{stats.criticalIssuesResolved}</p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-500" />
            </div>
          </Card>
          
          <Card className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Avg Scan Time</p>
                <p className="text-2xl font-bold">{Math.round(stats.averageScanTime / 60)}m</p>
              </div>
              <Clock className="h-8 w-8 text-blue-500" />
            </div>
          </Card>
          
          <Card className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Projects</p>
                <p className="text-2xl font-bold">{stats.projectsScanned}</p>
              </div>
              <Folder className="h-8 w-8 text-purple-500" />
            </div>
          </Card>
          
          <Card className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Success Rate</p>
                <p className="text-2xl font-bold">
                  {Math.round((stats.completedScans / stats.totalScans) * 100)}%
                </p>
              </div>
              <TrendingUp className="h-8 w-8 text-green-500" />
            </div>
          </Card>
        </div>

        {/* Active Scan Progress */}
        {currentActiveScans.map((progress) => (
          <ScanProgressCard
            key={progress.scanId}
            progress={progress}
            onCancel={() => handleCancelScan(progress.scanId)}
          />
        ))}

        {/* Main Content Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="issues">Issues</TabsTrigger>
            <TabsTrigger value="reports">Reports</TabsTrigger>
            <TabsTrigger value="history">History</TabsTrigger>
          </TabsList>
          
          <TabsContent value="overview" className="space-y-6">
            {/* Project Upload/Selection */}
            <ProjectUploadCard
              onStartScan={handleStartScan}
              loading={loading}
            />

            {/* Recent Scans */}
            <Card className="p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold">Recent Scans</h3>
                <Button variant="outline" size="sm">
                  View All
                </Button>
              </div>
              <div className="space-y-4">
                {recentScans.map((scan) => (
                  <div 
                    key={scan.id}
                    className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800/50 cursor-pointer transition-colors"
                    onClick={() => setSelectedScan(scan)}
                  >
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        <h4 className="font-medium">{scan.projectName}</h4>
                        <Badge variant="outline">{scan.scanType}</Badge>
                        <div className="flex gap-1">
                          {Object.entries(scan.summary.issuesBySeverity).map(([severity, count]) => 
                            count > 0 && (
                              <Badge 
                                key={severity}
                                variant={severityColors[severity as IssueSeverity]}
                                className="text-xs"
                              >
                                {count}
                              </Badge>
                            )
                          )}
                        </div>
                      </div>
                      <p className="text-sm text-muted-foreground">
                        {scan.summary.filesScanned} files • {scan.summary.linesOfCode.toLocaleString()} lines
                        {scan.summary.score && ` • Score: ${scan.summary.score}/100`}
                      </p>
                    </div>
                    
                    <div className="flex items-center gap-3">
                      <div className="text-right text-sm text-muted-foreground">
                        {scan.completedAt?.toLocaleDateString()}
                      </div>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleExportResults(scan.scanId, 'pdf');
                        }}
                      >
                        <Download className="h-4 w-4" />
                      </Button>
                      <ChevronRight className="h-4 w-4" />
                    </div>
                  </div>
                ))}
              </div>
              
              {selectedScan && (
                <div className="mt-6 pt-6 border-t">
                  <h4 className="text-lg font-semibold mb-4">Scan Results Visualization</h4>
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <ScanResultsChart scanResult={selectedScan} chartType="severity" />
                    <ScanResultsChart scanResult={selectedScan} chartType="category" />
                  </div>
                  <div className="mt-6">
                    <ScanResultsChart scanResult={selectedScan} chartType="timeline" />
                  </div>
                </div>
              )}
            </Card>
          </TabsContent>
          
          <TabsContent value="issues" className="space-y-6">
            {/* Issue Filters */}
            <Card className="p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold">Issues</h3>
                <div className="flex gap-2">
                  <Button variant="outline" size="sm">
                    <Filter className="h-4 w-4 mr-2" />
                    Filter
                  </Button>
                  <Button variant="outline" size="sm">
                    <Download className="h-4 w-4 mr-2" />
                    Export
                  </Button>
                </div>
              </div>
              
              <div className="flex gap-4 mb-6">
                <div className="flex-1">
                  <Input 
                    placeholder="Search issues..." 
                    value={filter.searchQuery}
                    onChange={(e) => setFilter({ ...filter, searchQuery: e.target.value })}
                  />
                </div>
                
                <Select>
                  <SelectTrigger className="w-32">
                    <SelectValue placeholder="Severity" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All</SelectItem>
                    <SelectItem value="critical">Critical</SelectItem>
                    <SelectItem value="high">High</SelectItem>
                    <SelectItem value="medium">Medium</SelectItem>
                    <SelectItem value="low">Low</SelectItem>
                  </SelectContent>
                </Select>
                
                <Select>
                  <SelectTrigger className="w-36">
                    <SelectValue placeholder="Category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All</SelectItem>
                    <SelectItem value="security">Security</SelectItem>
                    <SelectItem value="performance">Performance</SelectItem>
                    <SelectItem value="maintainability">Maintainability</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              {/* Issues List */}
              <div className="space-y-3">
                {filterIssues(currentIssues, filter).map((issue) => (
                  <IssueCard
                    key={issue.id}
                    issue={issue}
                    onViewInEditor={(file, line) => {
                      console.log('Open in editor:', file, line);
                      // Implement editor integration
                    }}
                    onMarkAsFixed={(issueId) => {
                      console.log('Mark as fixed:', issueId);
                      // Implement mark as fixed functionality
                    }}
                    onIgnore={(issueId) => {
                      console.log('Ignore issue:', issueId);
                      // Implement ignore functionality
                    }}
                  />
                ))}
                
                {filterIssues(currentIssues, filter).length === 0 && (
                  <div className="text-center py-12 text-muted-foreground">
                    <Search className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p>No issues found matching your filters</p>
                    <p className="text-sm mt-2">Try adjusting your search criteria</p>
                  </div>
                )}
              </div>
            </Card>
          </TabsContent>
          
          <TabsContent value="reports" className="space-y-6">
            <Card className="p-6">
              <h3 className="text-lg font-semibold mb-4">Generate Report</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div>
                    <Label>Report Type</Label>
                    <Select>
                      <SelectTrigger>
                        <SelectValue placeholder="Select report type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="summary">Executive Summary</SelectItem>
                        <SelectItem value="detailed">Detailed Report</SelectItem>
                        <SelectItem value="security">Security Report</SelectItem>
                        <SelectItem value="compliance">Compliance Report</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div>
                    <Label>Format</Label>
                    <Select>
                      <SelectTrigger>
                        <SelectValue placeholder="Select format" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="pdf">PDF</SelectItem>
                        <SelectItem value="html">HTML</SelectItem>
                        <SelectItem value="json">JSON</SelectItem>
                        <SelectItem value="csv">CSV</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                
                <div className="space-y-4">
                  <div>
                    <Label>Include</Label>
                    <div className="space-y-2 mt-2">
                      {[
                        'Code snippets',
                        'Recommendations',
                        'Charts and graphs',
                        'File locations',
                        'Historical data'
                      ].map((option) => (
                        <div key={option} className="flex items-center space-x-2">
                          <input
                            type="checkbox"
                            id={option}
                            className="rounded"
                            defaultChecked
                          />
                          <Label htmlFor={option} className="text-sm">
                            {option}
                          </Label>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
              
              <div className="flex justify-end mt-6">
                <Button>
                  <Download className="h-4 w-4 mr-2" />
                  Generate Report
                </Button>
              </div>
            </Card>
            
            {/* Recent Reports */}
            <Card className="p-6">
              <h3 className="text-lg font-semibold mb-4">Recent Reports</h3>
              <div className="space-y-3">
                {[
                  { name: 'E-commerce Security Report', type: 'Security', date: '2024-01-15', format: 'PDF' },
                  { name: 'Code Quality Summary', type: 'Quality', date: '2024-01-14', format: 'HTML' },
                  { name: 'Performance Analysis', type: 'Performance', date: '2024-01-13', format: 'PDF' }
                ].map((report, index) => (
                  <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                    <div>
                      <h4 className="font-medium">{report.name}</h4>
                      <p className="text-sm text-muted-foreground">
                        {report.type} • {report.date} • {report.format}
                      </p>
                    </div>
                    <Button variant="outline" size="sm">
                      <Download className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
              </div>
            </Card>
          </TabsContent>
          
          <TabsContent value="history" className="space-y-6">
            <Card className="p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold">Scan History</h3>
                <Button variant="outline" size="sm">
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Refresh
                </Button>
              </div>
              
              <div className="space-y-3">
                {mockRecentScans.map((scan, index) => (
                  <div key={scan.id} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex-1">
                      <div className="flex items-center gap-3">
                        <h4 className="font-medium">{scan.projectName}</h4>
                        <Badge variant="outline">{scan.scanType}</Badge>
                        <Badge variant="secondary">
                          <CheckCircle className="h-3 w-3 mr-1" />
                          Completed
                        </Badge>
                      </div>
                      <div className="flex items-center gap-4 mt-1 text-sm text-muted-foreground">
                        <span>{scan.completedAt?.toLocaleDateString()}</span>
                        <span>{scan.summary.filesScanned} files</span>
                        <span>{scan.summary.totalIssues} issues</span>
                        <span>{Math.round(scan.metrics.scanDuration / 60)}m {scan.metrics.scanDuration % 60}s</span>
                      </div>
                    </div>
                    
                    <div className="flex gap-2">
                      <Button variant="outline" size="sm">
                        <Eye className="h-4 w-4" />
                      </Button>
                      <Button variant="outline" size="sm">
                        <Download className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </AppLayout>
  );
}

// Add missing Package component or import
function Package({ className }: { className?: string }) {
  return (
    <svg
      className={className}
      fill="none"
      stroke="currentColor"
      viewBox="0 0 24 24"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"
      />
    </svg>
  );
}