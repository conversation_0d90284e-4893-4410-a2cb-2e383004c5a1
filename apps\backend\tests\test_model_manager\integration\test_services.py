"""
Integration tests for model manager services.
"""

import pytest
from unittest.mock import Mock, patch

from app.model_manager.domain.entities import Provider, ModelStatus, ModelId, Model, AvailableModel, Pagination
from app.model_manager.domain.services import CatalogService, InventoryService, InstallService, ConfigService
from app.model_manager.utils.errors import ValidationError, ProviderError, NotFoundError


class TestCatalogServiceIntegration:
    """Test CatalogService integration with providers."""
    
    @pytest.fixture
    def catalog_service(self, whisper_provider, ollama_provider, mock_filesystem):
        """Create catalog service with mock providers."""
        return CatalogService(whisper_provider, ollama_provider, mock_filesystem)
    
    def test_list_available_all_providers(self, catalog_service, whisper_provider, ollama_provider):
        """Test listing available models from all providers."""
        # Mock provider responses
        whisper_models = [
            AvailableModel(provider=Provider.WHISPER, name="tiny", metadata={"size": "39 MB"}),
            AvailableModel(provider=Provider.WHISPER, name="base", metadata={"size": "74 MB"})
        ]
        
        ollama_models = [
            AvailableModel(provider=Provider.OLLAMA, name="llama3", metadata={"size_bytes": **********}),
            AvailableModel(provider=Provider.OLLAMA, name="mistral", metadata={"size_bytes": **********})
        ]
        
        with patch.object(whisper_provider, 'get_catalog', return_value=whisper_models), \
             patch.object(ollama_provider, 'get_catalog') as mock_ollama_catalog:
            
            from app.model_manager.domain.entities import PagedResult
            mock_ollama_catalog.return_value = PagedResult(
                items=ollama_models,
                total=2,
                page=1,
                page_size=50
            )
            
            result = catalog_service.list_available(None, 1, 50, None)
        
        assert result.total == 4  # 2 Whisper + 2 Ollama
        assert len(result.items) == 4
        
        # Check models are from both providers
        providers = [model.provider for model in result.items]
        assert Provider.WHISPER in providers
        assert Provider.OLLAMA in providers
    
    def test_list_available_single_provider(self, catalog_service, whisper_provider, ollama_provider):
        """Test listing available models from single provider."""
        whisper_models = [
            AvailableModel(provider=Provider.WHISPER, name="tiny", metadata={})
        ]
        
        with patch.object(whisper_provider, 'get_catalog', return_value=whisper_models), \
             patch.object(ollama_provider, 'get_catalog') as mock_ollama_catalog:
            
            mock_ollama_catalog.return_value = Mock(items=[], total=0)
            
            result = catalog_service.list_available(Provider.WHISPER, 1, 50, None)
        
        assert result.total == 1
        assert len(result.items) == 1
        assert result.items[0].provider == Provider.WHISPER
        
        # Ollama catalog should not be called
        mock_ollama_catalog.assert_not_called()
    
    def test_list_available_with_query_filter(self, catalog_service, whisper_provider, ollama_provider):
        """Test listing available models with query filtering."""
        whisper_models = [
            AvailableModel(provider=Provider.WHISPER, name="tiny", metadata={}),
            AvailableModel(provider=Provider.WHISPER, name="large", metadata={})
        ]
        
        ollama_models = [
            AvailableModel(provider=Provider.OLLAMA, name="llama3", metadata={}),
            AvailableModel(provider=Provider.OLLAMA, name="mistral", metadata={})
        ]
        
        with patch.object(whisper_provider, 'get_catalog', return_value=whisper_models), \
             patch.object(ollama_provider, 'get_catalog') as mock_ollama_catalog:
            
            from app.model_manager.domain.entities import PagedResult
            mock_ollama_catalog.return_value = PagedResult(
                items=ollama_models,
                total=2,
                page=1,
                page_size=50
            )
            
            result = catalog_service.list_available(None, 1, 50, "large")
        
        # Should only return models matching "large"
        assert result.total == 1
        assert result.items[0].name == "large"
    
    def test_list_available_provider_error(self, catalog_service, whisper_provider, ollama_provider):
        """Test listing available models when provider raises error."""
        with patch.object(whisper_provider, 'get_catalog', side_effect=ProviderError(
            message="Catalog fetch failed",
            provider="WHISPER",
            operation="CATALOG",
            cause="NETWORK"
        )):
            
            with pytest.raises(ProviderError) as exc_info:
                catalog_service.list_available(Provider.WHISPER, 1, 50, None)
            
            assert "Catalog fetch failed" in str(exc_info.value)


class TestInventoryServiceIntegration:
    """Test InventoryService integration with filesystem."""
    
    @pytest.fixture
    def inventory_service(self, mock_filesystem):
        """Create inventory service with mock filesystem."""
        return InventoryService(mock_filesystem)
    
    def test_list_installed_models(self, inventory_service, mock_filesystem, temp_dir):
        """Test listing installed models from filesystem."""
        # Mock filesystem structure
        mock_filesystem.exists.return_value = True
        mock_filesystem.list_dirs.return_value = ["whisper", "ollama"]
        
        # Mock Whisper models
        whisper_models = ["tiny", "base"]
        ollama_models = ["llama3_latest", "mistral_7b"]
        
        def mock_list_dirs_side_effect(path):
            if "whisper" in path:
                return whisper_models
            elif "ollama" in path:
                return ollama_models
            return []
        
        mock_filesystem.list_dirs.side_effect = mock_list_dirs_side_effect
        
        # Mock manifest files
        def mock_manifest_data(path):
            if "tiny" in path:
                return {
                    "provider": "WHISPER",
                    "name": "tiny",
                    "version": None,
                    "installed_at": "2024-01-01T12:00:00Z",
                    "source": "whisper_sdk"
                }
            elif "base" in path:
                return {
                    "provider": "WHISPER",
                    "name": "base",
                    "version": None,
                    "installed_at": "2024-01-01T12:30:00Z",
                    "source": "whisper_sdk"
                }
            elif "llama3" in path:
                return {
                    "provider": "OLLAMA",
                    "name": "llama3",
                    "version": "latest",
                    "installed_at": "2024-01-01T13:00:00Z",
                    "source": "ollama_cli"
                }
            elif "mistral" in path:
                return {
                    "provider": "OLLAMA",
                    "name": "mistral",
                    "version": "7b",
                    "installed_at": "2024-01-01T13:30:00Z",
                    "source": "ollama_cli"
                }
            return {}
        
        mock_filesystem.read_json.side_effect = mock_manifest_data
        mock_filesystem.size_on_disk.return_value = 1024000
        
        models = inventory_service.list_installed()
        
        assert len(models) == 4
        
        # Check provider distribution
        whisper_models = [m for m in models if m.id.provider == Provider.WHISPER]
        ollama_models = [m for m in models if m.id.provider == Provider.OLLAMA]
        
        assert len(whisper_models) == 2
        assert len(ollama_models) == 2
        
        # Check specific models
        tiny_model = next(m for m in models if m.id.name == "tiny")
        assert tiny_model.id.provider == Provider.WHISPER
        assert tiny_model.status == ModelStatus.INSTALLED
        assert tiny_model.metadata.source == "whisper_sdk"
        
        llama_model = next(m for m in models if m.id.name == "llama3")
        assert llama_model.id.provider == Provider.OLLAMA
        assert llama_model.id.version == "latest"
        assert llama_model.metadata.source == "ollama_cli"
    
    def test_list_installed_empty(self, inventory_service, mock_filesystem):
        """Test listing installed models when none exist."""
        mock_filesystem.exists.return_value = False
        
        models = inventory_service.list_installed()
        
        assert len(models) == 0
    
    def test_list_installed_corrupted_manifest(self, inventory_service, mock_filesystem, temp_dir):
        """Test listing installed models with corrupted manifest."""
        mock_filesystem.exists.return_value = True
        mock_filesystem.list_dirs.side_effect = [
            ["whisper"],  # Provider dirs
            ["tiny"]      # Model dirs
        ]
        
        # Mock corrupted manifest
        mock_filesystem.read_json.side_effect = Exception("Invalid JSON")
        
        models = inventory_service.list_installed()
        
        # Should handle corrupted manifests gracefully
        assert len(models) == 0


class TestInstallServiceIntegration:
    """Test InstallService integration with providers."""
    
    @pytest.fixture
    def install_service(self, whisper_provider, ollama_provider):
        """Create install service with mock providers."""
        return InstallService(whisper_provider, ollama_provider)
    
    def test_install_whisper_model(self, install_service, whisper_provider):
        """Test installing Whisper model."""
        expected_model = Model(
            id=ModelId(provider=Provider.WHISPER, name="tiny", version=None),
            status=ModelStatus.INSTALLED,
            metadata=Mock()
        )
        
        with patch.object(whisper_provider, 'install', return_value=expected_model) as mock_install:
            model = install_service.install(Provider.WHISPER, "tiny", None)
        
        assert model.id.provider == Provider.WHISPER
        assert model.id.name == "tiny"
        assert model.status == ModelStatus.INSTALLED
        
        mock_install.assert_called_once_with("tiny")
    
    def test_install_ollama_model(self, install_service, ollama_provider):
        """Test installing Ollama model."""
        expected_model = Model(
            id=ModelId(provider=Provider.OLLAMA, name="llama3", version="8b"),
            status=ModelStatus.INSTALLED,
            metadata=Mock()
        )
        
        with patch.object(ollama_provider, 'install', return_value=expected_model) as mock_install:
            model = install_service.install(Provider.OLLAMA, "llama3", "8b")
        
        assert model.id.provider == Provider.OLLAMA
        assert model.id.name == "llama3"
        assert model.id.version == "8b"
        
        mock_install.assert_called_once_with("llama3", "8b")
    
    def test_install_invalid_provider(self, install_service):
        """Test installing model with invalid provider."""
        with pytest.raises(ValueError) as exc_info:
            install_service.install("INVALID", "model", None)
        
        assert "Unknown provider" in str(exc_info.value)
    
    def test_uninstall_whisper_model(self, install_service, whisper_provider):
        """Test uninstalling Whisper model."""
        with patch.object(whisper_provider, 'uninstall') as mock_uninstall:
            install_service.uninstall(Provider.WHISPER, "tiny", None)
        
        mock_uninstall.assert_called_once_with("tiny")
    
    def test_uninstall_ollama_model(self, install_service, ollama_provider):
        """Test uninstalling Ollama model."""
        with patch.object(ollama_provider, 'uninstall') as mock_uninstall:
            install_service.uninstall(Provider.OLLAMA, "llama3", "8b")
        
        mock_uninstall.assert_called_once_with("llama3:8b")
    
    def test_install_provider_error_propagation(self, install_service, whisper_provider):
        """Test that provider errors are properly propagated."""
        with patch.object(whisper_provider, 'install', side_effect=NotFoundError(
            message="Model not found",
            provider="WHISPER",
            operation="INSTALL",
            cause="CATALOG"
        )):
            
            with pytest.raises(NotFoundError) as exc_info:
                install_service.install(Provider.WHISPER, "nonexistent", None)
            
            assert "Model not found" in str(exc_info.value)
            assert exc_info.value.provider == "WHISPER"


class TestConfigServiceIntegration:
    """Test ConfigService integration with providers."""
    
    @pytest.fixture
    def config_service(self, whisper_provider, ollama_provider):
        """Create config service with mock providers."""
        return ConfigService(whisper_provider, ollama_provider)
    
    def test_configure_whisper_model(self, config_service, whisper_provider):
        """Test configuring Whisper model."""
        config = {
            "device": "cuda",
            "language": "en",
            "temperature": 0.7
        }
        
        with patch.object(whisper_provider, 'configure') as mock_configure:
            config_service.configure(Provider.WHISPER, "tiny", config)
        
        mock_configure.assert_called_once_with("tiny", config)
    
    def test_configure_ollama_model(self, config_service, ollama_provider):
        """Test configuring Ollama model."""
        config = {
            "temperature": 0.8,
            "top_p": 0.9,
            "max_tokens": 2048
        }
        
        with patch.object(ollama_provider, 'configure') as mock_configure:
            config_service.configure(Provider.OLLAMA, "llama3", config)
        
        mock_configure.assert_called_once_with("llama3", config)
    
    def test_configure_invalid_provider(self, config_service):
        """Test configuring model with invalid provider."""
        with pytest.raises(ValueError) as exc_info:
            config_service.configure("INVALID", "model", {})
        
        assert "Unknown provider" in str(exc_info.value)
    
    def test_configure_validation_error_propagation(self, config_service, whisper_provider):
        """Test that validation errors are properly propagated."""
        with patch.object(whisper_provider, 'configure', side_effect=ValidationError(
            message="Invalid temperature",
            provider="WHISPER",
            operation="CONFIGURE",
            cause="INVALID_TEMPERATURE"
        )):
            
            with pytest.raises(ValidationError) as exc_info:
                config_service.configure(Provider.WHISPER, "tiny", {"temperature": 2.0})
            
            assert "Invalid temperature" in str(exc_info.value)
            assert exc_info.value.cause == "INVALID_TEMPERATURE"


class TestServiceErrorHandling:
    """Test error handling across services."""
    
    def test_service_coordination_error_propagation(self, whisper_provider, ollama_provider, mock_filesystem):
        """Test that errors propagate correctly through service layers."""
        catalog_service = CatalogService(whisper_provider, ollama_provider, mock_filesystem)
        
        # Test provider error propagation
        with patch.object(whisper_provider, 'get_catalog', side_effect=ProviderError(
            message="Network timeout",
            provider="WHISPER",
            operation="CATALOG",
            cause="TIMEOUT"
        )):
            
            with pytest.raises(ProviderError) as exc_info:
                catalog_service.list_available(Provider.WHISPER, 1, 50, None)
            
            assert exc_info.value.message == "Network timeout"
            assert exc_info.value.provider == "WHISPER"
            assert exc_info.value.operation == "CATALOG"
            assert exc_info.value.cause == "TIMEOUT"
    
    def test_service_partial_failure_handling(self, whisper_provider, ollama_provider, mock_filesystem):
        """Test handling when one provider fails but others succeed."""
        catalog_service = CatalogService(whisper_provider, ollama_provider, mock_filesystem)
        
        # Mock successful Whisper response
        whisper_models = [
            AvailableModel(provider=Provider.WHISPER, name="tiny", metadata={})
        ]
        
        with patch.object(whisper_provider, 'get_catalog', return_value=whisper_models), \
             patch.object(ollama_provider, 'get_catalog', side_effect=Exception("Network error")):
            
            # When not filtering by provider, should handle partial failure gracefully
            result = catalog_service.list_available(None, 1, 50, None)
            
            # Should still return Whisper models even if Ollama fails
            assert result.total == 1
            assert result.items[0].provider == Provider.WHISPER
