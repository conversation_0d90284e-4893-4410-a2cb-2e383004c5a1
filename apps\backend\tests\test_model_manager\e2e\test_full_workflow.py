"""
End-to-end tests for model manager complete workflows.
"""

import pytest
import asyncio
import tempfile
import shutil
from unittest.mock import Mock, patch, AsyncMock
from fastapi.testclient import TestClient
from fastapi import FastAPI

from app.model_manager.app.router import router as model_router
from app.model_manager.domain.entities import Provider, ModelStatus


@pytest.fixture
def app():
    """Create FastAPI app for E2E testing."""
    app = FastAPI()
    app.include_router(model_router, prefix="/models")
    return app


@pytest.fixture
def client(app):
    """Create test client for E2E testing."""
    return TestClient(app)


@pytest.fixture
def temp_data_dir():
    """Create temporary data directory for E2E tests."""
    temp_dir = tempfile.mkdtemp()
    yield temp_dir
    shutil.rmtree(temp_dir, ignore_errors=True)


class TestCompleteModelLifecycle:
    """Test complete model lifecycle from catalog to deletion."""
    
    @pytest.mark.asyncio
    async def test_whisper_model_full_lifecycle(self, client, temp_data_dir):
        """Test complete Whisper model lifecycle."""
        with patch('app.model_manager.app.controllers.model_controller._cfg') as mock_cfg, \
             patch('app.model_manager.app.controllers.model_controller._fs') as mock_fs, \
             patch('app.model_manager.app.controllers.model_controller._proc') as mock_proc, \
             patch('app.model_manager.app.controllers.model_controller._cache') as mock_cache:
            
            # Setup mocks
            self._setup_whisper_mocks(mock_cfg, mock_fs, mock_proc, mock_cache, temp_data_dir)
            
            # 1. Check health
            health_response = client.get("/models/health")
            assert health_response.status_code == 200
            assert health_response.json()["whisper_cli"] is True
            
            # 2. List available models
            available_response = client.get("/models/available?provider=WHISPER")
            assert available_response.status_code == 200
            available_data = available_response.json()
            assert len(available_data["items"]) > 0
            tiny_model = next(m for m in available_data["items"] if m["name"] == "tiny")
            assert tiny_model["provider"] == "WHISPER"
            
            # 3. Install model
            install_response = client.post("/models/install", json={
                "provider": "WHISPER",
                "name": "tiny",
                "version": None
            })
            assert install_response.status_code == 200
            install_data = install_response.json()
            assert install_data["status"] == "INSTALLED"
            
            # 4. Verify installation
            installed_response = client.get("/models/installed")
            assert installed_response.status_code == 200
            installed_data = installed_response.json()
            installed_models = [m for m in installed_data["items"] if m["id"]["name"] == "tiny"]
            assert len(installed_models) == 1
            assert installed_models[0]["status"] == "INSTALLED"
            
            # 5. Configure model
            config_response = client.post("/models/configure", json={
                "provider": "WHISPER",
                "name": "tiny",
                "config": {
                    "device": "cpu",
                    "language": "en",
                    "temperature": 0.7
                }
            })
            assert config_response.status_code == 200
            assert config_response.json()["ok"] is True
            
            # 6. Delete model
            delete_response = client.delete("/models/WHISPER/tiny")
            assert delete_response.status_code == 200
            assert delete_response.json()["ok"] is True
            
            # 7. Verify deletion
            final_installed_response = client.get("/models/installed")
            assert final_installed_response.status_code == 200
            final_installed_data = final_installed_response.json()
            remaining_models = [m for m in final_installed_data["items"] if m["id"]["name"] == "tiny"]
            assert len(remaining_models) == 0
    
    @pytest.mark.asyncio
    async def test_ollama_model_full_lifecycle(self, client, temp_data_dir):
        """Test complete Ollama model lifecycle."""
        with patch('app.model_manager.app.controllers.model_controller._cfg') as mock_cfg, \
             patch('app.model_manager.app.controllers.model_controller._fs') as mock_fs, \
             patch('app.model_manager.app.controllers.model_controller._proc') as mock_proc, \
             patch('app.model_manager.app.controllers.model_controller._cache') as mock_cache:
            
            # Setup mocks
            self._setup_ollama_mocks(mock_cfg, mock_fs, mock_proc, mock_cache, temp_data_dir)
            
            # 1. Check health
            health_response = client.get("/models/health")
            assert health_response.status_code == 200
            assert health_response.json()["ollama_cli"] is True
            
            # 2. List available models
            available_response = client.get("/models/available?provider=OLLAMA")
            assert available_response.status_code == 200
            available_data = available_response.json()
            assert len(available_data["items"]) > 0
            
            # 3. Install model with version
            install_response = client.post("/models/install", json={
                "provider": "OLLAMA",
                "name": "llama3",
                "version": "8b"
            })
            assert install_response.status_code == 200
            install_data = install_response.json()
            assert install_data["status"] == "INSTALLED"
            
            # 4. Configure model
            config_response = client.post("/models/configure", json={
                "provider": "OLLAMA",
                "name": "llama3",
                "config": {
                    "temperature": 0.8,
                    "top_p": 0.9,
                    "max_tokens": 2048
                }
            })
            assert config_response.status_code == 200
            
            # 5. Delete model with version
            delete_response = client.delete("/models/OLLAMA/llama3?version=8b")
            assert delete_response.status_code == 200
    
    def _setup_whisper_mocks(self, mock_cfg, mock_fs, mock_proc, mock_cache, temp_dir):
        """Setup mocks for Whisper testing."""
        # Config mock
        mock_cfg.paths.data_root = temp_dir
        mock_cfg.providers.whisper_enabled = True
        mock_cfg.providers.ollama_enabled = False
        mock_cfg.whisper.use_cli = True
        mock_cfg.whisper.executable = "whisper"
        mock_cfg.whisper.models = [
            {"name": "tiny", "size": "39 MB", "multilingual": True}
        ]
        
        # Filesystem mock
        mock_fs.exists.return_value = False  # No existing installation
        mock_fs.acquire_lock.return_value = True
        mock_fs.ensure_dir.return_value = None
        mock_fs.size_on_disk.return_value = 40960000
        mock_fs.write_json_atomic.return_value = None
        mock_fs.remove_dir_safe.return_value = None
        mock_fs.release_lock.return_value = None
        
        # Process mock
        mock_proc.which.return_value = "/usr/bin/whisper"
        result = Mock()
        result.returncode = 0
        result.exit_code = 0
        result.stdout = "Success"
        result.stderr = ""
        mock_proc.run.return_value = result
        
        # Cache mock
        mock_cache.get.return_value = None
        mock_cache.set.return_value = None
    
    def _setup_ollama_mocks(self, mock_cfg, mock_fs, mock_proc, mock_cache, temp_dir):
        """Setup mocks for Ollama testing."""
        # Config mock
        mock_cfg.paths.data_root = temp_dir
        mock_cfg.providers.whisper_enabled = False
        mock_cfg.providers.ollama_enabled = True
        mock_cfg.ollama.executable = "ollama"
        mock_cfg.ollama.catalog_source = "cli"
        mock_cfg.ollama.catalog_cache_key = "ollama_catalog"
        
        # Filesystem mock
        mock_fs.exists.return_value = False
        mock_fs.acquire_lock.return_value = True
        mock_fs.ensure_dir.return_value = None
        mock_fs.size_on_disk.return_value = **********
        mock_fs.write_json_atomic.return_value = None
        mock_fs.remove_dir_safe.return_value = None
        mock_fs.release_lock.return_value = None
        
        # Process mock
        mock_proc.which.return_value = "/usr/bin/ollama"
        
        def mock_run_side_effect(cmd, **kwargs):
            result = Mock()
            result.exit_code = 0
            result.stderr = ""
            
            if "list" in cmd:
                result.stdout = "llama3:8b\t4.7GB\t2 hours ago\n"
            else:
                result.stdout = "Success"
            
            return result
        
        mock_proc.run.side_effect = mock_run_side_effect
        
        # Cache mock
        mock_cache.get.return_value = None
        mock_cache.set.return_value = None


class TestErrorScenarios:
    """Test error scenarios and edge cases."""
    
    def test_install_with_insufficient_disk_space(self, client):
        """Test installation failure due to insufficient disk space."""
        with patch('app.model_manager.app.controllers.model_controller._install_service') as mock_service:
            from app.model_manager.utils.errors import ProviderError
            mock_service.install.side_effect = ProviderError(
                message="Insufficient disk space",
                provider="WHISPER",
                operation="INSTALL",
                cause="DISK_FULL"
            )
            
            response = client.post("/models/install", json={
                "provider": "WHISPER",
                "name": "large"
            })
            
            assert response.status_code == 400
            data = response.json()
            assert "Insufficient disk space" in data["detail"]["message"]
    
    def test_concurrent_installation_attempts(self, client):
        """Test handling of concurrent installation attempts."""
        with patch('app.model_manager.app.controllers.model_controller._install_service') as mock_service:
            from app.model_manager.utils.errors import ProviderError
            mock_service.install.side_effect = ProviderError(
                message="Model is locked (install in progress)",
                provider="WHISPER",
                operation="INSTALL",
                cause="LOCKED"
            )
            
            response = client.post("/models/install", json={
                "provider": "WHISPER",
                "name": "tiny"
            })
            
            assert response.status_code == 400
            data = response.json()
            assert "Model is locked" in data["detail"]["message"]
    
    def test_configuration_validation_errors(self, client):
        """Test configuration validation error handling."""
        with patch('app.model_manager.app.controllers.model_controller._config_service') as mock_service:
            from app.model_manager.utils.errors import ValidationError
            mock_service.configure.side_effect = ValidationError(
                message="Invalid temperature value",
                provider="WHISPER",
                operation="CONFIGURE",
                cause="INVALID_TEMPERATURE"
            )
            
            response = client.post("/models/configure", json={
                "provider": "WHISPER",
                "name": "tiny",
                "config": {"temperature": 2.0}
            })
            
            assert response.status_code == 400
            data = response.json()
            assert "Invalid temperature value" in data["detail"]["message"]
    
    def test_delete_nonexistent_model(self, client):
        """Test deletion of non-existent model."""
        with patch('app.model_manager.app.controllers.model_controller._install_service') as mock_service:
            from app.model_manager.utils.errors import NotFoundError
            mock_service.uninstall.side_effect = NotFoundError(
                message="Model not found",
                provider="WHISPER",
                operation="DELETE",
                cause="NOT_FOUND"
            )
            
            response = client.delete("/models/WHISPER/nonexistent")
            
            assert response.status_code == 400
            data = response.json()
            assert "Model not found" in data["detail"]["message"]


class TestPerformanceScenarios:
    """Test performance-related scenarios."""
    
    def test_large_catalog_pagination(self, client):
        """Test handling of large catalogs with pagination."""
        with patch('app.model_manager.app.controllers.model_controller._catalog_service') as mock_service:
            from app.model_manager.domain.entities import PagedResult, AvailableModel
            
            # Mock large catalog
            models = [
                AvailableModel(
                    provider=Provider.OLLAMA,
                    name=f"model-{i}",
                    version="latest",
                    metadata={"size_bytes": 1000000 * i}
                )
                for i in range(1000)
            ]
            
            mock_service.list_available.return_value = PagedResult(
                items=models[:50],  # First page
                total=1000,
                page=1,
                page_size=50
            )
            
            response = client.get("/models/available?page=1&page_size=50")
            
            assert response.status_code == 200
            data = response.json()
            assert data["total"] == 1000
            assert len(data["items"]) == 50
            assert data["page"] == 1
            assert data["page_size"] == 50
    
    def test_concurrent_api_requests(self, client):
        """Test handling of concurrent API requests."""
        import threading
        import queue
        
        with patch('app.model_manager.app.controllers.model_controller._inventory_service') as mock_service:
            mock_service.list_installed.return_value = []
            
            results = queue.Queue()
            
            def make_request():
                try:
                    response = client.get("/models/installed")
                    results.put(response.status_code)
                except Exception as e:
                    results.put(str(e))
            
            # Create 10 concurrent requests
            threads = []
            for _ in range(10):
                thread = threading.Thread(target=make_request)
                threads.append(thread)
                thread.start()
            
            # Wait for all threads to complete
            for thread in threads:
                thread.join()
            
            # Check all requests succeeded
            status_codes = []
            while not results.empty():
                status_codes.append(results.get())
            
            assert len(status_codes) == 10
            assert all(code == 200 for code in status_codes)
    
    def test_memory_usage_monitoring(self, client):
        """Test memory usage monitoring endpoint."""
        with patch('app.model_manager.app.controllers.model_controller._fs') as mock_fs, \
             patch('app.model_manager.app.controllers.model_controller._cache') as mock_cache:
            
            mock_fs.size_on_disk.side_effect = lambda path: {
                "/tmp/models": 10737418240,      # 10GB total
                "/tmp/models/whisper": **********,  # 5GB Whisper
                "/tmp/models/ollama": **********    # 5GB Ollama
            }.get(path, 0)
            
            mock_cache.stats.return_value = {
                "hit_count": 1000,
                "miss_count": 100,
                "total_size": 104857600  # 100MB cache
            }
            
            response = client.get("/models/diagnostics")
            
            assert response.status_code == 200
            data = response.json()
            
            # Check large file handling
            assert data["models"]["total_bytes"] == 10737418240
            assert data["models"]["whisper_bytes"] == **********
            assert data["models"]["ollama_bytes"] == **********
            
            # Check cache efficiency
            assert data["cache"]["hit_count"] == 1000
            assert data["cache"]["miss_count"] == 100


class TestSecurityScenarios:
    """Test security-related scenarios."""
    
    def test_path_traversal_protection(self, client):
        """Test protection against path traversal attacks."""
        # Test with path traversal in model name
        response = client.post("/models/install", json={
            "provider": "WHISPER",
            "name": "../../../etc/passwd"
        })
        
        # Should be caught by validation
        assert response.status_code == 400 or response.status_code == 422
    
    def test_malicious_configuration_injection(self, client):
        """Test protection against malicious configuration injection."""
        malicious_config = {
            "__proto__": {"isAdmin": True},
            "constructor": {"prototype": {"isAdmin": True}},
            "device": "cpu",
            "command": "rm -rf /"
        }
        
        with patch('app.model_manager.app.controllers.model_controller._config_service') as mock_service:
            # Should validate and sanitize input
            mock_service.configure.return_value = None
            
            response = client.post("/models/configure", json={
                "provider": "WHISPER",
                "name": "tiny",
                "config": malicious_config
            })
            
            # Should either succeed with sanitized config or fail validation
            assert response.status_code in [200, 400, 422]
    
    def test_resource_exhaustion_protection(self, client):
        """Test protection against resource exhaustion attacks."""
        # Test with extremely large request
        large_config = {f"key_{i}": "value" * 1000 for i in range(1000)}
        
        response = client.post("/models/configure", json={
            "provider": "WHISPER",
            "name": "tiny",
            "config": large_config
        })
        
        # Should handle large payloads gracefully
        assert response.status_code in [200, 400, 413, 422]
