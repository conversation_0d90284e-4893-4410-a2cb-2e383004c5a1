"""
System exception classes for the LONI backend application.
"""

from typing import Any, Dict, Optional

from .base import AppException


class DatabaseError(AppException):
    """
    Exception raised when database operations fail.
    
    Used for database connection errors, query failures,
    and other database-related issues.
    """
    
    def __init__(
        self,
        message: str = "Database operation failed",
        operation: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        """Initialize database exception."""
        if operation and not details:
            details = {"operation": operation}
        elif operation and details:
            details["operation"] = operation
            
        super().__init__(
            message=message,
            error_code="DATABASE_ERROR",
            details=details,
            status_code=500
        )


class ExternalServiceError(AppException):
    """
    Exception raised when external service calls fail.
    
    Used for API calls to third-party services, Redis connections,
    and other external service issues.
    """
    
    def __init__(
        self,
        message: str = "External service error",
        service_name: Optional[str] = None,
        service_error: Optional[str] = None
    ):
        """Initialize external service exception."""
        details = {}
        if service_name:
            details["service_name"] = service_name
        if service_error:
            details["service_error"] = service_error
            
        super().__init__(
            message=message,
            error_code="EXTERNAL_SERVICE_ERROR",
            details=details,
            status_code=503
        )