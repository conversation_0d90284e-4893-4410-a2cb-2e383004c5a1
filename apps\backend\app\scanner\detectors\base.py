"""
Base detector class for all issue detection algorithms.

Provides common functionality and interface for all detectors.
"""

import uuid
from abc import ABC, abstractmethod
from typing import List

from ..models import Issue, IssueType, IssueSeverity


class BaseDetector(ABC):
    """
    Abstract base class for all issue detectors.
    
    Provides common functionality and defines the interface that all 
    detectors must implement.
    """
    
    def __init__(self, issue_type: IssueType):
        """
        Initialize the detector.
        
        Args:
            issue_type: The type of issues this detector finds
        """
        self.issue_type = issue_type
    
    @abstractmethod
    def detect_issues(self, file_path: str, content: str) -> List[Issue]:
        """
        Detect issues in the given file content.
        
        Args:
            file_path: Path to the file being analyzed
            content: Content of the file
            
        Returns:
            List of detected issues
        """
        pass
    
    def create_issue(
        self,
        file_path: str,
        title: str,
        description: str,
        severity: IssueSeverity,
        line_number: int = None,
        column_number: int = None,
        rule_id: str = None,
        suggestion: str = None,
        metadata: dict = None
    ) -> Issue:
        """
        Create an Issue object with common properties.
        
        Args:
            file_path: Path to the file where issue was found
            title: Short description of the issue
            description: Detailed description of the issue
            severity: Severity level of the issue
            line_number: Line number where issue occurs
            column_number: Column number where issue occurs
            rule_id: ID of the rule that detected this issue
            suggestion: Suggested fix for the issue
            metadata: Additional metadata
            
        Returns:
            Issue object
        """
        return Issue(
            id=str(uuid.uuid4()),
            type=self.issue_type,
            severity=severity,
            title=title,
            description=description,
            file_path=file_path,
            line_number=line_number,
            column_number=column_number,
            rule_id=rule_id,
            suggestion=suggestion,
            metadata=metadata or {}
        )
    
    def get_file_extension(self, file_path: str) -> str:
        """
        Get the file extension.
        
        Args:
            file_path: Path to the file
            
        Returns:
            File extension (e.g., '.py', '.js')
        """
        return file_path.split('.')[-1].lower() if '.' in file_path else ''
    
    def count_lines(self, content: str) -> int:
        """
        Count the number of lines in the content.
        
        Args:
            content: File content
            
        Returns:
            Number of lines
        """
        return len(content.splitlines())
    
    def is_supported_file(self, file_path: str) -> bool:
        """
        Check if the file type is supported by this detector.
        
        Args:
            file_path: Path to the file
            
        Returns:
            True if file is supported, False otherwise
        """
        # Base implementation supports all files
        # Subclasses can override to be more specific
        return True