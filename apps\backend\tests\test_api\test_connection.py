"""
Tests for connection endpoints.
"""

import pytest
from fastapi.testclient import <PERSON><PERSON><PERSON>
from sqlalchemy.ext.asyncio import AsyncSession
from app.main import create_app
from app.models.user import User
from app.models.canvas import Can<PERSON>, Node, Connection


@pytest.fixture
def client() -> TestClient:
    """Create a test client for the FastAPI app."""
    app = create_app()
    return TestClient(app)


@pytest.fixture
def auth_headers(client: TestClient, test_user: User) -> dict:
    """Get authentication headers for a test user."""
    # Login to get a token
    response = client.post("/api/v1/auth/login", data={
        "username": "<EMAIL>",
        "password": "password123"
    })
    
    assert response.status_code == 200
    token_data = response.json()
    access_token = token_data["access_token"]
    
    return {"Authorization": f"Bearer {access_token}"}


@pytest.mark.asyncio
async def test_create_connection(client: TestClient, auth_headers: dict, test_canvas: Canvas, test_node: Node):
    """Test create connection endpoint."""
    # Create a second node for the connection
    node_response = client.post(f"/api/v1/canvas/{test_canvas.id}/nodes", json={
        "type": "logic",
        "title": "Second Node",
        "description": "Another test node",
        "position": {"x": 300, "y": 100},
        "size": {"width": 150, "height": 100}
    }, headers=auth_headers)
    
    assert node_response.status_code == 200
    second_node = node_response.json()
    
    # Create connection between nodes
    response = client.post(f"/api/v1/canvas/{test_canvas.id}/connections", json={
        "source_node_id": str(test_node.id),
        "target_node_id": second_node["id"],
        "connection_type": "default",
        "label": "Test Connection"
    }, headers=auth_headers)
    
    assert response.status_code == 200
    data = response.json()
    assert data["source_node_id"] == str(test_node.id)
    assert data["target_node_id"] == second_node["id"]
    assert data["connection_type"] == "default"
    assert data["label"] == "Test Connection"
    assert "id" in data


@pytest.mark.asyncio
async def test_delete_connection(client: TestClient, auth_headers: dict, test_connection: Connection):
    """Test delete connection endpoint."""
    response = client.delete(f"/api/v1/canvas/connections/{test_connection.id}", headers=auth_headers)
    
    assert response.status_code == 200
    data = response.json()
    assert data["message"] == "Connection deleted successfully"
    
    # Verify connection is deleted
    # This would require a get connection endpoint, but we can test by trying to delete it again
    delete_response = client.delete(f"/api/v1/canvas/connections/{test_connection.id}", headers=auth_headers)
    assert delete_response.status_code == 404