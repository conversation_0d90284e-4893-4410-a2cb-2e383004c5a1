"""Caching mechanism for Ollama model operations."""
import time
from typing import Dict, List, Any, Optional


class ModelCache:
    """Cache for model information and operations."""

    def __init__(self, ttl: int = 300):
        """Initialize the model cache.

        Args:
            ttl: Time-to-live for cached data in seconds (default: 5 minutes)
        """
        self._available_models: List[str] = []
        self._model_cache: Dict[str, Dict[str, Any]] = {}
        self._last_cache_update: Optional[float] = None
        self._cache_ttl = ttl

    def is_cache_valid(self) -> bool:
        """Check if the model cache is still valid."""
        if self._last_cache_update is None:
            return False
        return (time.time() - self._last_cache_update) < self._cache_ttl

    def get_available_models(self) -> List[str]:
        """Get the list of available models from cache."""
        return self._available_models.copy()

    def set_available_models(self, models: List[str]) -> None:
        """Set the list of available models in cache."""
        self._available_models = models
        self._last_cache_update = time.time()

    def get_model_info(self, model_name: str) -> Optional[Dict[str, Any]]:
        """Get model info from cache."""
        return self._model_cache.get(model_name)

    def set_model_info(self, model_name: str, info: Dict[str, Any]) -> None:
        """Set model info in cache."""
        self._model_cache[model_name] = info

    def invalidate_cache(self) -> None:
        """Invalidate the model cache."""
        self._last_cache_update = None
        self._available_models = []

    def clear_cache(self) -> None:
        """Clear all cached data."""
        self.invalidate_cache()
        self._model_cache.clear()

    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        return {
            "model_count": len(self._available_models),
            "cached_models_info": len(self._model_cache),
            "last_update": self._last_cache_update,
            "cache_ttl": self._cache_ttl
        }
