#!/bin/bash
# Script to run frontend type checking with logging

# Set variables
PROJECT_ROOT="/mnt/e/Projects/lonors/loni"
FRONTEND_DIR="$PROJECT_ROOT/apps/frontend"
LOG_FILE="$PROJECT_ROOT/apps/data/logs/devtools-frontend-typecheck.log"

# Create log file if it doesn't exist
mkdir -p "$PROJECT_ROOT/apps/data/logs"
touch "$LOG_FILE"

# Function to log messages
log_message() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

# Start type checking execution
log_message "Starting frontend type checking execution"

# Change to frontend directory
cd "$FRONTEND_DIR" || { log_message "ERROR: Failed to change to frontend directory"; exit 1; }

# Run type checking with bun
log_message "Running type checking with bun..."
bun run type-check 2>&1 | tee -a "$LOG_FILE"

# Check exit status
if [ ${PIPESTATUS[0]} -eq 0 ]; then
    log_message "Frontend type checking completed successfully"
else
    log_message "ERROR: Frontend type checking failed"
    exit 1
fi