"""
Storage and retrieval system for scan results.

Provides persistence for:
- Scan results and metadata
- Issue details and history
- User scan preferences
- Analytics and reporting data
"""

import json
import sqlite3
from typing import List, Optional, Dict, Any
from datetime import datetime, timezone
from pathlib import Path

from ..logger import get_logger
from .models import ScanResponse, Issue, ScanSummary, IssueType, IssueSeverity

logger = get_logger("scanner_storage")


class ScanStorage:
    """
    Storage backend for scan results using SQLite.
    
    In production, this could be replaced with PostgreSQL, MongoDB, 
    or other database systems following the same interface.
    """
    
    def __init__(self, db_path: str = "data/scanner.db"):
        """
        Initialize the storage system.
        
        Args:
            db_path: Path to the SQLite database file
        """
        self.db_path = Path(db_path)
        self.db_path.parent.mkdir(exist_ok=True)
        
        self._init_database()
    
    def _init_database(self):
        """Initialize the database schema."""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # Create scans table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS scans (
                    scan_id TEXT PRIMARY KEY,
                    request_id TEXT NOT NULL,
                    user_id TEXT NOT NULL,
                    status TEXT NOT NULL,
                    created_at TIMESTAMP NOT NULL,
                    completed_at TIMESTAMP,
                    project_path TEXT NOT NULL,
                    scan_config TEXT NOT NULL,
                    summary_data TEXT,
                    errors TEXT,
                    INDEX(user_id),
                    INDEX(created_at),
                    INDEX(status)
                )
            """)
            
            # Create issues table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS issues (
                    id TEXT PRIMARY KEY,
                    scan_id TEXT NOT NULL,
                    issue_type TEXT NOT NULL,
                    severity TEXT NOT NULL,
                    title TEXT NOT NULL,
                    description TEXT NOT NULL,
                    file_path TEXT NOT NULL,
                    line_number INTEGER,
                    column_number INTEGER,
                    rule_id TEXT,
                    suggestion TEXT,
                    metadata TEXT,
                    FOREIGN KEY(scan_id) REFERENCES scans(scan_id),
                    INDEX(scan_id),
                    INDEX(issue_type),
                    INDEX(severity),
                    INDEX(file_path)
                )
            """)
            
            # Create scan analytics table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS scan_analytics (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    scan_id TEXT NOT NULL,
                    metric_name TEXT NOT NULL,
                    metric_value REAL NOT NULL,
                    recorded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY(scan_id) REFERENCES scans(scan_id),
                    INDEX(scan_id),
                    INDEX(metric_name)
                )
            """)
            
            # Create user preferences table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS user_preferences (
                    user_id TEXT PRIMARY KEY,
                    default_scan_config TEXT,
                    notification_settings TEXT,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            conn.commit()
            
        logger.info("Database initialized", db_path=str(self.db_path))
    
    async def store_scan_result(self, scan_result: ScanResponse) -> bool:
        """
        Store a complete scan result.
        
        Args:
            scan_result: Complete scan result to store
            
        Returns:
            True if stored successfully, False otherwise
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # Store scan metadata
                cursor.execute("""
                    INSERT OR REPLACE INTO scans (
                        scan_id, request_id, user_id, status, created_at, completed_at,
                        project_path, scan_config, summary_data, errors
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    scan_result.scan_id,
                    scan_result.request_id,
                    scan_result.user_id,
                    scan_result.status,
                    scan_result.created_at.isoformat(),
                    scan_result.completed_at.isoformat() if scan_result.completed_at else None,
                    scan_result.target.project_path,
                    json.dumps(scan_result.config.dict()),
                    json.dumps(scan_result.summary.dict()) if scan_result.summary else None,
                    json.dumps(scan_result.errors)
                ))
                
                # Store issues
                for issue in scan_result.issues:
                    cursor.execute("""
                        INSERT OR REPLACE INTO issues (
                            id, scan_id, issue_type, severity, title, description,
                            file_path, line_number, column_number, rule_id,
                            suggestion, metadata
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """, (
                        issue.id,
                        scan_result.scan_id,
                        issue.type.value,
                        issue.severity.value,
                        issue.title,
                        issue.description,
                        issue.file_path,
                        issue.line_number,
                        issue.column_number,
                        issue.rule_id,
                        issue.suggestion,
                        json.dumps(issue.metadata)
                    ))
                
                # Store analytics if summary exists
                if scan_result.summary:
                    await self._store_scan_analytics(cursor, scan_result.scan_id, scan_result.summary)
                
                conn.commit()
                
            logger.info(
                "Scan result stored",
                scan_id=scan_result.scan_id,
                user_id=scan_result.user_id,
                issues_count=len(scan_result.issues)
            )
            return True
            
        except Exception as e:
            logger.error(
                "Error storing scan result",
                scan_id=scan_result.scan_id,
                error=str(e),
                error_type=type(e).__name__
            )
            return False
    
    async def get_scan_result(self, scan_id: str, user_id: str) -> Optional[ScanResponse]:
        """
        Retrieve a scan result by ID.
        
        Args:
            scan_id: Unique scan identifier
            user_id: User ID for access control
            
        Returns:
            ScanResponse if found and accessible, None otherwise
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # Get scan metadata
                cursor.execute("""
                    SELECT scan_id, request_id, user_id, status, created_at, completed_at,
                           project_path, scan_config, summary_data, errors
                    FROM scans 
                    WHERE scan_id = ? AND user_id = ?
                """, (scan_id, user_id))
                
                scan_row = cursor.fetchone()
                if not scan_row:
                    return None
                
                # Parse scan data
                (scan_id, request_id, user_id, status, created_at, completed_at,
                 project_path, scan_config_json, summary_json, errors_json) = scan_row
                
                # Get issues
                cursor.execute("""
                    SELECT id, issue_type, severity, title, description, file_path,
                           line_number, column_number, rule_id, suggestion, metadata
                    FROM issues 
                    WHERE scan_id = ?
                    ORDER BY severity DESC, file_path, line_number
                """, (scan_id,))
                
                issues = []
                for issue_row in cursor.fetchall():
                    (issue_id, issue_type, severity, title, description, file_path,
                     line_number, column_number, rule_id, suggestion, metadata_json) = issue_row
                    
                    issues.append(Issue(
                        id=issue_id,
                        type=IssueType(issue_type),
                        severity=IssueSeverity(severity),
                        title=title,
                        description=description,
                        file_path=file_path,
                        line_number=line_number,
                        column_number=column_number,
                        rule_id=rule_id,
                        suggestion=suggestion,
                        metadata=json.loads(metadata_json) if metadata_json else {}
                    ))
                
                # Reconstruct scan response (simplified - would need to recreate full objects)
                scan_result = {
                    "scan_id": scan_id,
                    "request_id": request_id,
                    "user_id": user_id,
                    "status": status,
                    "created_at": datetime.fromisoformat(created_at),
                    "completed_at": datetime.fromisoformat(completed_at) if completed_at else None,
                    "issues": issues,
                    "errors": json.loads(errors_json) if errors_json else []
                }
                
                return scan_result
                
        except Exception as e:
            logger.error(
                "Error retrieving scan result",
                scan_id=scan_id,
                user_id=user_id,
                error=str(e),
                error_type=type(e).__name__
            )
            return None
    
    async def list_user_scans(
        self, 
        user_id: str, 
        limit: int = 50, 
        offset: int = 0,
        status_filter: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """
        List scans for a user with pagination.
        
        Args:
            user_id: User identifier
            limit: Maximum number of results
            offset: Number of results to skip
            status_filter: Optional status filter
            
        Returns:
            List of scan summaries
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # Build query
                query = """
                    SELECT scan_id, request_id, status, created_at, completed_at,
                           project_path, summary_data
                    FROM scans 
                    WHERE user_id = ?
                """
                params = [user_id]
                
                if status_filter:
                    query += " AND status = ?"
                    params.append(status_filter)
                
                query += " ORDER BY created_at DESC LIMIT ? OFFSET ?"
                params.extend([limit, offset])
                
                cursor.execute(query, params)
                
                scans = []
                for row in cursor.fetchall():
                    (scan_id, request_id, status, created_at, completed_at,
                     project_path, summary_json) = row
                    
                    scan_summary = {
                        "scan_id": scan_id,
                        "request_id": request_id,
                        "status": status,
                        "created_at": created_at,
                        "completed_at": completed_at,
                        "project_path": project_path,
                        "summary": json.loads(summary_json) if summary_json else None
                    }
                    scans.append(scan_summary)
                
                return scans
                
        except Exception as e:
            logger.error(
                "Error listing user scans",
                user_id=user_id,
                error=str(e),
                error_type=type(e).__name__
            )
            return []
    
    async def get_scan_statistics(self, user_id: str) -> Dict[str, Any]:
        """
        Get scan statistics for a user.
        
        Args:
            user_id: User identifier
            
        Returns:
            Dictionary with scan statistics
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # Basic scan counts
                cursor.execute("""
                    SELECT status, COUNT(*)
                    FROM scans 
                    WHERE user_id = ?
                    GROUP BY status
                """, (user_id,))
                
                status_counts = dict(cursor.fetchall())
                
                # Issue statistics
                cursor.execute("""
                    SELECT i.issue_type, COUNT(*)
                    FROM issues i
                    JOIN scans s ON i.scan_id = s.scan_id
                    WHERE s.user_id = ?
                    GROUP BY i.issue_type
                """, (user_id,))
                
                issue_type_counts = dict(cursor.fetchall())
                
                cursor.execute("""
                    SELECT i.severity, COUNT(*)
                    FROM issues i
                    JOIN scans s ON i.scan_id = s.scan_id
                    WHERE s.user_id = ?
                    GROUP BY i.severity
                """, (user_id,))
                
                severity_counts = dict(cursor.fetchall())
                
                # Recent activity
                cursor.execute("""
                    SELECT COUNT(*)
                    FROM scans 
                    WHERE user_id = ? AND created_at >= datetime('now', '-7 days')
                """, (user_id,))
                
                recent_scans = cursor.fetchone()[0]
                
                return {
                    "total_scans": sum(status_counts.values()),
                    "status_counts": status_counts,
                    "issue_type_counts": issue_type_counts,
                    "severity_counts": severity_counts,
                    "recent_scans_7_days": recent_scans
                }
                
        except Exception as e:
            logger.error(
                "Error getting scan statistics",
                user_id=user_id,
                error=str(e),
                error_type=type(e).__name__
            )
            return {}
    
    async def _store_scan_analytics(self, cursor, scan_id: str, summary: ScanSummary):
        """Store analytics metrics for a scan."""
        metrics = [
            ("total_files_scanned", summary.total_files_scanned),
            ("total_issues_found", summary.total_issues_found),
            ("scan_duration_seconds", summary.scan_duration_seconds),
            ("files_with_issues", summary.files_with_issues),
            ("average_issues_per_file", summary.average_issues_per_file),
        ]
        
        for metric_name, metric_value in metrics:
            cursor.execute("""
                INSERT INTO scan_analytics (scan_id, metric_name, metric_value)
                VALUES (?, ?, ?)
            """, (scan_id, metric_name, metric_value))
    
    async def delete_scan(self, scan_id: str, user_id: str) -> bool:
        """
        Delete a scan and all associated data.
        
        Args:
            scan_id: Scan identifier
            user_id: User identifier for access control
            
        Returns:
            True if deleted successfully, False otherwise
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # Verify ownership
                cursor.execute("""
                    SELECT COUNT(*) FROM scans 
                    WHERE scan_id = ? AND user_id = ?
                """, (scan_id, user_id))
                
                if cursor.fetchone()[0] == 0:
                    return False
                
                # Delete in proper order (foreign key constraints)
                cursor.execute("DELETE FROM issues WHERE scan_id = ?", (scan_id,))
                cursor.execute("DELETE FROM scan_analytics WHERE scan_id = ?", (scan_id,))
                cursor.execute("DELETE FROM scans WHERE scan_id = ?", (scan_id,))
                
                conn.commit()
                
            logger.info("Scan deleted", scan_id=scan_id, user_id=user_id)
            return True
            
        except Exception as e:
            logger.error(
                "Error deleting scan",
                scan_id=scan_id,
                user_id=user_id,
                error=str(e),
                error_type=type(e).__name__
            )
            return False
    
    async def cleanup_old_scans(self, days_old: int = 30) -> int:
        """
        Clean up scans older than specified days.
        
        Args:
            days_old: Number of days old for cleanup threshold
            
        Returns:
            Number of scans cleaned up
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # Get old scan IDs
                cursor.execute("""
                    SELECT scan_id FROM scans 
                    WHERE created_at < datetime('now', '-{} days')
                """.format(days_old))
                
                old_scan_ids = [row[0] for row in cursor.fetchall()]
                
                if not old_scan_ids:
                    return 0
                
                # Delete old scans and related data
                placeholders = ','.join('?' * len(old_scan_ids))
                
                cursor.execute(f"DELETE FROM issues WHERE scan_id IN ({placeholders})", old_scan_ids)
                cursor.execute(f"DELETE FROM scan_analytics WHERE scan_id IN ({placeholders})", old_scan_ids)
                cursor.execute(f"DELETE FROM scans WHERE scan_id IN ({placeholders})", old_scan_ids)
                
                conn.commit()
                
            logger.info("Old scans cleaned up", count=len(old_scan_ids), days_old=days_old)
            return len(old_scan_ids)
            
        except Exception as e:
            logger.error(
                "Error cleaning up old scans",
                days_old=days_old,
                error=str(e),
                error_type=type(e).__name__
            )
            return 0


# Global storage instance
storage = ScanStorage()