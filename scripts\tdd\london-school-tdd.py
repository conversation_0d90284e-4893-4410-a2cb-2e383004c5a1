#!/usr/bin/env python3
"""
London School TDD Implementation
Focuses on behavior verification and mock-driven development
"""

import ast
import os
import re
import subprocess
import sys
from dataclasses import dataclass
from pathlib import Path
from typing import Dict, List, Optional, Set, Tuple
import json


@dataclass
class Collaborator:
    """Represents a collaborator/dependency in the system"""
    name: str
    interface: List[str]  # Method names
    file_path: str
    is_external: bool = False


@dataclass
class MockContract:
    """Represents a mock contract between objects"""
    collaborator: Collaborator
    interactions: List[Dict[str, any]]  # Expected method calls
    return_values: Dict[str, any]
    side_effects: Dict[str, any]


@dataclass
class BehaviorSpec:
    """Specification for object behavior"""
    subject_class: str
    collaborators: List[Collaborator]
    scenarios: List[Dict[str, any]]
    contracts: List[MockContract]


class CollaboratorDiscovery:
    """Discovers collaborators and their interfaces"""
    
    def __init__(self, project_path: Path):
        self.project_path = project_path
        self.discovered_interfaces: Dict[str, List[str]] = {}
    
    def discover_from_file(self, file_path: Path) -> List[Collaborator]:
        """Discover collaborators from a Python file"""
        if not file_path.exists() or not file_path.suffix == '.py':
            return []
        
        try:
            with open(file_path, 'r') as f:
                content = f.read()
            
            tree = ast.parse(content)
            collaborators = []
            
            # Find class definitions and their dependencies
            for node in ast.walk(tree):
                if isinstance(node, ast.ClassDef):
                    class_collaborators = self._extract_class_collaborators(node, content)
                    collaborators.extend(class_collaborators)
            
            return collaborators
            
        except Exception as e:
            print(f"Warning: Could not parse {file_path}: {e}")
            return []
    
    def _extract_class_collaborators(self, class_node: ast.ClassDef, content: str) -> List[Collaborator]:
        """Extract collaborators from a class definition"""
        collaborators = []
        
        # Look for constructor parameters (typical injection points)
        for node in ast.walk(class_node):
            if isinstance(node, ast.FunctionDef) and node.name == '__init__':
                for arg in node.args.args[1:]:  # Skip 'self'
                    if arg.annotation:
                        type_name = self._extract_type_name(arg.annotation)
                        if type_name and type_name not in ['str', 'int', 'bool', 'float', 'list', 'dict']:
                            interface = self._discover_interface(type_name)
                            collaborator = Collaborator(
                                name=type_name,
                                interface=interface,
                                file_path="",  # Would need to resolve
                                is_external=self._is_external_dependency(type_name)
                            )
                            collaborators.append(collaborator)
        
        return collaborators
    
    def _extract_type_name(self, annotation) -> Optional[str]:
        """Extract type name from AST annotation"""
        if isinstance(annotation, ast.Name):
            return annotation.id
        elif isinstance(annotation, ast.Attribute):
            return annotation.attr
        return None
    
    def _discover_interface(self, type_name: str) -> List[str]:
        """Discover interface methods for a type"""
        if type_name in self.discovered_interfaces:
            return self.discovered_interfaces[type_name]
        
        # Try to find the class definition
        interface = self._find_class_methods(type_name)
        self.discovered_interfaces[type_name] = interface
        return interface
    
    def _find_class_methods(self, type_name: str) -> List[str]:
        """Find public methods of a class"""
        methods = []
        
        # Search through project files
        for py_file in self.project_path.rglob("*.py"):
            try:
                with open(py_file, 'r') as f:
                    content = f.read()
                
                tree = ast.parse(content)
                
                for node in ast.walk(tree):
                    if isinstance(node, ast.ClassDef) and node.name == type_name:
                        for item in node.body:
                            if isinstance(item, ast.FunctionDef) and not item.name.startswith('_'):
                                methods.append(item.name)
                        break
                        
            except Exception:
                continue
        
        return methods
    
    def _is_external_dependency(self, type_name: str) -> bool:
        """Check if type is an external dependency"""
        external_patterns = [
            'Session', 'Engine', 'Client', 'Repository', 'Service',
            'Logger', 'Cache', 'Queue', 'Database'
        ]
        return any(pattern in type_name for pattern in external_patterns)


class MockGenerator:
    """Generates mock objects and contracts"""
    
    def generate_mock_for_collaborator(self, collaborator: Collaborator) -> str:
        """Generate mock code for a collaborator"""
        mock_code = f"""
# Mock for {collaborator.name}
{collaborator.name.lower()}_mock = Mock(spec={collaborator.name})
"""
        
        for method in collaborator.interface:
            mock_code += f"""
{collaborator.name.lower()}_mock.{method} = Mock()
"""
        
        return mock_code
    
    def generate_behavior_test(self, spec: BehaviorSpec) -> str:
        """Generate behavior test following London School principles"""
        test_code = f"""
import pytest
from unittest.mock import Mock, patch
from {spec.subject_class.lower()} import {spec.subject_class}


class Test{spec.subject_class}:
    \"\"\"
    London School TDD tests for {spec.subject_class}
    Focuses on behavior verification and object collaborations
    \"\"\"
    
    def setup_method(self):
        \"\"\"Setup mocks for each test\"\"\"
"""
        
        # Generate mock setup
        for collaborator in spec.collaborators:
            test_code += f"""        self.{collaborator.name.lower()}_mock = Mock(spec={collaborator.name})
"""
        
        test_code += f"""
        self.{spec.subject_class.lower()} = {spec.subject_class}(
"""
        
        # Constructor arguments
        for i, collaborator in enumerate(spec.collaborators):
            comma = "," if i < len(spec.collaborators) - 1 else ""
            test_code += f"""            {collaborator.name.lower()}=self.{collaborator.name.lower()}_mock{comma}
"""
        
        test_code += """        )
    
"""
        
        # Generate test methods for each scenario
        for scenario in spec.scenarios:
            test_code += self._generate_scenario_test(scenario, spec)
        
        return test_code
    
    def _generate_scenario_test(self, scenario: Dict[str, any], spec: BehaviorSpec) -> str:
        """Generate test method for a specific scenario"""
        method_name = scenario.get('name', 'scenario').replace(' ', '_').lower()
        
        test_method = f"""
    def test_{method_name}(self):
        \"\"\"Test: {scenario.get('description', scenario.get('name', 'Scenario'))}\"\"\"
        # Arrange
"""
        
        # Setup mock expectations
        for setup in scenario.get('setup', []):
            test_method += f"""        {setup}
"""
        
        test_method += """
        # Act
"""
        
        # Execute the action
        action = scenario.get('action', f'result = self.{spec.subject_class.lower()}.some_method()')
        test_method += f"""        {action}
        
        # Assert - Verify interactions (London School focus)
"""
        
        # Verify mock interactions
        for assertion in scenario.get('assertions', []):
            test_method += f"""        {assertion}
"""
        
        return test_method


class LondonSchoolTDD:
    """Main orchestrator for London School TDD implementation"""
    
    def __init__(self, project_path: Path):
        self.project_path = project_path
        self.discovery = CollaboratorDiscovery(project_path)
        self.mock_generator = MockGenerator()
    
    def analyze_class_for_tdd(self, class_file: Path) -> BehaviorSpec:
        """Analyze a class and generate TDD specification"""
        collaborators = self.discovery.discover_from_file(class_file)
        
        # Extract class name from file
        class_name = self._extract_class_name(class_file)
        
        # Generate basic scenarios based on class structure
        scenarios = self._generate_basic_scenarios(class_file, class_name)
        
        # Generate mock contracts
        contracts = [
            MockContract(
                collaborator=collab,
                interactions=[],
                return_values={},
                side_effects={}
            )
            for collab in collaborators
        ]
        
        return BehaviorSpec(
            subject_class=class_name,
            collaborators=collaborators,
            scenarios=scenarios,
            contracts=contracts
        )
    
    def _extract_class_name(self, file_path: Path) -> str:
        """Extract main class name from file"""
        try:
            with open(file_path, 'r') as f:
                content = f.read()
            
            tree = ast.parse(content)
            
            for node in ast.walk(tree):
                if isinstance(node, ast.ClassDef):
                    return node.name
                    
        except Exception:
            pass
        
        # Fallback to filename
        return file_path.stem.title()
    
    def _generate_basic_scenarios(self, file_path: Path, class_name: str) -> List[Dict[str, any]]:
        """Generate basic test scenarios from class structure"""
        scenarios = []
        
        try:
            with open(file_path, 'r') as f:
                content = f.read()
            
            tree = ast.parse(content)
            
            for node in ast.walk(tree):
                if isinstance(node, ast.ClassDef) and node.name == class_name:
                    for method_node in node.body:
                        if isinstance(method_node, ast.FunctionDef) and not method_node.name.startswith('_'):
                            scenario = {
                                'name': f'calls_{method_node.name}',
                                'description': f'Should coordinate properly when {method_node.name} is called',
                                'setup': [
                                    '# Setup mock expectations'
                                ],
                                'action': f'result = self.{class_name.lower()}.{method_node.name}()',
                                'assertions': [
                                    f'# Verify {method_node.name} interactions',
                                    'assert result is not None  # Replace with actual assertion'
                                ]
                            }
                            scenarios.append(scenario)
                            
        except Exception:
            pass
        
        return scenarios
    
    def generate_test_file(self, class_file: Path, output_dir: Path) -> Path:
        """Generate complete test file for a class"""
        spec = self.analyze_class_for_tdd(class_file)
        test_code = self.mock_generator.generate_behavior_test(spec)
        
        # Determine output path
        relative_path = class_file.relative_to(self.project_path)
        test_file_name = f"test_{class_file.name}"
        output_path = output_dir / test_file_name
        
        # Ensure output directory exists
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        # Write test file
        with open(output_path, 'w') as f:
            f.write(test_code)
        
        return output_path
    
    def validate_london_school_principles(self, test_file: Path) -> List[str]:
        """Validate that test follows London School principles"""
        issues = []
        
        if not test_file.exists():
            return ["Test file does not exist"]
        
        try:
            with open(test_file, 'r') as f:
                content = f.read()
                
            # Check for mock usage
            if 'Mock' not in content and 'mock' not in content:
                issues.append("No mocks found - London School relies heavily on mocks")
            
            # Check for interaction verification
            if '.assert_called' not in content and 'called_with' not in content:
                issues.append("No interaction verification found - should verify mock calls")
            
            # Check for state verification (should be minimal)
            state_checks = len(re.findall(r'assert.*==|assertEqual', content))
            interaction_checks = len(re.findall(r'assert_called|called_with', content))
            
            if state_checks > interaction_checks:
                issues.append("More state verification than interaction verification - prefer behavior over state")
            
            # Check for proper test structure
            if 'def setup_method' not in content and 'setUp' not in content:
                issues.append("No test setup method found - mocks should be properly initialized")
                
        except Exception as e:
            issues.append(f"Could not analyze test file: {e}")
        
        return issues


def main():
    """Main entry point for London School TDD tooling"""
    import argparse
    
    parser = argparse.ArgumentParser(description="London School TDD Generator")
    parser.add_argument('--analyze', type=str, help='Analyze class file for TDD')
    parser.add_argument('--generate', type=str, help='Generate test file for class')
    parser.add_argument('--validate', type=str, help='Validate test follows London School principles')
    parser.add_argument('--output', type=str, help='Output directory for generated tests')
    
    args = parser.parse_args()
    
    project_path = Path.cwd()
    if 'apps/backend' in str(project_path):
        project_path = project_path / 'apps' / 'backend'
    
    london_tdd = LondonSchoolTDD(project_path)
    
    if args.analyze:
        class_file = Path(args.analyze)
        if not class_file.exists():
            print(f"File not found: {class_file}")
            sys.exit(1)
        
        spec = london_tdd.analyze_class_for_tdd(class_file)
        
        print(f"Class: {spec.subject_class}")
        print(f"Collaborators: {len(spec.collaborators)}")
        for collab in spec.collaborators:
            print(f"  - {collab.name}: {', '.join(collab.interface)}")
        print(f"Scenarios: {len(spec.scenarios)}")
        for scenario in spec.scenarios:
            print(f"  - {scenario['name']}")
    
    elif args.generate:
        class_file = Path(args.generate)
        output_dir = Path(args.output) if args.output else project_path / 'tests'
        
        if not class_file.exists():
            print(f"File not found: {class_file}")
            sys.exit(1)
        
        test_file = london_tdd.generate_test_file(class_file, output_dir)
        print(f"Generated test file: {test_file}")
    
    elif args.validate:
        test_file = Path(args.validate)
        issues = london_tdd.validate_london_school_principles(test_file)
        
        if not issues:
            print("✅ Test follows London School principles")
        else:
            print("❌ London School principle violations found:")
            for issue in issues:
                print(f"  - {issue}")
            sys.exit(1)


if __name__ == "__main__":
    main()