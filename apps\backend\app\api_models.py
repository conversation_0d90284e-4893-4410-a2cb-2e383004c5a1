"""API models for LONI backend."""
from typing import Optional
from pydantic import BaseModel
from typing import List


class ModelInfo(BaseModel):
    """Model information response."""
    name: str
    size: Optional[str] = None
    modified_at: Optional[str] = None
    status: str = "available"
    description: Optional[str] = None


class TranscriptionRequest(BaseModel):
    """Audio transcription request."""
    audio_path: str
    language: Optional[str] = None
    verbose: bool = False


class TranscriptionResponse(BaseModel):
    """Audio transcription response."""
    transcription: str
    language: Optional[str] = None
    duration: Optional[float] = None
    word_count: Optional[int] = None
    confidence: Optional[float] = None


class ErrorResponse(BaseModel):
    """Error response model."""
    message: str
    error_id: str = "internal_error"
    details: Optional[str] = None


class HealthResponse(BaseModel):
    """Health check response."""
    status: str
    version: str
    timestamp: Optional[str] = None
