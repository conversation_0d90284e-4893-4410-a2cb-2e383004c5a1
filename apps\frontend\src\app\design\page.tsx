import { AppLayout } from "@/components/layout/AppLayout";
import { Card } from "@/components/common/Card";
import { Button } from "@/components/common/Button";
import { useState } from "react";
import { toast } from "sonner";
import {
  Palette,
  Type,
  Layout,
  Settings,
  Download,
  RefreshCw,
  Eye,
  Code
} from "lucide-react";

export default function DesignPage() {
  const [selectedTab, setSelectedTab] = useState<'colors' | 'typography' | 'components' | 'layout'>('colors');

  const tabs = [
    { id: 'colors', label: 'Colors', icon: Palette },
    { id: 'typography', label: 'Typography', icon: Type },
    { id: 'components', label: 'Components', icon: Layout },
    { id: 'layout', label: 'Layout', icon: Settings }
  ];

  const colorPalette = [
    { name: 'Primary', colors: ['#3b82f6', '#1d4ed8', '#93c5fd', '#1e40af'] },
    { name: 'Secondary', colors: ['#64748b', '#475569', '#cbd5e1', '#334155'] },
    { name: 'Success', colors: ['#10b981', '#047857', '#6ee7b7', '#065f46'] },
    { name: 'Warning', colors: ['#f59e0b', '#d97706', '#fcd34d', '#b45309'] },
    { name: 'Error', colors: ['#ef4444', '#dc2626', '#fca5a5', '#b91c1c'] },
    { name: 'Background', colors: ['#ffffff', '#f8fafc', '#f1f5f9', '#e2e8f0'] }
  ];

  const handleSaveConfig = () => {
    toast.success("Design configuration saved successfully!");
  };

  const handleResetConfig = () => {
    toast.info("Design configuration reset to defaults");
  };

  return (
    <AppLayout>
      <div className="space-y-8">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold">Design System</h1>
            <p className="text-muted-foreground">Customize the appearance and behavior of your application</p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" onClick={handleResetConfig}>
              <RefreshCw className="h-4 w-4 mr-2" />
              Reset
            </Button>
            <Button onClick={handleSaveConfig}>
              <Download className="h-4 w-4 mr-2" />
              Save Changes
            </Button>
          </div>
        </div>

        {/* Tabs */}
        <Card className="p-6">
          <div className="flex gap-1 border-b mb-6">
            {tabs.map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setSelectedTab(tab.id as any)}
                  className={`flex items-center gap-2 px-4 py-2 text-sm font-medium transition-colors ${
                    selectedTab === tab.id
                      ? 'text-primary border-b-2 border-primary'
                      : 'text-muted-foreground hover:text-foreground'
                  }`}
                >
                  <Icon className="h-4 w-4" />
                  {tab.label}
                </button>
              );
            })}
          </div>

          {/* Tab Content */}
          {selectedTab === 'colors' && (
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-semibold mb-4">Color Palette</h3>
                <div className="grid gap-4">
                  {colorPalette.map((palette) => (
                    <div key={palette.name} className="space-y-2">
                      <h4 className="font-medium">{palette.name}</h4>
                      <div className="flex gap-2">
                        {palette.colors.map((color, index) => (
                          <div
                            key={index}
                            className="w-12 h-12 rounded-md border-2 border-white shadow-sm cursor-pointer hover:scale-110 transition-transform"
                            style={{ backgroundColor: color }}
                            title={color}
                          />
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              <div>
                <h3 className="text-lg font-semibold mb-4">Theme Settings</h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <Card className="p-4 text-center">
                    <div className="text-4xl mb-2">☀️</div>
                    <h4 className="font-medium">Light</h4>
                    <p className="text-sm text-muted-foreground">Clean and bright</p>
                    <Button size="sm" className="mt-2 w-full">Apply</Button>
                  </Card>
                  <Card className="p-4 text-center">
                    <div className="text-4xl mb-2">🌙</div>
                    <h4 className="font-medium">Dark</h4>
                    <p className="text-sm text-muted-foreground">Easy on the eyes</p>
                    <Button size="sm" className="mt-2 w-full">Apply</Button>
                  </Card>
                  <Card className="p-4 text-center">
                    <div className="text-4xl mb-2">🌓</div>
                    <h4 className="font-medium">Auto</h4>
                    <p className="text-sm text-muted-foreground">System preference</p>
                    <Button size="sm" className="mt-2 w-full">Apply</Button>
                  </Card>
                </div>
              </div>
            </div>
          )}

          {selectedTab === 'typography' && (
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-semibold mb-4">Font Families</h3>
                <div className="space-y-4">
                  <div className="p-4 border rounded-lg">
                    <h4 className="font-medium mb-2">Primary Font (Geist Sans)</h4>
                    <p className="text-sm text-muted-foreground mb-2 font-geist-sans">
                      The quick brown fox jumps over the lazy dog
                    </p>
                    <code className="text-xs bg-muted px-2 py-1 rounded">
                      font-geist-sans
                    </code>
                  </div>
                  <div className="p-4 border rounded-lg">
                    <h4 className="font-medium mb-2">Monospace Font (Geist Mono)</h4>
                    <p className="text-sm text-muted-foreground mb-2 font-geist-mono">
                      The quick brown fox jumps over the lazy dog
                    </p>
                    <code className="text-xs bg-muted px-2 py-1 rounded">
                      font-geist-mono
                    </code>
                  </div>
                </div>
              </div>

              <div>
                <h3 className="text-lg font-semibold mb-4">Text Sizes</h3>
                <div className="space-y-3">
                  {['text-xs', 'text-sm', 'text-base', 'text-lg', 'text-xl', 'text-2xl', 'text-3xl'].map((size) => (
                    <div key={size} className="flex items-center gap-4">
                      <code className="text-xs bg-muted px-2 py-1 rounded w-20">
                        {size}
                      </code>
                      <p className={`${size} text-muted-foreground`}>
                        The quick brown fox jumps over the lazy dog
                      </p>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          {selectedTab === 'components' && (
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-semibold mb-4">Component Variants</h3>
                <div className="space-y-4">
                  <div>
                    <h4 className="font-medium mb-3">Buttons</h4>
                    <div className="flex gap-2 flex-wrap">
                      <Button size="sm">Small</Button>
                      <Button>Default</Button>
                      <Button size="lg">Large</Button>
                      <Button variant="outline">Outline</Button>
                      <Button variant="ghost">Ghost</Button>
                      <Button variant="destructive">Destructive</Button>
                    </div>
                  </div>

                  <div>
                    <h4 className="font-medium mb-3">Cards</h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <Card className="p-4">
                        <h5 className="font-medium mb-2">Default Card</h5>
                        <p className="text-sm text-muted-foreground">
                          This is a default card with standard styling.
                        </p>
                      </Card>
                      <Card className="p-4 border-primary">
                        <h5 className="font-medium mb-2">Primary Card</h5>
                        <p className="text-sm text-muted-foreground">
                          This card has a primary border color.
                        </p>
                      </Card>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {selectedTab === 'layout' && (
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-semibold mb-4">Layout Options</h3>
                <div className="space-y-4">
                  <div className="p-4 border rounded-lg">
                    <div className="flex items-center justify-between mb-3">
                      <h4 className="font-medium">Sidebar Position</h4>
                      <select className="text-sm border rounded px-2 py-1">
                        <option>Left</option>
                        <option>Right</option>
                      </select>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      Choose whether the sidebar appears on the left or right side
                    </p>
                  </div>

                  <div className="p-4 border rounded-lg">
                    <div className="flex items-center justify-between mb-3">
                      <h4 className="font-medium">Container Width</h4>
                      <select className="text-sm border rounded px-2 py-1">
                        <option>Default (1280px)</option>
                        <option>Wide (1536px)</option>
                        <option>Narrow (1024px)</option>
                        <option>Full Width</option>
                      </select>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      Control the maximum width of page content
                    </p>
                  </div>

                  <div className="p-4 border rounded-lg">
                    <div className="flex items-center justify-between mb-3">
                      <h4 className="font-medium">Border Radius</h4>
                      <select className="text-sm border rounded px-2 py-1">
                        <option>None</option>
                        <option>Small (0.25rem)</option>
                        <option>Medium (0.5rem)</option>
                        <option>Large (0.75rem)</option>
                        <option>Extra Large (1rem)</option>
                      </select>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      Adjust the border radius for buttons, cards, and other components
                    </p>
                  </div>
                </div>
              </div>
            </div>
          )}
        </Card>

        {/* Preview Section */}
        <Card className="p-6">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-semibold">Live Preview</h2>
            <div className="flex gap-2">
              <Button variant="outline" size="sm">
                <Eye className="h-4 w-4 mr-2" />
                Preview
              </Button>
              <Button variant="outline" size="sm">
                <Code className="h-4 w-4 mr-2" />
                Export
              </Button>
            </div>
          </div>
          <div className="border rounded-lg p-4 bg-muted/30">
            <p className="text-sm text-muted-foreground text-center py-8">
              Preview area - changes will be reflected here in real-time
            </p>
          </div>
        </Card>
      </div>
    </AppLayout>
  );
}

