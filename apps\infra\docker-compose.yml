# Docker Compose configuration for LONI stack
# Restructured following Docker best practices for multi-service applications

name: loni-stack

# Define custom bridge network for service communication
networks:
  loni-network:
    driver: bridge
    name: loni-network

# Named volumes for persistent data storage
volumes:
  postgres_data:
    name: loni_postgres_data
  qdrant_data:
    name: loni_qdrant_data
  ollama_models:
    name: loni_ollama_models
  redis_data:
    name: loni_redis_data

services:
  # Redis cache service
  redis:
    image: redis:7-alpine
    container_name: loni-redis
    restart: unless-stopped
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    healthcheck:
      test: [ "CMD", "redis-cli", "ping" ]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 10s
    networks:
      - loni-network

  # PostgreSQL database service
  postgres:
    image: postgres:16-alpine
    container_name: loni-postgres
    restart: unless-stopped
    environment:
      POSTGRES_USER: ${POSTGRES_USER:-loni_user}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-loni_password}
      POSTGRES_DB: ${POSTGRES_DB:-loni_db}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    healthcheck:
      test: [ "CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-loni_user}" ]
      interval: 10s
      timeout: 5s
      retries: 12
      start_period: 20s
    networks:
      - loni-network

  # Qdrant vector database service
  qdrant:
    image: qdrant/qdrant:v1.8.4
    container_name: loni-qdrant
    restart: unless-stopped
    environment:
      QDRANT__STORAGE__PERSISTENCE_PATH: /qdrant/storage
      QDRANT__SERVICE__GRPC_PORT: 6334
    volumes:
      - qdrant_data:/qdrant/storage
    ports:
      - "6333:6333"
      - "6334:6334"
    healthcheck:
      test: [ "CMD-SHELL", "curl -f http://localhost:6333/health || exit 1" ]
      interval: 10s
      timeout: 5s
      retries: 12
      start_period: 20s
    networks:
      - loni-network

  # Ollama AI model service
  ollama:
    image: ollama/ollama:0.1.44
    container_name: loni-ollama
    restart: unless-stopped
    environment:
      OLLAMA_HOST: 0.0.0.0
    volumes:
      - ollama_models:/root/.ollama
    ports:
      - "11434:11434"
    healthcheck:
      test: [ "CMD-SHELL", "curl -f http://localhost:11434/api/tags || curl -f http://localhost:11434 || exit 1" ]
      interval: 15s
      timeout: 5s
      retries: 10
      start_period: 25s
    networks:
      - loni-network

  # Backend API service (Python FastAPI)
  backend:
    build:
      context: ../backend
      dockerfile: Dockerfile
    container_name: loni-backend
    restart: unless-stopped
    env_file:
      - .env
    environment:
      # Application configuration
      PORT: 8000
      LOG_LEVEL: ${LOG_LEVEL:-info}
      LOG_JSON: ${LOG_JSON:-false}
      ENABLE_OPENAPI: ${ENABLE_OPENAPI:-true}
      DEBUG: ${DEBUG:-false}
      API_V1_STR: ${API_V1_STR:-/api/v1}
      SECRET_KEY: ${SECRET_KEY}

      # Database connections
      DATABASE_URL: postgresql://${POSTGRES_USER:-loni_user}:${POSTGRES_PASSWORD:-loni_password}@postgres:5432/${POSTGRES_DB:-loni_db}
      REDIS_HOST: redis
      REDIS_PORT: 6379
      REDIS_DB: 0

      # Vector database
      QDRANT_URL: http://qdrant:6333
      QDRANT_HOST: qdrant
      QDRANT_PORT: 6333

      # Model management configuration
      MODEL_MGR_DATA_ROOT: /workspace/apps/data
      MODEL_MGR_ENABLE_WHISPER: ${MODEL_MGR_ENABLE_WHISPER:-true}
      MODEL_MGR_ENABLE_OLLAMA: ${MODEL_MGR_ENABLE_OLLAMA:-true}
      MODEL_MGR_PROCESS_TIMEOUT_SEC: ${MODEL_MGR_PROCESS_TIMEOUT_SEC:-600}
      MODEL_MGR_CACHE_TTL_SEC: ${MODEL_MGR_CACHE_TTL_SEC:-1800}
      MODEL_MGR_INSTALL_LOCK_TTL_SEC: ${MODEL_MGR_INSTALL_LOCK_TTL_SEC:-1800}
      MODEL_MGR_MAX_PARALLEL_INSTALLS: ${MODEL_MGR_MAX_PARALLEL_INSTALLS:-1}
      MODEL_MGR_WHISPER_CATALOG_VERSION: ${MODEL_MGR_WHISPER_CATALOG_VERSION:-1}
      MODEL_MGR_WHISPER_USE_CLI: ${MODEL_MGR_WHISPER_USE_CLI:-true}
      MODEL_MGR_WHISPER_EXECUTABLE: ${MODEL_MGR_WHISPER_EXECUTABLE:-whisper}
      MODEL_MGR_OLLAMA_API_BASE: http://ollama:11434
      MODEL_MGR_OLLAMA_EXECUTABLE: ${MODEL_MGR_OLLAMA_EXECUTABLE:-ollama}
      MODEL_MGR_OLLAMA_DATA_DIR: /workspace/apps/data/models/ollama
      MODEL_MGR_OLLAMA_CATALOG_SOURCE: ${MODEL_MGR_OLLAMA_CATALOG_SOURCE:-web}
      MODEL_MGR_ALLOW_OLLAMA_SYMLINK: ${MODEL_MGR_ALLOW_OLLAMA_SYMLINK:-false}
    volumes:
      - ../data:/workspace/apps/data
    ports:
      - "8000:8000"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_started
      qdrant:
        condition: service_healthy
      ollama:
        condition: service_started
    healthcheck:
      test: [ "CMD-SHELL", "python -c \"import urllib.request,sys; sys.exit(0) if urllib.request.urlopen('http://localhost:8000/api/v1/health', timeout=5).getcode()==200 else sys.exit(1)\"" ]
      interval: 15s
      timeout: 10s
      retries: 12
      start_period: 30s
    networks:
      - loni-network

  # Frontend web application (Next.js with Bun)
  frontend:
    build:
      context: ../frontend
      dockerfile: Dockerfile
      target: runner # Production build target
    container_name: loni-frontend
    restart: unless-stopped
    env_file:
      - .env
    environment:
      NODE_ENV: production
      NEXT_PUBLIC_API_BASE: ${NEXT_PUBLIC_API_BASE:-/api/v1}
      NEXT_TELEMETRY_DISABLED: 1
      PORT: 3000
      HOSTNAME: "0.0.0.0"
    ports:
      - "3000:3000"
    depends_on:
      backend:
        condition: service_healthy
    healthcheck:
      test: [ "CMD-SHELL", "curl -f http://localhost:3000/ || exit 1" ]
      interval: 15s
      timeout: 10s
      retries: 10
      start_period: 30s
    networks:
      - loni-network

  # Nginx reverse proxy
  nginx:
    image: nginx:1.25-alpine
    container_name: loni-nginx
    restart: unless-stopped
    depends_on:
      backend:
        condition: service_healthy
      frontend:
        condition: service_healthy
    ports:
      - "80:80"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
    healthcheck:
      test: [ "CMD-SHELL", "curl -f http://localhost || exit 1" ]
      interval: 10s
      timeout: 5s
      retries: 12
      start_period: 15s
    networks:
      - loni-network

  # Development services (activated with --profile dev)
  # Usage: docker compose --profile dev up

  # Frontend development service with hot reload
  frontend-dev:
    build:
      context: ../frontend
      dockerfile: Dockerfile
      target: dev # Development build target
    container_name: loni-frontend-dev
    restart: unless-stopped
    env_file:
      - .env
    environment:
      NODE_ENV: development
      NEXT_PUBLIC_API_BASE: ${NEXT_PUBLIC_API_BASE:-/api/v1}
    ports:
      - "3000:3000"
    volumes:
      # Bind mount for hot reload
      - ../frontend:/app
      - /app/node_modules
      - /app/.next
    networks:
      - loni-network
    profiles:
      - dev

  # Backend development service with hot reload
  backend-dev:
    build:
      context: ../backend
      dockerfile: Dockerfile
    container_name: loni-backend-dev
    restart: unless-stopped
    env_file:
      - .env
    environment:
      PORT: 8000
      DEBUG: true
      SECRET_KEY: ${SECRET_KEY:-dev-secret-key-change-in-production}
      DATABASE_URL: postgresql://${POSTGRES_USER:-loni_user}:${POSTGRES_PASSWORD:-loni_password}@postgres:5432/${POSTGRES_DB:-loni_db}
      REDIS_HOST: redis
      REDIS_PORT: 6379
      REDIS_DB: 0
      QDRANT_HOST: qdrant
      QDRANT_PORT: 6333
      QDRANT_URL: http://qdrant:6333
    volumes:
      # Bind mount for hot reload
      - ../backend:/app
    ports:
      - "8000:8000"
    depends_on:
      postgres:
        condition: service_started
      redis:
        condition: service_started
      qdrant:
        condition: service_started
    networks:
      - loni-network
    profiles:
      - dev
