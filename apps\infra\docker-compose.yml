# Docker Compose configuration for LONI stack
# Simplified configuration with frontend and backend services only

name: loni-stack

# Define custom bridge network for service communication
networks:
  loni-network:
    driver: bridge
    name: loni-network

services:
  # Backend API service (Python FastAPI)
  backend:
    build:
      context: ../backend
      dockerfile: Dockerfile
    container_name: loni-backend
    restart: unless-stopped
    env_file:
      - .env
    environment:
      # Application configuration
      PORT: 8000
      LOG_LEVEL: ${LOG_LEVEL:-info}
      LOG_JSON: ${LOG_JSON:-false}
      ENABLE_OPENAPI: ${ENABLE_OPENAPI:-true}
      DEBUG: ${DEBUG:-false}
      API_V1_STR: ${API_V1_STR:-/api/v1}
      SECRET_KEY: ${SECRET_KEY}
    ports:
      - "8000:8000"
    healthcheck:
      test: ["CMD-SHELL", "python -c \"import urllib.request,sys; sys.exit(0) if urllib.request.urlopen('http://localhost:8000/api/v1/health', timeout=5).getcode()==200 else sys.exit(1)\""]
      interval: 15s
      timeout: 10s
      retries: 12
      start_period: 30s
    networks:
      - loni-network

  # Frontend web application (Next.js with Bun)
  frontend:
    build:
      context: ../frontend
      dockerfile: Dockerfile
      target: runner  # Production build target
    container_name: loni-frontend
    restart: unless-stopped
    env_file:
      - .env
    environment:
      NODE_ENV: production
      NEXT_PUBLIC_API_BASE: ${NEXT_PUBLIC_API_BASE:-/api/v1}
      NEXT_TELEMETRY_DISABLED: 1
      PORT: 3000
      HOSTNAME: "0.0.0.0"
    ports:
      - "3000:3000"
    depends_on:
      backend:
        condition: service_healthy
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:3000/ || exit 1"]
      interval: 15s
      timeout: 10s
      retries: 10
      start_period: 30s
    networks:
      - loni-network
