"""
Performance tests for model manager components.
"""

import pytest
import time
import threading
import asyncio
from concurrent.futures import ThreadPoolExecutor
from unittest.mock import Mock, patch
import psutil
import gc

from app.model_manager.infra.providers.whisper_provider import WhisperProviderImpl
from app.model_manager.infra.providers.ollama_provider import OllamaProviderImpl
from app.model_manager.domain.services import CatalogService, InventoryService
from app.model_manager.domain.entities import Provider, AvailableModel, PagedResult


class TestProviderPerformance:
    """Test performance characteristics of providers."""
    
    def test_whisper_catalog_performance(self, whisper_provider, mock_config):
        """Test Whisper catalog retrieval performance."""
        # Setup large catalog
        large_catalog = [
            {
                "name": f"model_{i}",
                "size": "100 MB",
                "size_bytes": 100000000,
                "multilingual": True,
                "gpu_recommended": i % 2 == 0,
                "languages": ["en", "es", "fr"],
                "notes": f"Model {i} for testing",
                "performance_tier": "balanced"
            }
            for i in range(1000)
        ]
        mock_config.whisper.models = large_catalog
        
        start_time = time.time()
        catalog = whisper_provider.get_catalog()
        end_time = time.time()
        
        # Should complete within reasonable time
        execution_time = end_time - start_time
        assert execution_time < 1.0  # Less than 1 second
        assert len(catalog) == 1000
    
    def test_ollama_catalog_caching_performance(self, ollama_provider, mock_cache):
        """Test Ollama catalog caching performance."""
        # Setup cached data
        cached_models = [
            {"provider": "OLLAMA", "name": f"model_{i}", "version": None, "metadata": {}}
            for i in range(500)
        ]
        mock_cache.get.return_value = {"items": cached_models}
        
        # First call should use cache
        start_time = time.time()
        result = ollama_provider.get_catalog()
        end_time = time.time()
        
        execution_time = end_time - start_time
        assert execution_time < 0.1  # Should be very fast with cache
        assert result.total == 500
    
    def test_concurrent_provider_operations(self, whisper_provider, ollama_provider):
        """Test concurrent operations on providers."""
        def whisper_operation():
            return whisper_provider.get_catalog()
        
        def ollama_operation():
            from app.model_manager.domain.entities import Pagination
            return ollama_provider.get_catalog(Pagination(page=1, page_size=10))
        
        with ThreadPoolExecutor(max_workers=10) as executor:
            start_time = time.time()
            
            # Submit multiple concurrent operations
            futures = []
            for _ in range(5):
                futures.append(executor.submit(whisper_operation))
                futures.append(executor.submit(ollama_operation))
            
            # Wait for all to complete
            results = [future.result() for future in futures]
            
            end_time = time.time()
        
        execution_time = end_time - start_time
        assert execution_time < 5.0  # Should complete within 5 seconds
        assert len(results) == 10
    
    def test_memory_usage_during_operations(self, whisper_provider):
        """Test memory usage during provider operations."""
        process = psutil.Process()
        initial_memory = process.memory_info().rss
        
        # Perform multiple operations
        for _ in range(100):
            catalog = whisper_provider.get_catalog()
            # Force garbage collection
            del catalog
            gc.collect()
        
        final_memory = process.memory_info().rss
        memory_increase = final_memory - initial_memory
        
        # Memory increase should be reasonable (less than 50MB)
        assert memory_increase < 50 * 1024 * 1024


class TestServicePerformance:
    """Test performance characteristics of services."""
    
    def test_catalog_service_large_dataset(self, whisper_provider, ollama_provider, mock_filesystem):
        """Test catalog service with large datasets."""
        catalog_service = CatalogService(whisper_provider, ollama_provider, mock_filesystem)
        
        # Mock large Whisper catalog
        whisper_models = [
            AvailableModel(
                provider=Provider.WHISPER,
                name=f"whisper_model_{i}",
                metadata={"size": "100 MB"}
            )
            for i in range(500)
        ]
        
        # Mock large Ollama catalog
        ollama_models = [
            AvailableModel(
                provider=Provider.OLLAMA,
                name=f"ollama_model_{i}",
                metadata={"size_bytes": **********}
            )
            for i in range(500)
        ]
        
        with patch.object(whisper_provider, 'get_catalog', return_value=whisper_models), \
             patch.object(ollama_provider, 'get_catalog') as mock_ollama:
            
            mock_ollama.return_value = PagedResult(
                items=ollama_models,
                total=500,
                page=1,
                page_size=500
            )
            
            start_time = time.time()
            result = catalog_service.list_available(None, 1, 1000, None)
            end_time = time.time()
        
        execution_time = end_time - start_time
        assert execution_time < 2.0  # Should complete within 2 seconds
        assert result.total == 1000
    
    def test_inventory_service_large_filesystem(self, mock_filesystem):
        """Test inventory service with large filesystem."""
        inventory_service = InventoryService(mock_filesystem)
        
        # Mock large filesystem structure
        providers = ["whisper", "ollama"]
        models_per_provider = 250
        
        def mock_list_dirs(path):
            if "models" in path and path.endswith("models"):
                return providers
            elif "whisper" in path:
                return [f"whisper_model_{i}" for i in range(models_per_provider)]
            elif "ollama" in path:
                return [f"ollama_model_{i}" for i in range(models_per_provider)]
            return []
        
        def mock_read_json(path):
            if "whisper" in path:
                return {
                    "provider": "WHISPER",
                    "name": "whisper_model",
                    "installed_at": "2024-01-01T12:00:00Z",
                    "source": "whisper_sdk"
                }
            elif "ollama" in path:
                return {
                    "provider": "OLLAMA",
                    "name": "ollama_model",
                    "installed_at": "2024-01-01T12:00:00Z",
                    "source": "ollama_cli"
                }
            return {}
        
        mock_filesystem.exists.return_value = True
        mock_filesystem.list_dirs.side_effect = mock_list_dirs
        mock_filesystem.read_json.side_effect = mock_read_json
        mock_filesystem.size_on_disk.return_value = 1000000
        
        start_time = time.time()
        models = inventory_service.list_installed()
        end_time = time.time()
        
        execution_time = end_time - start_time
        assert execution_time < 3.0  # Should complete within 3 seconds
        assert len(models) == 500  # 250 per provider
    
    def test_concurrent_service_operations(self, whisper_provider, ollama_provider, mock_filesystem):
        """Test concurrent operations across services."""
        catalog_service = CatalogService(whisper_provider, ollama_provider, mock_filesystem)
        inventory_service = InventoryService(mock_filesystem)
        
        # Mock service responses
        with patch.object(whisper_provider, 'get_catalog', return_value=[]), \
             patch.object(ollama_provider, 'get_catalog') as mock_ollama, \
             patch.object(mock_filesystem, 'exists', return_value=False):
            
            mock_ollama.return_value = PagedResult(items=[], total=0, page=1, page_size=50)
            
            def catalog_operation():
                return catalog_service.list_available(None, 1, 50, None)
            
            def inventory_operation():
                return inventory_service.list_installed()
            
            with ThreadPoolExecutor(max_workers=20) as executor:
                start_time = time.time()
                
                # Submit multiple concurrent operations
                futures = []
                for _ in range(10):
                    futures.append(executor.submit(catalog_operation))
                    futures.append(executor.submit(inventory_operation))
                
                # Wait for all to complete
                results = [future.result() for future in futures]
                
                end_time = time.time()
            
            execution_time = end_time - start_time
            assert execution_time < 10.0  # Should complete within 10 seconds
            assert len(results) == 20


class TestAPIPerformance:
    """Test API endpoint performance."""
    
    @pytest.fixture
    def client(self):
        """Create test client for performance testing."""
        from fastapi.testclient import TestClient
        from fastapi import FastAPI
        from app.model_manager.app.router import router
        
        app = FastAPI()
        app.include_router(router, prefix="/models")
        return TestClient(app)
    
    def test_available_models_endpoint_performance(self, client):
        """Test performance of /models/available endpoint."""
        with patch('app.model_manager.app.controllers.model_controller._catalog_service') as mock_service:
            # Mock large response
            large_response = PagedResult(
                items=[],
                total=10000,
                page=1,
                page_size=50
            )
            mock_service.list_available.return_value = large_response
            
            start_time = time.time()
            response = client.get("/models/available")
            end_time = time.time()
            
            execution_time = end_time - start_time
            assert response.status_code == 200
            assert execution_time < 1.0  # Should respond within 1 second
    
    def test_concurrent_api_requests(self, client):
        """Test concurrent API request handling."""
        with patch('app.model_manager.app.controllers.model_controller._inventory_service') as mock_service:
            mock_service.list_installed.return_value = []
            
            def make_request():
                return client.get("/models/installed")
            
            with ThreadPoolExecutor(max_workers=50) as executor:
                start_time = time.time()
                
                # Submit 100 concurrent requests
                futures = [executor.submit(make_request) for _ in range(100)]
                
                # Wait for all requests to complete
                responses = [future.result() for future in futures]
                
                end_time = time.time()
            
            execution_time = end_time - start_time
            assert execution_time < 10.0  # Should handle 100 requests within 10 seconds
            assert all(r.status_code == 200 for r in responses)
    
    def test_memory_usage_under_load(self, client):
        """Test memory usage under API load."""
        process = psutil.Process()
        initial_memory = process.memory_info().rss
        
        with patch('app.model_manager.app.controllers.model_controller._catalog_service') as mock_service:
            mock_service.list_available.return_value = PagedResult(
                items=[],
                total=0,
                page=1,
                page_size=50
            )
            
            # Make many requests
            for _ in range(1000):
                response = client.get("/models/available")
                assert response.status_code == 200
            
            # Force garbage collection
            gc.collect()
            
            final_memory = process.memory_info().rss
            memory_increase = final_memory - initial_memory
            
            # Memory increase should be reasonable (less than 100MB)
            assert memory_increase < 100 * 1024 * 1024


class TestCachePerformance:
    """Test cache performance characteristics."""
    
    def test_cache_hit_performance(self, ollama_provider, mock_cache):
        """Test cache hit performance."""
        # Setup cache with data
        cached_data = {
            "items": [{"provider": "OLLAMA", "name": f"model_{i}", "version": None, "metadata": {}} for i in range(100)]
        }
        mock_cache.get.return_value = cached_data
        
        # Measure cache hit performance
        times = []
        for _ in range(100):
            start_time = time.time()
            ollama_provider.get_catalog()
            end_time = time.time()
            times.append(end_time - start_time)
        
        # Cache hits should be consistently fast
        avg_time = sum(times) / len(times)
        max_time = max(times)
        
        assert avg_time < 0.01  # Average less than 10ms
        assert max_time < 0.05  # Max less than 50ms
    
    def test_cache_miss_performance(self, ollama_provider, mock_cache, mock_process):
        """Test cache miss performance."""
        mock_cache.get.return_value = None  # Cache miss
        
        # Mock network requests
        result = Mock()
        result.exit_code = 0
        result.stdout = "llama3:latest\t4.7GB\n"
        mock_process.run.return_value = result
        
        with patch('urllib.request.urlopen') as mock_urlopen:
            mock_response = Mock()
            mock_response.read.return_value = b'<a href="/library/llama3">llama3</a>'
            mock_urlopen.return_value.__enter__.return_value = mock_response
            
            start_time = time.time()
            ollama_provider.get_catalog()
            end_time = time.time()
        
        execution_time = end_time - start_time
        # Cache miss should still complete reasonably fast
        assert execution_time < 5.0


class TestScalabilityLimits:
    """Test scalability limits and edge cases."""
    
    def test_maximum_concurrent_installations(self, whisper_provider, mock_filesystem):
        """Test handling maximum concurrent installations."""
        mock_filesystem.acquire_lock.return_value = True
        mock_filesystem.exists.return_value = False
        
        def install_model(model_name):
            try:
                return whisper_provider.install(f"model_{model_name}")
            except Exception as e:
                return str(e)
        
        with ThreadPoolExecutor(max_workers=20) as executor:
            start_time = time.time()
            
            # Try to install 20 models concurrently
            futures = [executor.submit(install_model, i) for i in range(20)]
            results = [future.result() for future in futures]
            
            end_time = time.time()
        
        execution_time = end_time - start_time
        # Should handle concurrent installations without blocking indefinitely
        assert execution_time < 30.0
        
        # Some should succeed, some might fail due to locking
        successful = [r for r in results if not isinstance(r, str)]
        assert len(successful) > 0
    
    def test_large_configuration_handling(self, whisper_provider, mock_filesystem):
        """Test handling of large configuration objects."""
        # Create large configuration
        large_config = {
            f"parameter_{i}": f"value_{i}" * 100
            for i in range(1000)
        }
        
        start_time = time.time()
        
        try:
            whisper_provider.configure("tiny", large_config)
            end_time = time.time()
            
            execution_time = end_time - start_time
            # Should handle large configs within reasonable time
            assert execution_time < 2.0
            
        except Exception as e:
            # Should either succeed or fail gracefully with validation error
            assert "validation" in str(e).lower() or "size" in str(e).lower()
    
    def test_filesystem_stress(self, mock_filesystem):
        """Test filesystem operations under stress."""
        inventory_service = InventoryService(mock_filesystem)
        
        # Mock filesystem with many operations
        call_count = 0
        
        def slow_exists(path):
            nonlocal call_count
            call_count += 1
            # Simulate slow filesystem
            time.sleep(0.001)
            return call_count <= 100
        
        mock_filesystem.exists.side_effect = slow_exists
        mock_filesystem.list_dirs.return_value = []
        
        start_time = time.time()
        models = inventory_service.list_installed()
        end_time = time.time()
        
        execution_time = end_time - start_time
        # Should complete even with slow filesystem
        assert execution_time < 5.0
        assert len(models) == 0


@pytest.mark.benchmark
class TestBenchmarks:
    """Benchmark tests for performance regression detection."""
    
    def test_catalog_retrieval_benchmark(self, whisper_provider, benchmark):
        """Benchmark catalog retrieval performance."""
        def get_catalog():
            return whisper_provider.get_catalog()
        
        result = benchmark(get_catalog)
        assert len(result) >= 0
    
    def test_model_installation_benchmark(self, whisper_provider, mock_filesystem, benchmark):
        """Benchmark model installation performance."""
        mock_filesystem.acquire_lock.return_value = True
        mock_filesystem.exists.return_value = False
        
        def install_model():
            return whisper_provider.install("tiny")
        
        result = benchmark(install_model)
        assert result.status.value == "INSTALLED"
    
    def test_configuration_benchmark(self, whisper_provider, benchmark):
        """Benchmark model configuration performance."""
        config = {
            "device": "cpu",
            "language": "en",
            "temperature": 0.7
        }
        
        def configure_model():
            return whisper_provider.configure("tiny", config)
        
        benchmark(configure_model)
