"""
Resource exception classes for the LONI backend application.
"""

from typing import Any, Dict, Optional

from .base import AppException


class NotFoundError(AppException):
    """
    Exception raised when a requested resource is not found.
    
    Used for 404 errors when users, canvases, or other entities
    cannot be located in the database.
    """
    
    def __init__(
        self,
        message: str = "Resource not found",
        resource_type: Optional[str] = None,
        resource_id: Optional[str] = None
    ):
        """Initialize not found exception."""
        details = {}
        if resource_type:
            details["resource_type"] = resource_type
        if resource_id:
            details["resource_id"] = resource_id
            
        super().__init__(
            message=message,
            error_code="NOT_FOUND",
            details=details,
            status_code=404
        )