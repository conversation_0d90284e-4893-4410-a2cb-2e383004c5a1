#!/bin/bash
# Script to run backend tests with logging

# Set variables
PROJECT_ROOT="/mnt/e/Projects/lonors/loni"
BACKEND_DIR="$PROJECT_ROOT/apps/backend"
LOG_FILE="$PROJECT_ROOT/apps/data/logs/devtools-backend-tests.log"

# Create log file if it doesn't exist
mkdir -p "$PROJECT_ROOT/apps/data/logs"
touch "$LOG_FILE"

# Function to log messages
log_message() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

# Start test execution
log_message "Starting backend tests execution"

# Change to backend directory
cd "$BACKEND_DIR" || { log_message "ERROR: Failed to change to backend directory"; exit 1; }

# Run tests with uv
log_message "Running tests with uv..."
uv run pytest --cov=app tests/ 2>&1 | tee -a "$LOG_FILE"

# Check exit status
if [ ${PIPESTATUS[0]} -eq 0 ]; then
    log_message "Backend tests completed successfully"
else
    log_message "ERROR: Backend tests failed"
    exit 1
fi