"""Initial migration

Revision ID: 1234567890ab
Revises: 
Create Date: 2025-08-20 10:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '1234567890ab'
down_revision = None
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Create users table
    op.create_table('users',
        sa.<PERSON>umn('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('email', sa.String(length=255), nullable=False),
        sa.Column('username', sa.String(length=50), nullable=False),
        sa.Column('hashed_password', sa.String(length=255), nullable=False),
        sa.Column('is_active', sa.<PERSON>(), nullable=False),
        sa.Column('is_verified', sa.<PERSON>(), nullable=False),
        sa.Column('is_superuser', sa.<PERSON>(), nullable=False),
        sa.Column('full_name', sa.String(length=255), nullable=True),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('email'),
        sa.UniqueConstraint('username')
    )
    op.create_index(op.f('ix_users_email'), 'users', ['email'], unique=False)
    op.create_index(op.f('ix_users_username'), 'users', ['username'], unique=False)

    # Create canvases table
    op.create_table('canvases',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('name', sa.String(length=255), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('is_public', sa.Boolean(), nullable=False),
        sa.Column('tags', postgresql.JSONB(astext_type=sa.Text()), nullable=False),
        sa.Column('owner_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.ForeignKeyConstraint(['owner_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_canvases_name'), 'canvases', ['name'], unique=False)
    op.create_index(op.f('ix_canvases_owner_id'), 'canvases', ['owner_id'], unique=False)

    # Create nodes table
    op.create_table('nodes',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('type', sa.String(length=50), nullable=False),
        sa.Column('title', sa.String(length=255), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('position', postgresql.JSONB(astext_type=sa.Text()), nullable=False),
        sa.Column('size', postgresql.JSONB(astext_type=sa.Text()), nullable=False),
        sa.Column('data', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('canvas_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.ForeignKeyConstraint(['canvas_id'], ['canvases.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_nodes_canvas_id'), 'nodes', ['canvas_id'], unique=False)
    op.create_index(op.f('ix_nodes_type'), 'nodes', ['type'], unique=False)

    # Create connections table
    op.create_table('connections',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('connection_type', sa.String(length=50), nullable=False),
        sa.Column('label', sa.String(length=255), nullable=True),
        sa.Column('data', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('source_node_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('target_node_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('canvas_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.ForeignKeyConstraint(['canvas_id'], ['canvases.id'], ),
        sa.ForeignKeyConstraint(['source_node_id'], ['nodes.id'], ),
        sa.ForeignKeyConstraint(['target_node_id'], ['nodes.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_connections_canvas_id'), 'connections', ['canvas_id'], unique=False)
    op.create_index(op.f('ix_connections_connection_type'), 'connections', ['connection_type'], unique=False)
    op.create_index(op.f('ix_connections_source_node_id'), 'connections', ['source_node_id'], unique=False)
    op.create_index(op.f('ix_connections_target_node_id'), 'connections', ['target_node_id'], unique=False)


def downgrade() -> None:
    # Drop tables in reverse order
    op.drop_table('connections')
    op.drop_table('nodes')
    op.drop_table('canvases')
    op.drop_table('users')