import { AppLayout } from "@/components/layout/AppLayout";
import { Card } from "@/components/common/Card";
import { Button } from "@/components/common/Button";
import { useApi } from "@/lib/hooks/useApi";
import { useState, useRef, DragEvent } from "react";
import { toast } from "sonner";
import {
  Upload,
  FileAudio,
  X,
  CheckCircle,
  AlertCircle,
  Clock,
  Settings
} from "lucide-react";

interface TranscriptionResult {
  transcription: string;
  language?: string;
  duration?: number;
  word_count?: number;
  confidence?: number;
}

export default function TranscribePage() {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [dragActive, setDragActive] = useState(false);
  const [transcription, setTranscription] = useState<TranscriptionResult | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const { execute: transcribeAudio, isLoading } = useApi();

  const supportedFormats = [
    'mp3', 'wav', 'flac', 'm4a', 'ogg', 'webm'
  ];

  const handleFileSelect = (file: File) => {
    const fileExtension = file.name.split('.').pop()?.toLowerCase();
    if (!fileExtension || !supportedFormats.includes(fileExtension)) {
      toast.error(`Unsupported file format. Please use: ${supportedFormats.join(', ')}`);
      return;
    }

    if (file.size > 10 * 1024 * 1024) { // 10MB limit
      toast.error("File size too large. Maximum size is 10MB.");
      return;
    }

    setSelectedFile(file);
    setTranscription(null);
  };

  const handleDrop = (e: DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    const files = e.dataTransfer.files;
    if (files && files[0]) {
      handleFileSelect(files[0]);
    }
  };

  const handleDrag = (e: DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  };

  const handleFileInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files[0]) {
      handleFileSelect(files[0]);
    }
  };

  const removeFile = () => {
    setSelectedFile(null);
    setTranscription(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleTranscribe = async () => {
    if (!selectedFile) return;

    const formData = new FormData();
    formData.append('file', selectedFile);

    // Note: In a real implementation, you'd need to handle file uploads properly
    // For now, we'll simulate the transcription process
    const response = await transcribeAudio('/api/v1/transcribe', 'POST', {
      audio_path: selectedFile.name // This would be the actual file path on the server
    });

    if (response.success) {
      setTranscription({
        transcription: response.data?.transcription || "Sample transcription result...",
        language: "en",
        duration: 120,
        word_count: 150,
        confidence: 0.95
      });
      toast.success("Transcription completed successfully!");
    } else {
      toast.error("Transcription failed. Please try again.");
    }
  };

  return (
    <AppLayout>
      <div className="space-y-8">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold">Audio Transcription</h1>
            <p className="text-muted-foreground">Upload audio files and get accurate transcriptions</p>
          </div>
          <Button variant="outline" size="sm">
            <Settings className="h-4 w-4 mr-2" />
            Settings
          </Button>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Upload Section */}
          <div className="space-y-6">
            <Card className="p-6">
              <h2 className="text-xl font-semibold mb-4">Upload Audio File</h2>

              {!selectedFile ? (
                <div
                  className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
                    dragActive
                      ? 'border-primary bg-primary/5'
                      : 'border-muted-foreground/25 hover:border-primary/50'
                  }`}
                  onDrop={handleDrop}
                  onDragEnter={handleDrag}
                  onDragLeave={handleDrag}
                  onDragOver={handleDrag}
                >
                  <Upload className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <p className="text-lg font-medium mb-2">Drop your audio file here</p>
                  <p className="text-muted-foreground mb-4">
                    or click to browse files
                  </p>
                  <Button
                    onClick={() => fileInputRef.current?.click()}
                    variant="outline"
                  >
                    Choose File
                  </Button>
                  <input
                    ref={fileInputRef}
                    type="file"
                    accept={supportedFormats.map(format => `audio/${format}`).join(',')}
                    onChange={handleFileInput}
                    className="hidden"
                  />
                </div>
              ) : (
                <div className="border rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <FileAudio className="h-8 w-8 text-primary" />
                      <div>
                        <p className="font-medium">{selectedFile.name}</p>
                        <p className="text-sm text-muted-foreground">
                          {(selectedFile.size / 1024 / 1024).toFixed(2)} MB
                        </p>
                      </div>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={removeFile}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              )}

              <div className="mt-4">
                <p className="text-sm text-muted-foreground">
                  Supported formats: {supportedFormats.join(', ')} (max 10MB)
                </p>
              </div>
            </Card>

            <Card className="p-6">
              <h2 className="text-xl font-semibold mb-4">Transcription Settings</h2>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium mb-2">Language</label>
                  <select className="w-full p-2 border border-input bg-background rounded-md focus:outline-none focus:ring-2 focus:ring-ring">
                    <option value="auto">Auto Detect</option>
                    <option value="en">English</option>
                    <option value="es">Spanish</option>
                    <option value="fr">French</option>
                    <option value="de">German</option>
                    <option value="it">Italian</option>
                    <option value="pt">Portuguese</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium mb-2">Model</label>
                  <select className="w-full p-2 border border-input bg-background rounded-md focus:outline-none focus:ring-2 focus:ring-ring">
                    <option value="base">Whisper Base (Fast)</option>
                    <option value="small">Whisper Small (Balanced)</option>
                    <option value="medium">Whisper Medium (Accurate)</option>
                    <option value="large">Whisper Large (Most Accurate)</option>
                  </select>
                </div>
                <div className="flex items-center gap-2">
                  <input type="checkbox" id="timestamps" className="rounded" />
                  <label htmlFor="timestamps" className="text-sm">Include timestamps</label>
                </div>
              </div>
            </Card>

            <Button
              onClick={handleTranscribe}
              disabled={!selectedFile || isLoading}
              className="w-full"
              size="lg"
              gradient
            >
              {isLoading ? (
                <>
                  <Clock className="h-4 w-4 mr-2 animate-spin" />
                  Transcribing...
                </>
              ) : (
                <>
                  <FileAudio className="h-4 w-4 mr-2" />
                  Start Transcription
                </>
              )}
            </Button>
          </div>

          {/* Results Section */}
          <div className="space-y-6">
            <Card className="p-6">
              <h2 className="text-xl font-semibold mb-4">Transcription Results</h2>

              {!transcription ? (
                <div className="text-center py-8 text-muted-foreground">
                  <FileAudio className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>Upload an audio file and start transcription to see results here</p>
                </div>
              ) : (
                <div className="space-y-4">
                  <div className="flex items-center gap-2 text-green-600 mb-4">
                    <CheckCircle className="h-5 w-5" />
                    <span className="font-medium">Transcription Complete</span>
                  </div>

                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                    <div className="text-center p-3 bg-muted rounded-lg">
                      <p className="text-2xl font-bold text-primary">
                        {Math.round((transcription.duration || 0) / 60)}m
                      </p>
                      <p className="text-xs text-muted-foreground">Duration</p>
                    </div>
                    <div className="text-center p-3 bg-muted rounded-lg">
                      <p className="text-2xl font-bold text-primary">
                        {transcription.word_count}
                      </p>
                      <p className="text-xs text-muted-foreground">Words</p>
                    </div>
                    <div className="text-center p-3 bg-muted rounded-lg">
                      <p className="text-2xl font-bold text-primary">
                        {transcription.language?.toUpperCase()}
                      </p>
                      <p className="text-xs text-muted-foreground">Language</p>
                    </div>
                    <div className="text-center p-3 bg-muted rounded-lg">
                      <p className="text-2xl font-bold text-primary">
                        {Math.round((transcription.confidence || 0) * 100)}%
                      </p>
                      <p className="text-xs text-muted-foreground">Confidence</p>
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium mb-2">Transcription</label>
                    <div className="border rounded-lg p-4 bg-muted/30 min-h-[200px] max-h-[400px] overflow-y-auto">
                      <p className="text-sm whitespace-pre-wrap">
                        {transcription.transcription}
                      </p>
                    </div>
                  </div>

                  <div className="flex gap-2 pt-4">
                    <Button size="sm" variant="outline">
                      Copy Text
                    </Button>
                    <Button size="sm" variant="outline">
                      Download
                    </Button>
                    <Button size="sm" variant="outline">
                      Export to Word
                    </Button>
                  </div>
                </div>
              )}
            </Card>
          </div>
        </div>
      </div>
    </AppLayout>
  );
}

