# LONI Platform - Qwen Code Context

## Project Overview

LONI (Logical Organization and Navigation Interface) is a comprehensive software platform for visualizing software architecture, UI/UX, logic, data schemas, and flow using an interactive canvas interface. The platform consists of a FastAPI backend, Next.js frontend, and supporting infrastructure services.

The project follows modern development practices with:
- Test-Driven Development (TDD) using London School methodology
- Pre-commit hooks for code quality enforcement
- Docker-based containerization for consistent deployment
- Separation of code and data directories
- Comprehensive logging and monitoring

## Architecture

### Backend (Python/FastAPI)
- **Framework**: FastAPI with async support
- **Database**: PostgreSQL with SQLAlchemy ORM
- **Vector Database**: Qdrant for AI/ML embeddings
- **Authentication**: JWT-based system
- **Package Management**: UV (recommended) or pip
- **Testing**: Pytest with London School TDD approach

### Frontend (TypeScript/Next.js)
- **Framework**: Next.js with App Router
- **Styling**: Tailwind CSS with shadcn/ui components
- **State Management**: React Context Providers
- **Architecture**: Feature Slice Design (FSD)
- **Package Management**: Bun
- **Testing**: Jest with London School TDD approach

### Infrastructure
- **Containerization**: Docker Compose setup
- **Services**: PostgreSQL, Qdrant, Backend, Frontend
- **Reverse Proxy**: Nginx configuration
- **Deployment**: Production-ready configurations

### Data Management
- **Persistent Storage**: Dedicated data directory structure
- **Logs**: Structured logging with rotation
- **Models**: AI/ML model storage
- **Metadata**: System configuration and metadata

## Directory Structure

```
loni/
├── apps/
│   ├── backend/          # FastAPI backend service
│   ├── frontend/         # Next.js frontend application
│   ├── infra/            # Docker Compose infrastructure
│   └── data/             # Persistent data storage
├── scripts/
│   ├── devtools/         # Development utility scripts
│   └── tdd/              # TDD workflow automation
├── docs/                 # Documentation files
└── memory/               # Memory storage for development tools
```

## Development Workflow

### Initial Setup

1. **Backend Setup**:
   ```bash
   cd apps/backend
   # Using UV (recommended)
   curl -LsSf https://astral.sh/uv/install.sh | sh
   uv sync
   uv shell
   ```

2. **Frontend Setup**:
   ```bash
   cd apps/frontend
   bun install
   ```

3. **Infrastructure Setup**:
   ```bash
   cd apps/infra
   cp env.example .env
   # Update .env with your configuration
   ```

### Development Commands

#### Backend Development
```bash
# Run backend locally (recommended for development)
cd apps/backend
uv run uvicorn app.main:app --reload

# Run tests
cd apps/backend
pytest

# Run with coverage
pytest --cov=app tests/

# Linting and formatting
./run_linting.sh
./run_formatting.sh
./run_typecheck.sh
```

#### Frontend Development
```bash
# Run frontend locally (recommended for development)
cd apps/frontend
bun run dev

# Run tests
bun test

# Run with coverage
bun test --coverage

# Linting and formatting
bun run lint
bun run format
bun run type-check
```

#### Infrastructure Management
```bash
# Recommended development setup (Docker backend + local frontend)
cd apps/infra
docker-compose up -d postgres qdrant backend
# Then run frontend locally as shown above

# Full Docker setup (production)
docker-compose up -d

# View logs
docker-compose logs [service]

# Restart services
docker-compose restart [service]
```

### Test-Driven Development

The project implements London School TDD with automated workflows:

1. **Setup TDD Infrastructure**:
   ```bash
   # Backend
   cd apps/backend
   python3 ../../scripts/tdd/tdd-workflow.py --component backend
   
   # Frontend
   cd apps/frontend
   node ../../scripts/tdd/setup-frontend-testing.js
   ```

2. **Run TDD Workflow**:
   ```bash
   # Complete cycle
   python3 scripts/tdd/tdd-workflow.py --component all
   
   # Watch mode
   python3 scripts/tdd/tdd-workflow.py --watch
   ```

3. **Quality Gates**:
   - Backend: 80% coverage minimum
   - Frontend: 75% coverage minimum

### Code Quality Enforcement

Pre-commit hooks are configured for automatic code quality checks:

```bash
# Install pre-commit hooks
pip install pre-commit
pre-commit install
```

Hooks include:
- Python formatting (ruff)
- Python type checking (mypy)
- Python security scanning (bandit)
- JavaScript/TypeScript linting (eslint)
- Code formatting (prettier)
- Docker linting (hadolint)
- Security scanning (detect-secrets)

## Building and Deployment

### Backend
```bash
# Build Docker image
cd apps/backend
docker build -t loni-backend .

# Run in production
uvicorn app.main:app --host 0.0.0.0 --port 8000
```

### Frontend
```bash
# Build for production
cd apps/frontend
bun run build

# Start production server
bun start
```

### Infrastructure
```bash
# Build all images
cd apps/infra
docker-compose build

# Start in production mode
docker-compose --profile prod up -d
```

## API Endpoints

### Health Checks
- `GET /health` - Basic health check
- `GET /api/v1/health/` - Service health status
- `GET /api/v1/health/detailed` - Detailed health with dependencies

### Authentication
- `POST /api/v1/auth/login` - Login and receive access token
- `POST /api/v1/auth/register` - Register new user
- `GET /api/v1/auth/me` - Get current user information

### Canvas Management
- `GET /api/v1/canvas/` - List user's canvases
- `POST /api/v1/canvas/` - Create new canvas
- `GET /api/v1/canvas/{canvas_id}` - Get canvas details
- `PUT /api/v1/canvas/{canvas_id}` - Update canvas
- `DELETE /api/v1/canvas/{canvas_id}` - Delete canvas

### Visualization
- `POST /api/v1/visualization/analyze` - Analyze code files
- `POST /api/v1/visualization/generate` - Generate visualization
- `GET /api/v1/visualization/templates` - List templates

## Key Features

### Interactive Canvas
- Drag-and-drop interface for creating software visualizations
- Support for UI, Logic, Data, and Flow components
- Keyboard shortcuts for efficient navigation
- Zoom and pan functionality

### Authentication & Authorization
- JWT-based authentication system
- User registration and management
- Role-based access control (planned)

### Code Analysis
- Static code analysis for multiple programming languages
- AI-powered visualization generation
- Export/import capabilities

## Best Practices

1. **Development Environment**:
   - Use Docker for backend services
   - Run frontend locally for hot reload
   - Enable pre-commit hooks

2. **Code Quality**:
   - Follow TDD with London School methodology
   - Maintain high test coverage (80%+ backend, 75%+ frontend)
   - Use type hints in Python
   - Follow established naming conventions

3. **Documentation**:
   - Keep README files updated
   - Document API endpoints
   - Maintain TODO lists for tracking progress

4. **Security**:
   - Change default passwords in production
   - Disable debug mode in production
   - Configure CORS properly
   - Regular security scanning

## Troubleshooting

### Common Issues

1. **Frontend Container Issues**:
   - Check logs: `docker-compose logs frontend-dev`
   - Rebuild: `docker-compose build frontend-dev`
   - Use local development instead

2. **Backend API Issues**:
   - Check health: `curl http://localhost:8000/health`
   - View docs: http://localhost:8000/docs

3. **Database Connection Issues**:
   - Check status: `docker-compose exec postgres pg_isready`
   - Reset database: `docker-compose down postgres && docker volume rm infra_postgres_data`

4. **Port Conflicts**:
   - Check usage: `netstat -tulpn | grep :[port]`
   - Change ports in `.env` file

## Future Development

Key areas for future development include:
- Implement shared canvas functionality
- Add role-based access control for admin features
- Review and optimize Docker configurations
- Implement proper backup strategy for data volumes
- Add monitoring and alerting setup
- Implement CI/CD pipeline
- Add comprehensive documentation for all APIs