'use client';

import { useState, useEffect, useCallback } from 'react';
import { API_ENDPOINTS } from '@/lib/constants';
import { useApi } from './useApi';
import type {
  ProjectScanRequest,
  ScanResult,
  ScanProgress,
  DashboardStats,
  ScanIssue,
  ExportOptions,
  ScanFilter
} from '@/lib/types';

export function useProjectScanner() {
  const { apiCall, loading, error } = useApi();
  
  const [activeScans, setActiveScans] = useState<Map<string, ScanProgress>>(new Map());
  const [scanHistory, setScanHistory] = useState<ScanResult[]>([]);
  const [dashboardStats, setDashboardStats] = useState<DashboardStats | null>(null);

  // Start a new project scan
  const startScan = useCallback(async (request: Omit<ProjectScanRequest, 'id' | 'createdAt' | 'status'>) => {
    try {
      const response = await apiCall<{ scanId: string }>({
        endpoint: API_ENDPOINTS.PROJECT_SCANNER.SCAN,
        method: 'POST',
        data: request
      });
      
      if (response.success && response.data) {
        // Start polling for progress
        pollScanProgress(response.data.scanId);
        return response.data.scanId;
      }
      
      throw new Error(response.error || 'Failed to start scan');
    } catch (err) {
      console.error('Error starting scan:', err);
      throw err;
    }
  }, [apiCall]);

  // Poll scan progress
  const pollScanProgress = useCallback(async (scanId: string) => {
    const poll = async () => {
      try {
        const response = await apiCall<ScanProgress>({
          endpoint: `${API_ENDPOINTS.PROJECT_SCANNER.STATUS}/${scanId}`,
          method: 'GET'
        });
        
        if (response.success && response.data) {
          const progress = response.data;
          setActiveScans(prev => new Map(prev.set(scanId, progress)));
          
          // If scan is complete, stop polling and fetch results
          if (progress.status === 'completed' || progress.status === 'failed') {
            setActiveScans(prev => {
              const newMap = new Map(prev);
              newMap.delete(scanId);
              return newMap;
            });
            
            if (progress.status === 'completed') {
              await fetchScanResult(scanId);
            }
            
            return;
          }
          
          // Continue polling if still active
          if (progress.status === 'scanning' || progress.status === 'analyzing' || progress.status === 'initializing') {
            setTimeout(poll, 2000); // Poll every 2 seconds
          }
        }
      } catch (err) {
        console.error('Error polling scan progress:', err);
        // Remove from active scans on error
        setActiveScans(prev => {
          const newMap = new Map(prev);
          newMap.delete(scanId);
          return newMap;
        });
      }
    };
    
    poll();
  }, [apiCall]);

  // Fetch scan result
  const fetchScanResult = useCallback(async (scanId: string) => {
    try {
      const response = await apiCall<ScanResult>({
        endpoint: `${API_ENDPOINTS.PROJECT_SCANNER.RESULTS}/${scanId}`,
        method: 'GET'
      });
      
      if (response.success && response.data) {
        setScanHistory(prev => {
          const exists = prev.some(scan => scan.scanId === scanId);
          if (exists) {
            return prev.map(scan => scan.scanId === scanId ? response.data! : scan);
          }
          return [response.data!, ...prev];
        });
        
        return response.data;
      }
    } catch (err) {
      console.error('Error fetching scan result:', err);
      throw err;
    }
  }, [apiCall]);

  // Cancel active scan
  const cancelScan = useCallback(async (scanId: string) => {
    try {
      const response = await apiCall({
        endpoint: `${API_ENDPOINTS.PROJECT_SCANNER.CANCEL}/${scanId}`,
        method: 'POST'
      });
      
      if (response.success) {
        setActiveScans(prev => {
          const newMap = new Map(prev);
          newMap.delete(scanId);
          return newMap;
        });
      }
      
      return response.success;
    } catch (err) {
      console.error('Error cancelling scan:', err);
      return false;
    }
  }, [apiCall]);

  // Fetch scan history
  const fetchScanHistory = useCallback(async (limit?: number) => {
    try {
      const response = await apiCall<ScanResult[]>({
        endpoint: API_ENDPOINTS.PROJECT_SCANNER.HISTORY,
        method: 'GET',
        params: limit ? { limit: limit.toString() } : undefined
      });
      
      if (response.success && response.data) {
        setScanHistory(response.data);
        return response.data;
      }
    } catch (err) {
      console.error('Error fetching scan history:', err);
      throw err;
    }
  }, [apiCall]);

  // Fetch dashboard statistics
  const fetchDashboardStats = useCallback(async () => {
    try {
      const response = await apiCall<DashboardStats>({
        endpoint: API_ENDPOINTS.PROJECT_SCANNER.STATS,
        method: 'GET'
      });
      
      if (response.success && response.data) {
        setDashboardStats(response.data);
        return response.data;
      }
    } catch (err) {
      console.error('Error fetching dashboard stats:', err);
      throw err;
    }
  }, [apiCall]);

  // Export scan results
  const exportScanResults = useCallback(async (scanId: string, options: ExportOptions) => {
    try {
      const response = await apiCall<{ downloadUrl: string }>({
        endpoint: `${API_ENDPOINTS.PROJECT_SCANNER.EXPORT}/${scanId}`,
        method: 'POST',
        data: options
      });
      
      if (response.success && response.data) {
        // Trigger download
        const link = document.createElement('a');
        link.href = response.data.downloadUrl;
        link.download = `scan-report-${scanId}.${options.format}`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        
        return true;
      }
      
      return false;
    } catch (err) {
      console.error('Error exporting scan results:', err);
      return false;
    }
  }, [apiCall]);

  // Filter and sort issues
  const filterIssues = useCallback((issues: ScanIssue[], filter: ScanFilter): ScanIssue[] => {
    let filtered = [...issues];
    
    // Apply severity filter
    if (filter.severities.length > 0) {
      filtered = filtered.filter(issue => filter.severities.includes(issue.severity));
    }
    
    // Apply category filter
    if (filter.categories.length > 0) {
      filtered = filtered.filter(issue => filter.categories.includes(issue.category));
    }
    
    // Apply file filter
    if (filter.files.length > 0) {
      filtered = filtered.filter(issue => filter.files.some(file => issue.file.includes(file)));
    }
    
    // Apply search query
    if (filter.searchQuery) {
      const query = filter.searchQuery.toLowerCase();
      filtered = filtered.filter(issue => 
        issue.title.toLowerCase().includes(query) ||
        issue.description.toLowerCase().includes(query) ||
        issue.file.toLowerCase().includes(query) ||
        issue.rule.toLowerCase().includes(query)
      );
    }
    
    // Apply sorting
    filtered.sort((a, b) => {
      const severityOrder = { critical: 4, high: 3, medium: 2, low: 1, info: 0 };
      
      switch (filter.sortBy) {
        case 'severity':
          const severityDiff = severityOrder[b.severity] - severityOrder[a.severity];
          return filter.sortOrder === 'asc' ? -severityDiff : severityDiff;
          
        case 'category':
          const categoryCompare = a.category.localeCompare(b.category);
          return filter.sortOrder === 'asc' ? categoryCompare : -categoryCompare;
          
        case 'file':
          const fileCompare = a.file.localeCompare(b.file);
          return filter.sortOrder === 'asc' ? fileCompare : -fileCompare;
          
        case 'line':
          const lineDiff = (a.line || 0) - (b.line || 0);
          return filter.sortOrder === 'asc' ? lineDiff : -lineDiff;
          
        default:
          return 0;
      }
    });
    
    return filtered;
  }, []);

  // Initialize dashboard data on mount
  useEffect(() => {
    fetchDashboardStats();
    fetchScanHistory(10); // Fetch recent 10 scans
  }, [fetchDashboardStats, fetchScanHistory]);

  return {
    // State
    activeScans: Array.from(activeScans.values()),
    scanHistory,
    dashboardStats,
    loading,
    error,
    
    // Actions
    startScan,
    cancelScan,
    fetchScanResult,
    fetchScanHistory,
    fetchDashboardStats,
    exportScanResults,
    filterIssues,
    
    // Utilities
    isScanning: activeScans.size > 0,
    getActiveScan: (scanId: string) => activeScans.get(scanId),
  };
}

// Hook for real-time scan updates via WebSocket (optional enhancement)
export function useScanWebSocket(scanId?: string) {
  const [wsProgress, setWsProgress] = useState<ScanProgress | null>(null);
  const [isConnected, setIsConnected] = useState(false);

  useEffect(() => {
    if (!scanId) return;

    // WebSocket implementation would go here
    // For now, we'll use polling via the main hook
    
    return () => {
      // Cleanup WebSocket connection
    };
  }, [scanId]);

  return {
    progress: wsProgress,
    isConnected
  };
}