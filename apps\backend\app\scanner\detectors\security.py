"""
Security Detector for the Project Scanner system.

This module detects security vulnerabilities including OWASP Top 10 issues,
hardcoded secrets, insecure configurations, and authentication/authorization problems.
"""

import re
import json
import ast
import base64
from pathlib import Path
from typing import Any, Dict, List, Optional, Set, Tuple
from urllib.parse import urlparse

from ..base_detector import RuleBasedDetector
from ..models import (
    DetectorResult,
    Issue,
    IssueSeverity,
    IssueType,
    RemediationSuggestion
)


class SecurityDetector(RuleBasedDetector):
    """
    Detects security vulnerabilities including:
    - OWASP Top 10 vulnerabilities
    - Hardcoded secrets and credentials
    - Insecure configurations
    - Authentication and authorization issues
    - Input validation problems
    - Cryptographic issues
    - Path traversal vulnerabilities
    - Code injection risks
    """
    
    def __init__(self, config):
        super().__init__("Security", config)
        self._load_secret_patterns()
    
    def get_supported_file_types(self) -> Set[str]:
        """Get supported file extensions."""
        return {
            '.py', '.js', '.ts', '.jsx', '.tsx', '.json', '.yaml', '.yml',
            '.ini', '.conf', '.config', '.env', '.properties', '.xml',
            '.java', '.php', '.rb', '.go', '.cs', '.cpp', '.c', '.h'
        }
    
    def _load_rules(self) -> Dict[str, Any]:
        """Load security detection rules based on OWASP Top 10."""
        return {
            # A01:2021 - Broken Access Control
            'missing_authentication': {
                'patterns': [
                    r'@app\.route\([^)]*\)\s*def\s+\w+\([^)]*\):(?!\s*@)',  # Flask route without auth decorator
                    r'@router\.(get|post|put|delete)\([^)]*\)\s*def\s+\w+\([^)]*\):(?!\s*@)',  # FastAPI route without auth
                    r'app\.(get|post|put|delete)\([^)]*,\s*function\s*\([^)]*\)\s*{',  # Express.js route without middleware
                ],
                'severity': IssueSeverity.HIGH,
                'owasp': 'A01:2021',
                'cwe': 'CWE-862',
                'description': 'Missing authentication on sensitive endpoints'
            },
            'privilege_escalation': {
                'patterns': [
                    r'sudo\s+',
                    r'su\s+-',
                    r'chmod\s+777',
                    r'setuid\s*\(',
                    r'exec\s*\(["\']sudo',
                ],
                'severity': IssueSeverity.CRITICAL,
                'owasp': 'A01:2021',
                'cwe': 'CWE-269',
                'description': 'Potential privilege escalation vulnerability'
            },
            
            # A02:2021 - Cryptographic Failures
            'weak_crypto': {
                'patterns': [
                    r'md5\s*\(',
                    r'sha1\s*\(',
                    r'DES\s*\(',
                    r'RC4\s*\(',
                    r'Random\s*\(\)',  # Using Random instead of SecureRandom
                    r'Math\.random\(\)',
                    r'random\.random\(\)',
                ],
                'severity': IssueSeverity.HIGH,
                'owasp': 'A02:2021',
                'cwe': 'CWE-327',
                'description': 'Weak cryptographic algorithm detected'
            },
            'hardcoded_crypto_key': {
                'patterns': [
                    r'key\s*=\s*["\'][0-9a-fA-F]{16,}["\']',
                    r'secret\s*=\s*["\'][0-9a-zA-Z+/]{16,}={0,2}["\']',
                    r'password\s*=\s*["\'][^"\']{8,}["\']',
                ],
                'severity': IssueSeverity.CRITICAL,
                'owasp': 'A02:2021',
                'cwe': 'CWE-798',
                'description': 'Hardcoded cryptographic key or password'
            },
            
            # A03:2021 - Injection
            'sql_injection': {
                'patterns': [
                    r'execute\s*\(\s*["\'].*%s.*["\']',
                    r'query\s*\(\s*["\'].*\+.*["\']',
                    r'SELECT\s+.*\+.*FROM',
                    r'cursor\.execute\s*\([^)]*%[^)]*\)',
                    r'db\.query\s*\([^)]*\+[^)]*\)',
                ],
                'severity': IssueSeverity.CRITICAL,
                'owasp': 'A03:2021',
                'cwe': 'CWE-89',
                'description': 'Potential SQL injection vulnerability'
            },
            'command_injection': {
                'patterns': [
                    r'os\.system\s*\([^)]*\+[^)]*\)',
                    r'subprocess\.call\s*\([^)]*\+[^)]*\)',
                    r'exec\s*\([^)]*\+[^)]*\)',
                    r'eval\s*\([^)]*input[^)]*\)',
                    r'shell_exec\s*\([^)]*\$_',
                    r'system\s*\([^)]*\$_',
                ],
                'severity': IssueSeverity.CRITICAL,
                'owasp': 'A03:2021',
                'cwe': 'CWE-78',
                'description': 'Potential command injection vulnerability'
            },
            'xss_vulnerability': {
                'patterns': [
                    r'innerHTML\s*=\s*[^;]*\+',
                    r'document\.write\s*\([^)]*\+[^)]*\)',
                    r'\.html\s*\([^)]*\+[^)]*\)',
                    r'render_template_string\s*\([^)]*\+[^)]*\)',
                ],
                'severity': IssueSeverity.HIGH,
                'owasp': 'A03:2021',
                'cwe': 'CWE-79',
                'description': 'Potential cross-site scripting (XSS) vulnerability'
            },
            
            # A04:2021 - Insecure Design
            'insecure_random': {
                'patterns': [
                    r'random\.randint\s*\(',
                    r'Math\.random\s*\(\)',
                    r'Random\s*\(\)\.next',
                    r'rand\s*\(\)',
                ],
                'severity': IssueSeverity.MEDIUM,
                'owasp': 'A04:2021',
                'cwe': 'CWE-338',
                'description': 'Insecure random number generation'
            },
            
            # A05:2021 - Security Misconfiguration
            'debug_enabled': {
                'patterns': [
                    r'DEBUG\s*=\s*True',
                    r'debug\s*:\s*true',
                    r'app\.debug\s*=\s*True',
                    r'ENV\s*=\s*["\']development["\']',
                ],
                'severity': IssueSeverity.HIGH,
                'owasp': 'A05:2021',
                'cwe': 'CWE-489',
                'description': 'Debug mode enabled in production'
            },
            'cors_wildcard': {
                'patterns': [
                    r'Access-Control-Allow-Origin.*\*',
                    r'allow_origins\s*=\s*\[["\*"]\]',
                    r'cors\s*\(\s*{\s*origin\s*:\s*true',
                    r'@CrossOrigin\s*\(\s*origins\s*=\s*["\']?\*["\']?\)',
                ],
                'severity': IssueSeverity.HIGH,
                'owasp': 'A05:2021',
                'cwe': 'CWE-942',
                'description': 'Overly permissive CORS configuration'
            },
            
            # A06:2021 - Vulnerable and Outdated Components
            'vulnerable_import': {
                'patterns': [
                    r'import pickle',
                    r'from pickle import',
                    r'import cPickle',
                    r'import marshal',
                    r'eval\s*\(',
                    r'exec\s*\(',
                ],
                'severity': IssueSeverity.HIGH,
                'owasp': 'A06:2021',
                'cwe': 'CWE-502',
                'description': 'Use of vulnerable or dangerous functions'
            },
            
            # A07:2021 - Identification and Authentication Failures
            'weak_password_policy': {
                'patterns': [
                    r'password.*len.*<\s*[1-7]',
                    r'if.*len\(password\).*<\s*[1-7]',
                    r'password\.length\s*<\s*[1-7]',
                ],
                'severity': IssueSeverity.MEDIUM,
                'owasp': 'A07:2021',
                'cwe': 'CWE-521',
                'description': 'Weak password policy'
            },
            'session_fixation': {
                'patterns': [
                    r'session\[.*\]\s*=.*without.*regenerate',
                    r'session_start\(\).*without.*session_regenerate_id',
                ],
                'severity': IssueSeverity.MEDIUM,
                'owasp': 'A07:2021',
                'cwe': 'CWE-384',
                'description': 'Potential session fixation vulnerability'
            },
            
            # A08:2021 - Software and Data Integrity Failures
            'unsafe_deserialization': {
                'patterns': [
                    r'pickle\.loads?\s*\(',
                    r'yaml\.load\s*\([^)]*Loader\s*=\s*yaml\.Loader',
                    r'json\.loads?\s*\([^)]*[^}]eval',
                    r'marshal\.loads?\s*\(',
                ],
                'severity': IssueSeverity.CRITICAL,
                'owasp': 'A08:2021',
                'cwe': 'CWE-502',
                'description': 'Unsafe deserialization vulnerability'
            },
            
            # A09:2021 - Security Logging and Monitoring Failures
            'no_security_logging': {
                'patterns': [
                    r'except\s*:.*pass',
                    r'except\s+\w+\s*:.*pass',
                    r'try\s*:.*except\s*:.*continue',
                ],
                'severity': IssueSeverity.LOW,
                'owasp': 'A09:2021',
                'cwe': 'CWE-778',
                'description': 'Missing security logging'
            },
            
            # A10:2021 - Server-Side Request Forgery
            'ssrf_vulnerability': {
                'patterns': [
                    r'requests\.get\s*\([^)]*input[^)]*\)',
                    r'urllib\.request\s*\([^)]*user_input[^)]*\)',
                    r'fetch\s*\([^)]*req\.body[^)]*\)',
                ],
                'severity': IssueSeverity.HIGH,
                'owasp': 'A10:2021',
                'cwe': 'CWE-918',
                'description': 'Potential server-side request forgery (SSRF)'
            },
            
            # Additional Security Issues
            'path_traversal': {
                'patterns': [
                    r'open\s*\([^)]*\.\.[^)]*\)',
                    r'file\s*\([^)]*\.\.[^)]*\)',
                    r'readFile\s*\([^)]*\.\.[^)]*\)',
                    r'include\s*\([^)]*\.\.[^)]*\)',
                ],
                'severity': IssueSeverity.HIGH,
                'cwe': 'CWE-22',
                'description': 'Potential path traversal vulnerability'
            },
            'file_upload_risk': {
                'patterns': [
                    r'\.save\s*\([^)]*filename[^)]*\)',
                    r'move_uploaded_file\s*\(',
                    r'file\.write\s*\([^)]*without.*validation',
                ],
                'severity': IssueSeverity.HIGH,
                'cwe': 'CWE-434',
                'description': 'Unrestricted file upload vulnerability'
            }
        }
    
    def _load_secret_patterns(self):
        """Load patterns for detecting hardcoded secrets."""
        self.secret_patterns = {
            'api_key': {
                'patterns': [
                    r'api[_-]?key\s*[:=]\s*["\'][0-9a-zA-Z]{20,}["\']',
                    r'apikey\s*[:=]\s*["\'][0-9a-zA-Z]{20,}["\']',
                ],
                'severity': IssueSeverity.HIGH
            },
            'aws_key': {
                'patterns': [
                    r'AKIA[0-9A-Z]{16}',
                    r'aws[_-]?access[_-]?key[_-]?id\s*[:=]\s*["\'][A-Z0-9]{20}["\']',
                    r'aws[_-]?secret[_-]?access[_-]?key\s*[:=]\s*["\'][A-Za-z0-9/+=]{40}["\']',
                ],
                'severity': IssueSeverity.CRITICAL
            },
            'private_key': {
                'patterns': [
                    r'-----BEGIN PRIVATE KEY-----',
                    r'-----BEGIN RSA PRIVATE KEY-----',
                    r'-----BEGIN DSA PRIVATE KEY-----',
                    r'-----BEGIN EC PRIVATE KEY-----',
                ],
                'severity': IssueSeverity.CRITICAL
            },
            'database_url': {
                'patterns': [
                    r'(mysql|postgresql|mongodb)://[^/\s]+:[^/\s]+@[^/\s]+',
                    r'DATABASE_URL\s*[:=]\s*["\'][^"\']+://[^"\']+["\']',
                ],
                'severity': IssueSeverity.HIGH
            },
            'jwt_secret': {
                'patterns': [
                    r'jwt[_-]?secret\s*[:=]\s*["\'][0-9a-zA-Z+/=]{20,}["\']',
                    r'secret[_-]?key\s*[:=]\s*["\'][0-9a-zA-Z+/=]{20,}["\']',
                ],
                'severity': IssueSeverity.HIGH
            },
            'oauth_token': {
                'patterns': [
                    r'access[_-]?token\s*[:=]\s*["\'][0-9a-zA-Z.-]{20,}["\']',
                    r'bearer\s+[0-9a-zA-Z.-]{20,}',
                ],
                'severity': IssueSeverity.HIGH
            }
        }
    
    def detect(self, project_path: Path) -> DetectorResult:
        """Detect security vulnerabilities."""
        self.start_detection()
        
        try:
            files = self.get_project_files(project_path)
            
            for file_path in files:
                content = self.read_file_safely(file_path)
                if content:
                    self._analyze_file(file_path, content, project_path)
                    self.files_processed += 1
            
            return self.finish_detection()
            
        except Exception as e:
            return self.finish_detection(success=False, error_message=str(e))
    
    def _analyze_file(self, file_path: Path, content: str, project_path: Path) -> None:
        """Analyze a single file for security issues."""
        relative_path = str(file_path.relative_to(project_path))
        
        # Check for hardcoded secrets first
        self._check_hardcoded_secrets(relative_path, content)
        
        # Apply security rules
        issues = self.apply_rules_to_file(file_path, content)
        for issue in issues:
            self.add_issue(issue)
        
        # File type specific checks
        if file_path.suffix == '.py':
            self._analyze_python_security(relative_path, content)
        elif file_path.suffix in {'.js', '.ts', '.jsx', '.tsx'}:
            self._analyze_javascript_security(relative_path, content)
        elif file_path.suffix in {'.json', '.yaml', '.yml'}:
            self._analyze_config_security(relative_path, content, file_path.suffix)
        elif file_path.suffix in {'.env', '.ini', '.conf', '.properties'}:
            self._analyze_env_security(relative_path, content)
    
    def _check_hardcoded_secrets(self, file_path: str, content: str) -> None:
        """Check for hardcoded secrets and credentials."""
        lines = content.splitlines()
        
        for secret_type, config in self.secret_patterns.items():
            for pattern in config['patterns']:
                for line_num, line in enumerate(lines, 1):
                    matches = re.finditer(pattern, line, re.IGNORECASE)
                    for match in matches:
                        # Skip if it looks like a comment or example
                        if self._is_likely_example(line, match.group()):
                            continue
                        
                        self.add_issue(self.create_issue(
                            IssueType.HARDCODED_SECRET,
                            config['severity'],
                            f"Hardcoded {secret_type.replace('_', ' ')} detected",
                            f"Found what appears to be a hardcoded {secret_type.replace('_', ' ')} in the source code. "
                            f"This is a security risk as credentials should never be stored in source code.",
                            file_path,
                            line_number=line_num,
                            column_number=match.start(),
                            rule_id=f'hardcoded_{secret_type}',
                            confidence=0.8,
                            impact_score=90,
                            likelihood_score=95,
                            cwe_id='CWE-798',
                            owasp_category='A02:2021',
                            tags={'secrets', 'credentials'},
                            remediation=RemediationSuggestion(
                                title="Move secrets to environment variables",
                                description="Store sensitive credentials in environment variables or a secure secret management system.",
                                code_example=f"# Bad\n{secret_type.upper()} = 'hardcoded_value'\n\n# Good\n{secret_type.upper()} = os.environ.get('{secret_type.upper()}')",
                                effort_estimate="15 minutes",
                                references=[
                                    "https://owasp.org/www-project-top-ten/2017/A2_2017-Broken_Authentication",
                                    "https://cwe.mitre.org/data/definitions/798.html"
                                ]
                            )
                        ))
    
    def _is_likely_example(self, line: str, match: str) -> bool:
        """Check if a match is likely just an example or comment."""
        line_lower = line.lower()
        
        # Skip comments
        if line.strip().startswith('#') or line.strip().startswith('//'):
            return True
        
        # Skip common example values
        example_indicators = [
            'example', 'sample', 'test', 'demo', 'placeholder',
            'your_key_here', 'insert_key', 'replace_with'
        ]
        
        for indicator in example_indicators:
            if indicator in line_lower or indicator in match.lower():
                return True
        
        # Skip very short or obviously fake values
        if len(match.replace('"', '').replace("'", '').strip()) < 10:
            return True
        
        return False
    
    def _analyze_python_security(self, file_path: str, content: str) -> None:
        """Analyze Python-specific security issues."""
        try:
            tree = ast.parse(content)
            
            for node in ast.walk(tree):
                if isinstance(node, ast.Import):
                    for alias in node.names:
                        self._check_dangerous_import(alias.name, file_path, node.lineno)
                
                elif isinstance(node, ast.ImportFrom):
                    if node.module:
                        self._check_dangerous_import(node.module, file_path, node.lineno)
                
                elif isinstance(node, ast.Call):
                    self._check_dangerous_function_call(node, file_path)
        
        except SyntaxError:
            pass  # Already handled in code quality detector
    
    def _check_dangerous_import(self, module_name: str, file_path: str, line_number: int) -> None:
        """Check for dangerous imports."""
        dangerous_imports = {
            'pickle': {
                'severity': IssueSeverity.HIGH,
                'description': 'pickle module can execute arbitrary code during deserialization'
            },
            'marshal': {
                'severity': IssueSeverity.MEDIUM,
                'description': 'marshal module can be unsafe for untrusted data'
            },
            'subprocess': {
                'severity': IssueSeverity.LOW,
                'description': 'subprocess module requires careful input validation'
            }
        }
        
        if module_name in dangerous_imports:
            config = dangerous_imports[module_name]
            self.add_issue(self.create_issue(
                IssueType.VULNERABLE_DEPENDENCY,
                config['severity'],
                f"Potentially dangerous import: {module_name}",
                f"The {module_name} module {config['description']}. Ensure proper input validation.",
                file_path,
                line_number=line_number,
                rule_id=f'dangerous_import_{module_name}',
                impact_score=70 if config['severity'] == IssueSeverity.HIGH else 40,
                likelihood_score=60,
                cwe_id='CWE-502' if module_name == 'pickle' else 'CWE-78'
            ))
    
    def _check_dangerous_function_call(self, node: ast.Call, file_path: str) -> None:
        """Check for dangerous function calls."""
        if isinstance(node.func, ast.Name):
            func_name = node.func.id
            
            if func_name in ['eval', 'exec']:
                self.add_issue(self.create_issue(
                    IssueType.SECURITY_VULNERABILITY,
                    IssueSeverity.CRITICAL,
                    f"Dangerous function call: {func_name}",
                    f"The {func_name} function can execute arbitrary code and should be avoided.",
                    file_path,
                    line_number=node.lineno,
                    rule_id=f'dangerous_call_{func_name}',
                    impact_score=95,
                    likelihood_score=90,
                    cwe_id='CWE-94',
                    owasp_category='A03:2021'
                ))
    
    def _analyze_javascript_security(self, file_path: str, content: str) -> None:
        """Analyze JavaScript/TypeScript security issues."""
        lines = content.splitlines()
        
        for line_num, line in enumerate(lines, 1):
            # Check for dangerous functions
            if 'eval(' in line:
                self.add_issue(self.create_issue(
                    IssueType.SECURITY_VULNERABILITY,
                    IssueSeverity.CRITICAL,
                    "Use of eval() function",
                    "The eval() function can execute arbitrary JavaScript code and is a security risk.",
                    file_path,
                    line_number=line_num,
                    rule_id='javascript_eval',
                    impact_score=90,
                    likelihood_score=85,
                    cwe_id='CWE-94'
                ))
            
            # Check for innerHTML with concatenation
            if re.search(r'innerHTML\s*=\s*[^;]*\+', line):
                self.add_issue(self.create_issue(
                    IssueType.SECURITY_VULNERABILITY,
                    IssueSeverity.HIGH,
                    "Potential XSS via innerHTML",
                    "Using innerHTML with string concatenation can lead to XSS vulnerabilities.",
                    file_path,
                    line_number=line_num,
                    rule_id='javascript_xss_innerHTML',
                    impact_score=75,
                    likelihood_score=70,
                    cwe_id='CWE-79'
                ))
    
    def _analyze_config_security(self, file_path: str, content: str, file_ext: str) -> None:
        """Analyze configuration file security."""
        try:
            if file_ext == '.json':
                config = json.loads(content)
                self._check_config_values(config, file_path, 'json')
            elif file_ext in {'.yaml', '.yml'}:
                # Basic YAML security check without full parsing
                if '!!python/object' in content:
                    self.add_issue(self.create_issue(
                        IssueType.SECURITY_VULNERABILITY,
                        IssueSeverity.HIGH,
                        "Unsafe YAML deserialization",
                        "YAML file contains Python object serialization which can be exploited.",
                        file_path,
                        rule_id='unsafe_yaml',
                        impact_score=80,
                        likelihood_score=60,
                        cwe_id='CWE-502'
                    ))
        except (json.JSONDecodeError, Exception):
            pass  # Invalid JSON/YAML, skip detailed analysis
    
    def _check_config_values(self, config: dict, file_path: str, config_type: str, prefix: str = '') -> None:
        """Recursively check configuration values for security issues."""
        for key, value in config.items():
            full_key = f"{prefix}.{key}" if prefix else key
            
            if isinstance(value, dict):
                self._check_config_values(value, file_path, config_type, full_key)
            elif isinstance(value, str):
                # Check for suspicious configuration values
                key_lower = key.lower()
                if any(term in key_lower for term in ['password', 'secret', 'key', 'token']):
                    if len(value) > 8 and not value.startswith('${'):  # Not a variable reference
                        self.add_issue(self.create_issue(
                            IssueType.HARDCODED_SECRET,
                            IssueSeverity.MEDIUM,
                            f"Hardcoded secret in {config_type.upper()} config",
                            f"Configuration key '{full_key}' appears to contain a hardcoded secret.",
                            file_path,
                            rule_id=f'config_hardcoded_secret_{config_type}',
                            impact_score=60,
                            likelihood_score=70,
                            cwe_id='CWE-798'
                        ))
    
    def _analyze_env_security(self, file_path: str, content: str) -> None:
        """Analyze environment file security."""
        lines = content.splitlines()
        
        for line_num, line in enumerate(lines, 1):
            # Skip comments and empty lines
            if line.strip().startswith('#') or not line.strip():
                continue
            
            # Check for environment variables that might contain secrets
            if '=' in line:
                key, value = line.split('=', 1)
                key = key.strip()
                value = value.strip().strip('"\'')
                
                # Check if this looks like a real secret (not placeholder)
                if len(value) > 8 and not any(placeholder in value.lower() for placeholder in 
                    ['your_', 'insert_', 'replace_', 'example_', 'placeholder']):
                    
                    key_lower = key.lower()
                    if any(term in key_lower for term in ['password', 'secret', 'key', 'token']):
                        self.add_issue(self.create_issue(
                            IssueType.HARDCODED_SECRET,
                            IssueSeverity.LOW,  # Lower severity for env files as they're expected to contain secrets
                            f"Secret in environment file: {key}",
                            f"Environment file contains what appears to be a real secret for '{key}'. "
                            "Ensure this file is not committed to version control.",
                            file_path,
                            line_number=line_num,
                            rule_id='env_file_secret',
                            impact_score=40,
                            likelihood_score=80,
                            cwe_id='CWE-798',
                            remediation=RemediationSuggestion(
                                title="Exclude from version control",
                                description="Add this file to .gitignore and use template files for sharing configuration structure.",
                                effort_estimate="5 minutes"
                            )
                        ))
    
    def _apply_rule(self, rule_id: str, rule: Dict[str, Any], file_path: Path, content: str) -> List[Issue]:
        """Apply a specific security rule to file content."""
        issues = []
        relative_path = str(file_path.relative_to(file_path.parent.parent.parent))
        
        for pattern in rule['patterns']:
            matches = re.finditer(pattern, content, re.IGNORECASE | re.MULTILINE)
            
            for match in matches:
                # Calculate line number
                line_number = content[:match.start()].count('\n') + 1
                
                # Create remediation suggestion based on rule type
                remediation = self._get_remediation_for_rule(rule_id, rule)
                
                issue = self.create_issue(
                    IssueType.SECURITY_VULNERABILITY,
                    rule['severity'],
                    f"{rule['description']} (Rule: {rule_id})",
                    f"{rule['description']}. Pattern matched: {match.group()[:50]}{'...' if len(match.group()) > 50 else ''}",
                    relative_path,
                    line_number=line_number,
                    column_number=match.start() - content.rfind('\n', 0, match.start()),
                    rule_id=rule_id,
                    confidence=0.85,
                    impact_score=self._get_impact_score(rule['severity']),
                    likelihood_score=self._get_likelihood_score(rule_id),
                    remediation=remediation,
                    cwe_id=rule.get('cwe'),
                    owasp_category=rule.get('owasp'),
                    tags={'security', 'owasp'}
                )
                
                issues.append(issue)
        
        return issues
    
    def _get_remediation_for_rule(self, rule_id: str, rule: Dict[str, Any]) -> RemediationSuggestion:
        """Get appropriate remediation suggestion for a security rule."""
        remediations = {
            'sql_injection': RemediationSuggestion(
                title="Use parameterized queries",
                description="Replace string concatenation with parameterized queries or prepared statements.",
                code_example="# Bad\ncursor.execute(f'SELECT * FROM users WHERE id = {user_id}')\n\n# Good\ncursor.execute('SELECT * FROM users WHERE id = %s', (user_id,))",
                effort_estimate="30 minutes"
            ),
            'xss_vulnerability': RemediationSuggestion(
                title="Sanitize user input",
                description="Use proper HTML encoding and avoid innerHTML with user data.",
                code_example="# Bad\nelement.innerHTML = userInput\n\n# Good\nelement.textContent = userInput",
                effort_estimate="15 minutes"
            ),
            'cors_wildcard': RemediationSuggestion(
                title="Restrict CORS origins",
                description="Specify allowed origins instead of using wildcard.",
                code_example="# Bad\nallow_origins=['*']\n\n# Good\nallow_origins=['https://yourdomain.com', 'https://app.yourdomain.com']",
                effort_estimate="10 minutes"
            )
        }
        
        return remediations.get(rule_id, RemediationSuggestion(
            title="Address security vulnerability",
            description=f"{rule['description']}. Review the code and apply appropriate security measures.",
            effort_estimate="Variable"
        ))
    
    def _get_impact_score(self, severity: IssueSeverity) -> int:
        """Get impact score based on severity."""
        score_map = {
            IssueSeverity.CRITICAL: 95,
            IssueSeverity.HIGH: 80,
            IssueSeverity.MEDIUM: 60,
            IssueSeverity.LOW: 30,
            IssueSeverity.INFO: 10
        }
        return score_map.get(severity, 50)
    
    def _get_likelihood_score(self, rule_id: str) -> int:
        """Get likelihood score based on rule type."""
        high_likelihood = {
            'sql_injection', 'command_injection', 'xss_vulnerability',
            'hardcoded_crypto_key', 'unsafe_deserialization'
        }
        
        if rule_id in high_likelihood:
            return 85
        elif 'injection' in rule_id:
            return 75
        elif 'missing_' in rule_id:
            return 90
        else:
            return 60