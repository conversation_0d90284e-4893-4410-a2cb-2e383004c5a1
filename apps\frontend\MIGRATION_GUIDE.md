# Frontend Modular Architecture Migration Guide

## Overview

This guide provides step-by-step instructions for migrating the frontend from traditional React components to the new modular, n8n-like architecture. The modular architecture transforms each UI component and data provider into a self-contained "node" with clear interfaces, configuration, and composability.

## Migration Steps

### Phase 1: Preparation (1-2 days)

#### 1.1 Understand Current Architecture
Analyze existing React component structure:
```bash
# Examine current structure
find src/components -name "*.tsx" -type f | head -20

# Review main dependencies
cat package.json
```

#### 1.2 Set Up Module Structure
Create the new modular architecture:
```bash
# Create modules directory
mkdir -p src/lib/modules

# Copy base module system
cp -r path/to/new_modules/* src/lib/modules/
```

#### 1.3 Update Dependencies
Add any new dependencies if needed:
```bash
# Most existing dependencies should work
# Add module system dependencies if required
npm install # or bun install
```

### Phase 2: Core Module System (2-3 days)

#### 2.1 Create Base Module Classes
The core module system is implemented in `src/lib/modules/__init__.ts`. Review the key interfaces:

- `BaseModule`: Abstract base class for all modules
- `ModuleRegistry`: Manages module registration and lifecycle
- `ModuleFactory`: Creates module instances and configurations

#### 2.2 Define Module Types
Current module types:
- `UI_COMPONENT`: Reusable React components
- `DATA_PROVIDER`: Data fetching and management
- `STATE_MANAGER`: State management and persistence
- `API_CLIENT`: HTTP API communication

#### 2.3 Set Up TypeScript Interfaces
Standardize input/output interfaces:
```typescript
interface ModuleInput<T = any> {
  data: T;
  metadata?: Record<string, any>;
  context?: Record<string, any>;
}

interface ModuleOutput<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  metadata?: Record<string, any>;
}
```

### Phase 3: Convert Components to Modules (3-5 days)

#### 3.1 Extract UI Component Modules
Convert React components to modular format:

**Before:**
```tsx
// src/components/common/Button.tsx
interface ButtonProps {
  children: ReactNode;
  variant?: 'primary' | 'secondary';
  onClick?: () => void;
}

export function Button({ children, variant = 'primary', onClick }: ButtonProps) {
  const baseClasses = "inline-flex items-center justify-center rounded-md";
  const variants = {
    primary: "bg-primary text-primary-foreground hover:bg-primary/90",
    secondary: "bg-secondary text-secondary-foreground hover:bg-secondary/80"
  };

  return (
    <button className={`${baseClasses} ${variants[variant]}`} onClick={onClick}>
      {children}
    </button>
  );
}
```

**After:**
```tsx
// src/lib/modules/ui-components.tsx
class ButtonModule extends UIComponentModule<ButtonProps> {
  render(props: ButtonProps) {
    const baseClasses = "inline-flex items-center justify-center rounded-md";
    const variants = {
      primary: "bg-primary text-primary-foreground hover:bg-primary/90",
      secondary: "bg-secondary text-secondary-foreground hover:bg-secondary/80"
    };

    return (
      <button className={`${baseClasses} ${variants[props.variant || 'primary']}`} onClick={props.onClick}>
        {props.children}
      </button>
    );
  }
}
```

#### 3.2 Extract Data Provider Modules
Convert hooks and data providers:

**Before:**
```tsx
// src/lib/hooks/useProjectScanner.ts
export function useProjectScanner() {
  const { apiCall, loading, error } = useApi();

  const startScan = async (request: ProjectScanRequest) => {
    const response = await apiCall<{ scanId: string }>({
      endpoint: '/scan',
      method: 'POST',
      data: request
    });
    return response.data?.scanId;
  };

  return { startScan, loading, error };
}
```

**After:**
```tsx
// src/lib/modules/data-providers.tsx
class ProjectScannerProviderModule extends BaseModule {
  private apiClient: APIClientModule | null = null;

  async initialize(config: ModuleConfig): Promise<boolean> {
    this.apiClient = this.getDependency<APIClientModule>('apiClient');
    return true;
  }

  async execute(input: ModuleInput): Promise<ModuleOutput> {
    const { action, data } = input.data;

    if (action === 'startScan' && this.apiClient) {
      const result = await this.apiClient.execute({
        data: { method: 'POST', endpoint: '/scan', data }
      });
      return result;
    }

    return { success: false, error: 'Unknown action' };
  }
}
```

### Phase 4: Update Application Structure (2-3 days)

#### 4.1 Create Modular App Component
Replace traditional app structure with modular version:

**Before:**
```tsx
// src/app/layout.tsx
export default function RootLayout({ children }: { children: ReactNode }) {
  return (
    <html lang="en">
      <body>
        <ThemeProvider>
          <AppLayout>{children}</AppLayout>
        </ThemeProvider>
      </body>
    </html>
  );
}
```

**After:**
```tsx
// src/app/layout.tsx
import { initializeModularApp } from '@/lib/modules/app';

export default function RootLayout({ children }: { children: ReactNode }) {
  useEffect(() => {
    initializeModularApp().then((results) => {
      console.log('Modules initialized:', results);
    });
  }, []);

  return (
    <html lang="en">
      <body>
        <ThemeProvider>
          <AppLayout>{children}</AppLayout>
        </ThemeProvider>
      </body>
    </html>
  );
}
```

#### 4.2 Set Up Module Configuration
Create module configurations:
```tsx
// src/lib/modules/app.tsx
const configs = [
  ModuleFactory.createConfig('main-button', ModuleType.UI_COMPONENT, {
    defaultVariant: 'primary',
    defaultSize: 'md'
  }, { priority: 10 }),

  ModuleFactory.createConfig('app-state', ModuleType.STATE_MANAGER, {
    persist: true,
    storageKey: 'modular-app-state'
  }, { priority: 20 }),

  ModuleFactory.createConfig('api-client', ModuleType.API_CLIENT, {
    baseURL: '/api/v1',
    headers: { 'Content-Type': 'application/json' }
  }, { priority: 30 })
];
```

### Phase 5: Component Integration (2-3 days)

#### 5.1 Replace Component Usage
Update component usage throughout the app:

**Before:**
```tsx
// src/app/dashboard/page.tsx
import { Card } from '@/components/common/Card';
import { Button } from '@/components/common/Button';

export default function Dashboard() {
  return (
    <div>
      <Card title="Welcome">
        <Button onClick={() => console.log('Clicked!')}>
          Get Started
        </Button>
      </Card>
    </div>
  );
}
```

**After:**
```tsx
// src/app/dashboard/page.tsx
import { useModule } from '@/lib/modules/__init__';

export default function Dashboard() {
  const cardModule = useModule('main-card');
  const buttonModule = useModule('main-button');

  return (
    <div>
      {cardModule?.render({
        title: 'Welcome',
        children: buttonModule?.render({
          children: 'Get Started',
          onClick: () => console.log('Clicked!')
        })
      })}
    </div>
  );
}
```

#### 5.2 Update Hooks and Data Fetching
Replace custom hooks with modular data providers:

**Before:**
```tsx
// src/app/models/page.tsx
export default function ModelsPage() {
  const { models, loading, error } = useModels();

  if (loading) return <div>Loading...</div>;
  if (error) return <div>Error: {error}</div>;

  return (
    <div>
      {models.map(model => <div key={model.id}>{model.name}</div>)}
    </div>
  );
}
```

**After:**
```tsx
// src/app/models/page.tsx
export default function ModelsPage() {
  const [models, setModels] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const dataProvider = useModule('data-provider');

  const loadModels = async () => {
    if (!dataProvider) return;

    setLoading(true);
    const result = await dataProvider.execute({
      data: { resource: 'models', method: 'GET' }
    });

    if (result.success) {
      setModels(result.data?.models || []);
    } else {
      setError(result.error);
    }
    setLoading(false);
  };

  useEffect(() => { loadModels(); }, []);

  if (loading) return <div>Loading...</div>;
  if (error) return <div>Error: {error}</div>;

  return (
    <div>
      {models.map(model => <div key={model.id}>{model.name}</div>)}
    </div>
  );
}
```

### Phase 6: State Management Migration (1-2 days)

#### 6.1 Replace Redux/Context with Modular State
Convert global state management:

**Before:**
```tsx
// Redux or Context
const { state, dispatch } = useAppContext();

dispatch({ type: 'SET_LOADING', payload: true });
```

**After:**
```tsx
// Modular state management
const { state, updateState } = useStateManager('app-state');

updateState({ isLoading: true });
```

#### 6.2 Update Component State
Convert local component state:

**Before:**
```tsx
function MyComponent() {
  const [isOpen, setIsOpen] = useState(false);
  const [data, setData] = useState(null);
}
```

**After:**
```tsx
function MyComponent() {
  const { state, updateState } = useStateManager('component-state');

  const isOpen = state?.isOpen ?? false;
  const data = state?.data;

  const toggleOpen = () => updateState({ isOpen: !isOpen });
}
```

### Phase 7: Styling and Theming (1 day)

#### 7.1 Update Component Styling
Adapt existing styles to modular components:

**Before:**
```tsx
// src/components/common/Button.tsx
const variants = {
  primary: "bg-blue-600 text-white hover:bg-blue-700",
  secondary: "bg-gray-600 text-white hover:bg-gray-700"
};
```

**After:**
```tsx
// src/lib/modules/ui-components.tsx
const variants = {
  primary: "bg-primary text-primary-foreground hover:bg-primary/90",
  secondary: "bg-secondary text-secondary-foreground hover:bg-secondary/80"
};
```

#### 7.2 Theme Integration
Ensure modular components respect theme:

```tsx
// Use CSS custom properties
const themeVariants = {
  primary: "bg-[var(--primary)] text-[var(--primary-foreground)] hover:bg-[var(--primary)]/90",
  secondary: "bg-[var(--secondary)] text-[var(--secondary-foreground)] hover:bg-[var(--secondary)]/80"
};
```

### Phase 8: Testing and Validation (2-3 days)

#### 8.1 Unit Tests for Modules
Create tests for individual modules:
```tsx
// src/lib/modules/__tests__/ui-components.test.tsx
describe('ButtonModule', () => {
  it('renders with correct props', () => {
    const button = new ButtonModule('test-button');
    const element = button.render({
      children: 'Test Button',
      variant: 'primary'
    });

    expect(element.props.children).toBe('Test Button');
    expect(element.props.className).toContain('bg-primary');
  });
});
```

#### 8.2 Integration Tests
Test module interactions:
```tsx
// src/lib/modules/__tests__/integration.test.tsx
describe('Module Integration', () => {
  it('data provider uses API client', async () => {
    const apiClient = new APIClientModule('test-api');
    const dataProvider = new DataProviderModule('test-data');

    dataProvider.setDependency('apiClient', apiClient);

    const result = await dataProvider.execute({
      data: { resource: 'models', method: 'GET' }
    });

    expect(result.success).toBe(true);
  });
});
```

#### 8.3 Component Tests
Test modular components in React:
```tsx
// src/components/__tests__/ModularComponent.test.tsx
describe('ModularComponent', () => {
  it('renders modular components correctly', () => {
    render(<ModularDashboard />);

    // Test that modular components are rendered
    expect(screen.getByText('Welcome')).toBeInTheDocument();
  });
});
```

### Phase 9: Deployment and Monitoring (1-2 days)

#### 9.1 Build Configuration
Update build scripts and configurations:
```json
// package.json
{
  "scripts": {
    "build": "next build",
    "build:modules": "tsc --project tsconfig.modules.json"
  }
}
```

#### 9.2 Module Monitoring
Add module-specific monitoring:
```tsx
// src/lib/modules/monitoring.ts
export function useModuleMonitoring() {
  const [metrics, setMetrics] = useState<Record<string, any>>({});

  const trackModuleUsage = (moduleName: string, action: string) => {
    setMetrics(prev => ({
      ...prev,
      [moduleName]: {
        ...prev[moduleName],
        lastUsed: Date.now(),
        actions: [...(prev[moduleName]?.actions || []), action]
      }
    }));
  };

  return { metrics, trackModuleUsage };
}
```

## Migration Checklist

### ✅ Phase 1: Preparation
- [ ] Analyze current React component structure
- [ ] Set up module directory structure
- [ ] Update TypeScript configurations

### ✅ Phase 2: Core System
- [ ] Implement base module classes
- [ ] Define module types and interfaces
- [ ] Set up TypeScript interfaces

### ✅ Phase 3: Component Conversion
- [ ] Convert Button to ButtonModule
- [ ] Convert Card to CardModule
- [ ] Convert other common components
- [ ] Convert custom hooks to data providers

### ✅ Phase 4: Application Updates
- [ ] Create modular app component
- [ ] Set up module configurations
- [ ] Update main layout with module initialization

### ✅ Phase 5: Component Integration
- [ ] Replace component imports with module usage
- [ ] Update data fetching patterns
- [ ] Implement module composition

### ✅ Phase 6: State Management
- [ ] Replace Redux/Context with modular state
- [ ] Convert local component state
- [ ] Implement state persistence

### ✅ Phase 7: Styling
- [ ] Update component styling for modules
- [ ] Ensure theme integration
- [ ] Handle responsive design

### ✅ Phase 8: Testing
- [ ] Create unit tests for modules
- [ ] Implement integration tests
- [ ] Test React component integration

### ✅ Phase 9: Deployment
- [ ] Update build configurations
- [ ] Set up module monitoring
- [ ] Implement error boundaries

## Troubleshooting

### Common Issues

**Module Not Found**
```tsx
// Check module registration
const module = registry.get('module-name');
if (!module) {
  console.error('Module not found. Check registration.');
}

// Verify initialization
useEffect(() => {
  initializeModularApp().then(() => {
    console.log('Modules initialized');
  });
}, []);
```

**TypeScript Errors**
```tsx
// Check type definitions
interface ModuleProps {
  children: ReactNode;
  variant?: 'primary' | 'secondary';
}

// Update module interfaces
class MyModule extends UIComponentModule<ModuleProps> {
  render(props: ModuleProps) {
    // Implementation
  }
}
```

**State Management Issues**
```tsx
// Verify state manager configuration
const stateManager = registry.get<StateManagerModule>('app-state');
if (!stateManager) {
  console.error('State manager not initialized');
}

// Check state updates
const { state, updateState } = useStateManager('app-state');
console.log('Current state:', state);
```

### Performance Issues

**Slow Module Loading**
- Implement lazy loading for heavy modules
- Use React.memo for expensive components
- Optimize bundle splitting

**Memory Leaks**
- Clean up module subscriptions
- Use proper dependency cleanup
- Monitor component unmounting

**Re-render Issues**
- Use useMemo and useCallback
- Implement proper key props
- Avoid unnecessary state updates

## Rollback Plan

### Emergency Rollback
If issues occur, rollback to traditional React:

1. **Switch Component Usage**
```tsx
// Temporarily use original components
import { Button } from '@/components/common/Button';

<Button onClick={handleClick}>
  Click me
</Button>
```

2. **Restore Original Structure**
```tsx
// Keep modular version but use traditional components
function App() {
  return (
    <div>
      {/* Use original components temporarily */}
      <Button>Original Button</Button>

      {/* Keep modular components for gradual migration */}
      {/* <ModularButton /> */}
    </div>
  );
}
```

3. **Gradual Rollback**
```tsx
// Feature flags for gradual rollback
const USE_MODULAR = process.env.NEXT_PUBLIC_USE_MODULAR === 'true';

function MyComponent() {
  if (USE_MODULAR) {
    return <ModularComponent />;
  }
  return <TraditionalComponent />;
}
```

### Module-Level Rollback
For partial rollback of specific features:

1. **Disable Problematic Modules**
```tsx
// Disable in configuration
const config = ModuleFactory.createConfig(
  'problematic-module',
  ModuleType.UI_COMPONENT,
  { /* settings */ },
  { enabled: false }
);
```

2. **Fallback to Traditional Components**
```tsx
// Use fallback pattern
function MyComponent() {
  const modularComponent = useModule('my-component');

  if (modularComponent) {
    return modularComponent.render(props);
  }

  // Fallback to traditional component
  return <TraditionalComponent {...props} />;
}
```

## Best Practices

### Component Development
- Keep components focused on single responsibilities
- Use clear, descriptive prop interfaces
- Document component behavior and usage
- Handle edge cases gracefully

### Module Configuration
- Never hardcode values in component implementations
- Use configuration objects for all settings
- Validate props at runtime
- Support theme and styling customization

### State Management
- Use state manager modules for complex state
- Implement proper state persistence
- Handle state synchronization between modules
- Use optimistic updates for better UX

### Performance Optimization
- Implement React.memo for expensive components
- Use proper key props for dynamic lists
- Optimize re-renders with useMemo and useCallback
- Implement virtualization for large lists

### Testing Strategy
- Test each module in isolation
- Mock dependencies for unit tests
- Test integration between modules
- Validate component rendering

This migration guide provides a comprehensive roadmap for transforming the frontend architecture. The modular approach will significantly improve component reusability, maintainability, and developer experience.
