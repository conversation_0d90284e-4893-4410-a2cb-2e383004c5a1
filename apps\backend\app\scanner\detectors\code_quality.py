"""
Code Quality Detector for the Project Scanner system.

This module detects code quality issues including complexity, duplication,
style violations, maintainability issues, and test coverage gaps.
"""

import ast
import re
import hashlib
from collections import defaultdict, Counter
from pathlib import Path
from typing import Any, Dict, List, Optional, Set, Tuple

from ..base_detector import RuleBasedDetector
from ..models import (
    DetectorResult,
    Issue,
    IssueSeverity,
    IssueType,
    RemediationSuggestion
)


class CodeComplexityAnalyzer:
    """Analyzes cyclomatic complexity of code."""
    
    def __init__(self):
        self.complexity = 0
        self.function_complexities = {}
        self.class_complexities = {}
    
    def visit_function_def(self, node: ast.FunctionDef) -> int:
        """Calculate complexity for a function."""
        complexity = 1  # Base complexity
        
        for child in ast.walk(node):
            if isinstance(child, (ast.If, ast.While, ast.For, ast.AsyncFor)):
                complexity += 1
            elif isinstance(child, ast.ExceptH<PERSON><PERSON>):
                complexity += 1
            elif isinstance(child, ast.With, ast.AsyncWith):
                complexity += 1
            elif isinstance(child, ast.ListComp, ast.SetComp, ast.DictComp, ast.GeneratorExp):
                complexity += 1
            elif isinstance(child, ast.BoolOp):
                complexity += len(child.values) - 1
        
        return complexity
    
    def analyze_file(self, content: str) -> Dict[str, Any]:
        """Analyze complexity of entire file."""
        try:
            tree = ast.parse(content)
            results = {
                'total_complexity': 0,
                'functions': {},
                'classes': {},
                'lines_of_code': len(content.splitlines()),
                'blank_lines': content.count('\n\n'),
                'comment_lines': len([line for line in content.splitlines() if line.strip().startswith('#')])
            }
            
            for node in ast.walk(tree):
                if isinstance(node, ast.FunctionDef):
                    complexity = self.visit_function_def(node)
                    results['functions'][node.name] = {
                        'complexity': complexity,
                        'line_number': node.lineno,
                        'lines': node.end_lineno - node.lineno if hasattr(node, 'end_lineno') else 1
                    }
                    results['total_complexity'] += complexity
                
                elif isinstance(node, ast.ClassDef):
                    class_complexity = 0
                    methods = []
                    
                    for item in node.body:
                        if isinstance(item, ast.FunctionDef):
                            method_complexity = self.visit_function_def(item)
                            methods.append({
                                'name': item.name,
                                'complexity': method_complexity,
                                'line_number': item.lineno
                            })
                            class_complexity += method_complexity
                    
                    results['classes'][node.name] = {
                        'complexity': class_complexity,
                        'line_number': node.lineno,
                        'methods': methods
                    }
            
            return results
            
        except SyntaxError:
            return {'error': 'Syntax error in file'}


class CodeDuplicationDetector:
    """Detects duplicate code blocks."""
    
    def __init__(self, min_lines: int = 5):
        self.min_lines = min_lines
        self.code_blocks = defaultdict(list)
    
    def normalize_line(self, line: str) -> str:
        """Normalize a line of code for comparison."""
        # Remove leading/trailing whitespace
        line = line.strip()
        # Replace multiple spaces with single space
        line = re.sub(r'\s+', ' ', line)
        # Remove comments
        line = re.sub(r'#.*$', '', line)
        # Remove string literals (approximate)
        line = re.sub(r'["\'][^"\']*["\']', '""', line)
        return line
    
    def get_code_blocks(self, content: str, file_path: str) -> None:
        """Extract code blocks from file content."""
        lines = content.splitlines()
        
        for i in range(len(lines) - self.min_lines + 1):
            block_lines = []
            for j in range(self.min_lines):
                normalized = self.normalize_line(lines[i + j])
                if normalized:  # Skip empty lines
                    block_lines.append(normalized)
            
            if len(block_lines) >= self.min_lines:
                block_hash = hashlib.md5('\n'.join(block_lines).encode()).hexdigest()
                self.code_blocks[block_hash].append({
                    'file': file_path,
                    'start_line': i + 1,
                    'end_line': i + self.min_lines,
                    'content': block_lines
                })
    
    def find_duplicates(self) -> List[Dict[str, Any]]:
        """Find duplicate code blocks."""
        duplicates = []
        
        for block_hash, locations in self.code_blocks.items():
            if len(locations) > 1:
                duplicates.append({
                    'hash': block_hash,
                    'locations': locations,
                    'lines': len(locations[0]['content'])
                })
        
        return duplicates


class CodeQualityDetector(RuleBasedDetector):
    """
    Detects code quality issues including:
    - High cyclomatic complexity
    - Code duplication
    - Long functions/classes
    - Deep nesting
    - Style violations
    - Poor naming conventions
    - Missing documentation
    """
    
    def __init__(self, config):
        super().__init__("CodeQuality", config)
        self.complexity_analyzer = CodeComplexityAnalyzer()
        self.duplication_detector = CodeDuplicationDetector()
    
    def get_supported_file_types(self) -> Set[str]:
        """Get supported file extensions."""
        return {'.py', '.js', '.ts', '.jsx', '.tsx', '.java', '.c', '.cpp', '.cs', '.go', '.php', '.rb'}
    
    def _load_rules(self) -> Dict[str, Any]:
        """Load code quality detection rules."""
        return {
            'high_complexity_function': {
                'threshold': 10,
                'severity': IssueSeverity.HIGH,
                'description': 'Function has high cyclomatic complexity'
            },
            'very_high_complexity_function': {
                'threshold': 20,
                'severity': IssueSeverity.CRITICAL,
                'description': 'Function has very high cyclomatic complexity'
            },
            'long_function': {
                'threshold': 50,
                'severity': IssueSeverity.MEDIUM,
                'description': 'Function is too long'
            },
            'very_long_function': {
                'threshold': 100,
                'severity': IssueSeverity.HIGH,
                'description': 'Function is extremely long'
            },
            'long_class': {
                'threshold': 300,
                'severity': IssueSeverity.MEDIUM,
                'description': 'Class is too long'
            },
            'deep_nesting': {
                'threshold': 4,
                'severity': IssueSeverity.MEDIUM,
                'description': 'Code has deep nesting levels'
            },
            'too_many_parameters': {
                'threshold': 5,
                'severity': IssueSeverity.LOW,
                'description': 'Function has too many parameters'
            },
            'poor_naming': {
                'patterns': [
                    r'^[a-z]$',  # Single letter variables
                    r'^temp\d*$',  # temp variables
                    r'^data\d*$',  # generic data variables
                    r'^var\d*$',   # var variables
                ],
                'severity': IssueSeverity.LOW,
                'description': 'Poor variable naming'
            },
            'missing_docstring': {
                'severity': IssueSeverity.LOW,
                'description': 'Function/class missing docstring'
            },
            'code_duplication': {
                'min_lines': 5,
                'severity': IssueSeverity.MEDIUM,
                'description': 'Duplicate code detected'
            }
        }
    
    def detect(self, project_path: Path) -> DetectorResult:
        """Detect code quality issues."""
        self.start_detection()
        
        try:
            files = self.get_project_files(project_path)
            
            # First pass: collect all code blocks for duplication detection
            for file_path in files:
                if file_path.suffix == '.py':  # Focus on Python for duplication
                    content = self.read_file_safely(file_path)
                    if content:
                        self.duplication_detector.get_code_blocks(
                            content, str(file_path.relative_to(project_path))
                        )
            
            # Find duplicates
            duplicates = self.duplication_detector.find_duplicates()
            self._report_duplicates(duplicates, project_path)
            
            # Second pass: analyze individual files
            for file_path in files:
                content = self.read_file_safely(file_path)
                if content:
                    self._analyze_file(file_path, content, project_path)
                    self.files_processed += 1
            
            return self.finish_detection()
            
        except Exception as e:
            return self.finish_detection(success=False, error_message=str(e))
    
    def _analyze_file(self, file_path: Path, content: str, project_path: Path) -> None:
        """Analyze a single file for code quality issues."""
        relative_path = str(file_path.relative_to(project_path))
        
        if file_path.suffix == '.py':
            self._analyze_python_file(relative_path, content)
        elif file_path.suffix in {'.js', '.ts', '.jsx', '.tsx'}:
            self._analyze_javascript_file(relative_path, content)
        else:
            self._analyze_generic_file(relative_path, content)
    
    def _analyze_python_file(self, file_path: str, content: str) -> None:
        """Analyze Python file for quality issues."""
        try:
            # Parse AST for detailed analysis
            tree = ast.parse(content)
            
            # Analyze complexity
            complexity_results = self.complexity_analyzer.analyze_file(content)
            
            if 'error' not in complexity_results:
                # Check function complexities
                for func_name, func_data in complexity_results['functions'].items():
                    complexity = func_data['complexity']
                    line_number = func_data['line_number']
                    lines = func_data['lines']
                    
                    # High complexity
                    if complexity >= self.rules['very_high_complexity_function']['threshold']:
                        self.add_issue(self.create_issue(
                            IssueType.CODE_COMPLEXITY,
                            IssueSeverity.CRITICAL,
                            f"Function '{func_name}' has very high cyclomatic complexity ({complexity})",
                            f"This function has a cyclomatic complexity of {complexity}, which makes it difficult to understand and maintain. Consider breaking it into smaller functions.",
                            file_path,
                            line_number=line_number,
                            function_name=func_name,
                            rule_id='very_high_complexity_function',
                            impact_score=80,
                            likelihood_score=90,
                            remediation=RemediationSuggestion(
                                title="Refactor complex function",
                                description="Break this function into smaller, more focused functions. Use the Extract Method refactoring technique.",
                                effort_estimate="2-4 hours",
                                references=["https://refactoring.com/catalog/extractFunction.html"]
                            )
                        ))
                    elif complexity >= self.rules['high_complexity_function']['threshold']:
                        self.add_issue(self.create_issue(
                            IssueType.CODE_COMPLEXITY,
                            IssueSeverity.HIGH,
                            f"Function '{func_name}' has high cyclomatic complexity ({complexity})",
                            f"This function has a cyclomatic complexity of {complexity}. Consider simplifying the logic.",
                            file_path,
                            line_number=line_number,
                            function_name=func_name,
                            rule_id='high_complexity_function',
                            impact_score=60,
                            likelihood_score=80
                        ))
                    
                    # Long functions
                    if lines >= self.rules['very_long_function']['threshold']:
                        self.add_issue(self.create_issue(
                            IssueType.MAINTAINABILITY,
                            IssueSeverity.HIGH,
                            f"Function '{func_name}' is extremely long ({lines} lines)",
                            f"This function is {lines} lines long, which makes it difficult to understand and maintain.",
                            file_path,
                            line_number=line_number,
                            function_name=func_name,
                            rule_id='very_long_function',
                            impact_score=70,
                            likelihood_score=85
                        ))
                    elif lines >= self.rules['long_function']['threshold']:
                        self.add_issue(self.create_issue(
                            IssueType.MAINTAINABILITY,
                            IssueSeverity.MEDIUM,
                            f"Function '{func_name}' is too long ({lines} lines)",
                            f"This function is {lines} lines long. Consider breaking it into smaller functions.",
                            file_path,
                            line_number=line_number,
                            function_name=func_name,
                            rule_id='long_function',
                            impact_score=50,
                            likelihood_score=70
                        ))
                
                # Check class sizes
                for class_name, class_data in complexity_results['classes'].items():
                    line_number = class_data['line_number']
                    # Estimate class length based on methods
                    estimated_lines = sum(method['complexity'] * 5 for method in class_data['methods'])
                    
                    if estimated_lines >= self.rules['long_class']['threshold']:
                        self.add_issue(self.create_issue(
                            IssueType.MAINTAINABILITY,
                            IssueSeverity.MEDIUM,
                            f"Class '{class_name}' is too large",
                            f"This class appears to be very large with {len(class_data['methods'])} methods. Consider splitting into smaller classes.",
                            file_path,
                            line_number=line_number,
                            class_name=class_name,
                            rule_id='long_class',
                            impact_score=60,
                            likelihood_score=60
                        ))
            
            # Check AST for other issues
            for node in ast.walk(tree):
                if isinstance(node, ast.FunctionDef):
                    self._check_function_issues(node, file_path)
                elif isinstance(node, ast.ClassDef):
                    self._check_class_issues(node, file_path)
        
        except SyntaxError:
            self.add_issue(self.create_issue(
                IssueType.STYLE_VIOLATION,
                IssueSeverity.HIGH,
                "Python syntax error",
                "File contains syntax errors that prevent analysis.",
                file_path,
                rule_id='syntax_error',
                impact_score=90,
                likelihood_score=100
            ))
    
    def _check_function_issues(self, node: ast.FunctionDef, file_path: str) -> None:
        """Check function-specific issues."""
        # Check parameter count
        param_count = len(node.args.args)
        if param_count > self.rules['too_many_parameters']['threshold']:
            self.add_issue(self.create_issue(
                IssueType.MAINTAINABILITY,
                IssueSeverity.LOW,
                f"Function '{node.name}' has too many parameters ({param_count})",
                f"This function has {param_count} parameters. Consider using a configuration object or reducing the number of parameters.",
                file_path,
                line_number=node.lineno,
                function_name=node.name,
                rule_id='too_many_parameters',
                impact_score=30,
                likelihood_score=50
            ))
        
        # Check for missing docstring
        if not ast.get_docstring(node):
            self.add_issue(self.create_issue(
                IssueType.MISSING_DOCUMENTATION,
                IssueSeverity.LOW,
                f"Function '{node.name}' missing docstring",
                f"Public function '{node.name}' should have a docstring explaining its purpose and parameters.",
                file_path,
                line_number=node.lineno,
                function_name=node.name,
                rule_id='missing_docstring',
                impact_score=20,
                likelihood_score=60
            ))
        
        # Check variable names
        for child in ast.walk(node):
            if isinstance(child, ast.Name) and isinstance(child.ctx, ast.Store):
                self._check_variable_naming(child.id, file_path, child.lineno if hasattr(child, 'lineno') else node.lineno)
    
    def _check_class_issues(self, node: ast.ClassDef, file_path: str) -> None:
        """Check class-specific issues."""
        # Check for missing docstring
        if not ast.get_docstring(node):
            self.add_issue(self.create_issue(
                IssueType.MISSING_DOCUMENTATION,
                IssueSeverity.LOW,
                f"Class '{node.name}' missing docstring",
                f"Public class '{node.name}' should have a docstring explaining its purpose.",
                file_path,
                line_number=node.lineno,
                class_name=node.name,
                rule_id='missing_docstring',
                impact_score=25,
                likelihood_score=50
            ))
    
    def _check_variable_naming(self, var_name: str, file_path: str, line_number: int) -> None:
        """Check variable naming conventions."""
        for pattern in self.rules['poor_naming']['patterns']:
            if re.match(pattern, var_name):
                self.add_issue(self.create_issue(
                    IssueType.STYLE_VIOLATION,
                    IssueSeverity.LOW,
                    f"Poor variable name: '{var_name}'",
                    f"Variable '{var_name}' uses a poor naming convention. Use descriptive names instead.",
                    file_path,
                    line_number=line_number,
                    rule_id='poor_naming',
                    impact_score=15,
                    likelihood_score=40,
                    remediation=RemediationSuggestion(
                        title="Use descriptive variable names",
                        description="Choose variable names that clearly describe what the variable represents.",
                        code_example="# Bad\na = 5\n# Good\nuser_count = 5",
                        effort_estimate="2 minutes"
                    )
                ))
                break
    
    def _analyze_javascript_file(self, file_path: str, content: str) -> None:
        """Analyze JavaScript/TypeScript file for quality issues."""
        lines = content.splitlines()
        
        # Basic checks for JavaScript
        function_pattern = re.compile(r'function\s+(\w+)\s*\([^)]*\)\s*\{')
        arrow_function_pattern = re.compile(r'const\s+(\w+)\s*=\s*\([^)]*\)\s*=>\s*\{')
        
        brace_count = 0
        max_nesting = 0
        current_nesting = 0
        
        for i, line in enumerate(lines, 1):
            # Track nesting level
            brace_count += line.count('{') - line.count('}')
            current_nesting = max(0, brace_count)
            max_nesting = max(max_nesting, current_nesting)
            
            # Check for function declarations
            func_match = function_pattern.search(line) or arrow_function_pattern.search(line)
            if func_match:
                func_name = func_match.group(1)
                # Estimate function length (basic)
                self._estimate_function_length(lines, i, func_name, file_path)
        
        # Check overall nesting
        if max_nesting > self.rules['deep_nesting']['threshold']:
            self.add_issue(self.create_issue(
                IssueType.CODE_COMPLEXITY,
                IssueSeverity.MEDIUM,
                f"Deep nesting detected (max {max_nesting} levels)",
                f"File has deep nesting up to {max_nesting} levels. Consider refactoring to reduce complexity.",
                file_path,
                rule_id='deep_nesting',
                impact_score=50,
                likelihood_score=60
            ))
    
    def _analyze_generic_file(self, file_path: str, content: str) -> None:
        """Analyze any file for basic quality issues."""
        lines = content.splitlines()
        
        # Check for very long files
        if len(lines) > 1000:
            self.add_issue(self.create_issue(
                IssueType.MAINTAINABILITY,
                IssueSeverity.MEDIUM,
                f"Very long file ({len(lines)} lines)",
                f"This file is {len(lines)} lines long, which may make it difficult to maintain.",
                file_path,
                impact_score=40,
                likelihood_score=50
            ))
    
    def _estimate_function_length(self, lines: List[str], start_line: int, func_name: str, file_path: str) -> None:
        """Estimate function length for non-Python files."""
        brace_count = 0
        func_lines = 0
        
        for i in range(start_line - 1, len(lines)):
            line = lines[i]
            brace_count += line.count('{') - line.count('}')
            func_lines += 1
            
            if brace_count == 0 and func_lines > 1:
                break
        
        if func_lines > self.rules['long_function']['threshold']:
            severity = IssueSeverity.HIGH if func_lines > self.rules['very_long_function']['threshold'] else IssueSeverity.MEDIUM
            self.add_issue(self.create_issue(
                IssueType.MAINTAINABILITY,
                severity,
                f"Function '{func_name}' is too long ({func_lines} lines)",
                f"This function is {func_lines} lines long. Consider breaking it into smaller functions.",
                file_path,
                line_number=start_line,
                function_name=func_name,
                rule_id='long_function',
                impact_score=60 if severity == IssueSeverity.HIGH else 40,
                likelihood_score=70
            ))
    
    def _report_duplicates(self, duplicates: List[Dict[str, Any]], project_path: Path) -> None:
        """Report code duplication issues."""
        for duplicate in duplicates:
            locations = duplicate['locations']
            lines = duplicate['lines']
            
            # Create an issue for the first location, mentioning all others
            first_location = locations[0]
            other_locations = [f"{loc['file']}:{loc['start_line']}" for loc in locations[1:]]
            
            description = f"Duplicate code block of {lines} lines found. "
            description += f"Also appears in: {', '.join(other_locations)}"
            
            self.add_issue(self.create_issue(
                IssueType.CODE_DUPLICATION,
                IssueSeverity.MEDIUM,
                f"Duplicate code block ({lines} lines)",
                description,
                first_location['file'],
                line_number=first_location['start_line'],
                line_end=first_location['end_line'],
                rule_id='code_duplication',
                impact_score=50,
                likelihood_score=80,
                tags={'duplication', 'maintainability'},
                remediation=RemediationSuggestion(
                    title="Extract duplicate code into a shared function",
                    description="Create a reusable function or method to eliminate code duplication.",
                    effort_estimate="30 minutes",
                    references=["https://refactoring.com/catalog/extractFunction.html"]
                )
            ))
    
    def _apply_rule(self, rule_id: str, rule: Dict[str, Any], file_path: Path, content: str) -> List[Issue]:
        """Apply a specific rule - not used in this implementation as we use custom analysis."""
        return []