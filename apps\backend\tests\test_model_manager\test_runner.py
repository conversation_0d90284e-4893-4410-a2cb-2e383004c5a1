#!/usr/bin/env python3
"""
Test runner script for model manager tests.
"""

import sys
import os
import subprocess
import argparse
from pathlib import Path


def run_unit_tests():
    """Run unit tests."""
    cmd = [
        "python", "-m", "pytest",
        "unit/",
        "-v",
        "--tb=short",
        "-m", "unit"
    ]
    return subprocess.run(cmd).returncode


def run_integration_tests():
    """Run integration tests."""
    cmd = [
        "python", "-m", "pytest",
        "integration/",
        "-v",
        "--tb=short",
        "-m", "integration"
    ]
    return subprocess.run(cmd).returncode


def run_e2e_tests():
    """Run end-to-end tests."""
    cmd = [
        "python", "-m", "pytest",
        "e2e/",
        "-v",
        "--tb=short",
        "-m", "e2e"
    ]
    return subprocess.run(cmd).returncode


def run_performance_tests():
    """Run performance tests."""
    cmd = [
        "python", "-m", "pytest",
        "performance/",
        "-v",
        "--tb=short",
        "-m", "performance"
    ]
    return subprocess.run(cmd).returncode


def run_all_tests():
    """Run all tests."""
    cmd = [
        "python", "-m", "pytest",
        ".",
        "-v",
        "--tb=short",
        "--cov=app.model_manager",
        "--cov-report=html:htmlcov",
        "--cov-report=term-missing"
    ]
    return subprocess.run(cmd).returncode


def run_specific_test(test_path):
    """Run a specific test file or test function."""
    cmd = [
        "python", "-m", "pytest",
        test_path,
        "-v",
        "--tb=short"
    ]
    return subprocess.run(cmd).returncode


def main():
    parser = argparse.ArgumentParser(description="Model Manager Test Runner")
    parser.add_argument(
        "--type",
        choices=["unit", "integration", "e2e", "performance", "all"],
        default="all",
        help="Type of tests to run"
    )
    parser.add_argument(
        "--test",
        help="Specific test file or function to run"
    )
    parser.add_argument(
        "--coverage",
        action="store_true",
        help="Generate coverage report"
    )
    parser.add_argument(
        "--parallel",
        action="store_true",
        help="Run tests in parallel"
    )
    
    args = parser.parse_args()
    
    # Change to test directory
    test_dir = Path(__file__).parent
    os.chdir(test_dir)
    
    if args.test:
        return run_specific_test(args.test)
    
    test_functions = {
        "unit": run_unit_tests,
        "integration": run_integration_tests,
        "e2e": run_e2e_tests,
        "performance": run_performance_tests,
        "all": run_all_tests,
    }
    
    return test_functions[args.type]()


if __name__ == "__main__":
    sys.exit(main())
