"""
Tests for health check endpoints.
"""

import pytest
from fastapi.testclient import Test<PERSON>lient
from app.main import create_app


@pytest.fixture
def client() -> TestClient:
    """Create a test client for the FastAPI app."""
    app = create_app()
    return TestClient(app)


@pytest.mark.asyncio
async def test_health_check(client: TestClient):
    """Test the basic health check endpoint."""
    response = client.get("/health")
    
    assert response.status_code == 200
    data = response.json()
    assert "status" in data
    assert "service" in data
    assert "version" in data
    assert data["service"] == "loni-backend"


@pytest.mark.asyncio
async def test_api_health_check(client: TestClient):
    """Test the API health check endpoint."""
    response = client.get("/api/v1/health/")
    
    assert response.status_code == 200
    data = response.json()
    assert "status" in data
    assert "service" in data
    assert "version" in data


@pytest.mark.asyncio
async def test_detailed_health_check(client: TestClient):
    """Test the detailed health check endpoint."""
    response = client.get("/api/v1/health/detailed")
    
    assert response.status_code == 200
    data = response.json()
    assert "status" in data
    assert "service" in data
    assert "version" in data
    assert "checks" in data