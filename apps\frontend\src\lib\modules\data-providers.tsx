/**
 * Data Provider Modules - Modular data management and API clients.
 *
 * These modules handle data fetching, caching, and state management
 * in a modular, composable way.
 */

import { useState, useEffect, useCallback } from 'react';
import { BaseModule, ModuleInput, ModuleOutput, ModuleConfig, ModuleType } from './__init__';

// API Client Module
export class APIClientModule extends BaseModule {
  private baseURL: string;
  private headers: Record<string, string>;
  private cache: Map<string, { data: any; timestamp: number }> = new Map();
  private cacheTTL: number;

  constructor(name: string) {
    super(name, ModuleType.API_CLIENT);
  }

  async initialize(config: ModuleConfig): Promise<boolean> {
    await super.initialize(config);

    const settings = config.settings || {};
    this.baseURL = settings.baseURL || '/api/v1';
    this.headers = {
      'Content-Type': 'application/json',
      ...settings.headers
    };
    this.cacheTTL = settings.cacheTTL || 300000; // 5 minutes default

    return true;
  }

  async execute(input: ModuleInput): Promise<ModuleOutput> {
    const { method = 'GET', endpoint, data, params, useCache = true } = input.data;

    try {
      // Create cache key
      const cacheKey = `${method}:${endpoint}:${JSON.stringify(params || {})}`;

      // Check cache for GET requests
      if (method === 'GET' && useCache) {
        const cached = this.cache.get(cacheKey);
        if (cached && Date.now() - cached.timestamp < this.cacheTTL) {
          return {
            success: true,
            data: cached.data,
            metadata: { source: 'cache', module: this.name }
          };
        }
      }

      // Build URL
      let url = `${this.baseURL}${endpoint}`;
      if (params && method === 'GET') {
        const queryString = new URLSearchParams(params).toString();
        url += `?${queryString}`;
      }

      // Make request
      const response = await fetch(url, {
        method,
        headers: this.headers,
        body: method !== 'GET' ? JSON.stringify(data) : undefined
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();

      // Cache successful GET requests
      if (method === 'GET') {
        this.cache.set(cacheKey, { data: result, timestamp: Date.now() });
      }

      return {
        success: true,
        data: result,
        metadata: { source: 'api', module: this.name }
      };

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'API request failed',
        metadata: { module: this.name }
      };
    }
  }

  clearCache(): void {
    this.cache.clear();
  }

  getCacheStats(): { size: number; keys: string[] } {
    return {
      size: this.cache.size,
      keys: Array.from(this.cache.keys())
    };
  }
}

// State Manager Module
export class StateManagerModule<T = any> extends BaseModule {
  private state: T;
  private listeners: Set<(state: T) => void> = new Set();

  constructor(name: string, initialState: T) {
    super(name, ModuleType.STATE_MANAGER);
    this.state = initialState;
  }

  async execute(input: ModuleInput): Promise<ModuleOutput> {
    const { action, payload } = input.data;

    switch (action) {
      case 'get':
        return { success: true, data: this.state, metadata: { module: this.name } };

      case 'set':
        this.state = payload;
        this.notifyListeners();
        return { success: true, data: this.state, metadata: { module: this.name } };

      case 'update':
        this.state = { ...this.state, ...payload };
        this.notifyListeners();
        return { success: true, data: this.state, metadata: { module: this.name } };

      case 'reset':
        this.state = input.data.initialState || this.state;
        this.notifyListeners();
        return { success: true, data: this.state, metadata: { module: this.name } };

      default:
        return {
          success: false,
          error: `Unknown action: ${action}`,
          metadata: { module: this.name }
        };
    }
  }

  subscribe(listener: (state: T) => void): () => void {
    this.listeners.add(listener);
    return () => this.listeners.delete(listener);
  }

  private notifyListeners(): void {
    this.listeners.forEach(listener => listener(this.state));
  }

  getState(): T {
    return this.state;
  }
}

// React hook for using state manager module
export function useStateManager<T>(moduleName: string): {
  state: T | null;
  setState: (state: T) => void;
  updateState: (updates: Partial<T>) => void;
  resetState: (initialState?: T) => void;
} {
  const [state, setState] = useState<T | null>(null);
  const module = useModule<StateManagerModule<T>>(moduleName);

  useEffect(() => {
    if (module) {
      // Subscribe to state changes
      const unsubscribe = module.subscribe(setState);
      // Get initial state
      module.execute({ data: { action: 'get' } }).then(result => {
        if (result.success) {
          setState(result.data);
        }
      });
      return unsubscribe;
    }
  }, [module]);

  const setStateAction = useCallback((newState: T) => {
    module?.execute({ data: { action: 'set', payload: newState } });
  }, [module]);

  const updateStateAction = useCallback((updates: Partial<T>) => {
    module?.execute({ data: { action: 'update', payload: updates } });
  }, [module]);

  const resetStateAction = useCallback((initialState?: T) => {
    module?.execute({ data: { action: 'reset', initialState } });
  }, [module]);

  return {
    state,
    setState: setStateAction,
    updateState: updateStateAction,
    resetState: resetStateAction
  };
}

// Data Provider Module
export class DataProviderModule extends BaseModule {
  private apiClient: APIClientModule | null = null;

  constructor(name: string) {
    super(name, ModuleType.DATA_PROVIDER);
  }

  async initialize(config: ModuleConfig): Promise<boolean> {
    await super.initialize(config);

    // Get API client dependency
    if (config.dependencies.includes('apiClient')) {
      this.apiClient = this.getDependency<APIClientModule>('apiClient');
    }

    return true;
  }

  async execute(input: ModuleInput): Promise<ModuleOutput> {
    const { resource, method = 'GET', data, params } = input.data;

    if (!this.apiClient) {
      return {
        success: false,
        error: 'API client not available',
        metadata: { module: this.name }
      };
    }

    // Route to appropriate endpoint based on resource
    const endpointMap: Record<string, string> = {
      models: '/models',
      transcribe: '/transcribe',
      scan: '/scan',
      modules: '/modules'
    };

    const endpoint = endpointMap[resource];
    if (!endpoint) {
      return {
        success: false,
        error: `Unknown resource: ${resource}`,
        metadata: { module: this.name }
      };
    }

    // Execute API request
    const apiInput: ModuleInput = {
      data: { method, endpoint, data, params },
      metadata: { ...input.metadata, provider: this.name }
    };

    const result = await this.apiClient.execute(apiInput);

    return {
      success: result.success,
      data: result.data,
      error: result.error,
      metadata: {
        ...result.metadata,
        provider: this.name,
        resource
      }
    };
  }
}

// Cache Manager Module
export class CacheManagerModule extends BaseModule {
  private cache: Map<string, { data: any; timestamp: number; ttl: number }> = new Map();

  constructor(name: string) {
    super(name, ModuleType.UTILITY);
  }

  async execute(input: ModuleInput): Promise<ModuleOutput> {
    const { action, key, data, ttl = 300000 } = input.data; // 5 minutes default TTL

    switch (action) {
      case 'get':
        const cached = this.cache.get(key);
        if (cached && Date.now() - cached.timestamp < cached.ttl) {
          return {
            success: true,
            data: cached.data,
            metadata: { source: 'cache', module: this.name }
          };
        }
        return {
          success: false,
          error: 'Cache miss or expired',
          metadata: { module: this.name }
        };

      case 'set':
        this.cache.set(key, { data, timestamp: Date.now(), ttl });
        return {
          success: true,
          metadata: { module: this.name }
        };

      case 'delete':
        this.cache.delete(key);
        return {
          success: true,
          metadata: { module: this.name }
        };

      case 'clear':
        this.cache.clear();
        return {
          success: true,
          metadata: { module: this.name }
        };

      case 'stats':
        const now = Date.now();
        const validEntries = Array.from(this.cache.entries()).filter(
          ([, entry]) => now - entry.timestamp < entry.ttl
        );

        return {
          success: true,
          data: {
            total: this.cache.size,
            valid: validEntries.length,
            expired: this.cache.size - validEntries.length,
            keys: Array.from(this.cache.keys())
          },
          metadata: { module: this.name }
        };

      default:
        return {
          success: false,
          error: `Unknown action: ${action}`,
          metadata: { module: this.name }
        };
    }
  }

  // Cleanup expired entries
  cleanup(): void {
    const now = Date.now();
    for (const [key, entry] of this.cache.entries()) {
      if (now - entry.timestamp >= entry.ttl) {
        this.cache.delete(key);
      }
    }
  }
}

// Export factory functions
export const createDataProviders = () => ({
  apiClient: (name: string) => new APIClientModule(name),
  stateManager: <T>(name: string, initialState: T) => new StateManagerModule<T>(name, initialState),
  dataProvider: (name: string) => new DataProviderModule(name),
  cacheManager: (name: string) => new CacheManagerModule(name)
});
