# Modern development tooling configuration
# This file configures pre-commit hooks for code quality, security, and consistency

repos:
  # Python formatting and linting
  - repo: https://github.com/astral-sh/ruff-pre-commit
    rev: v0.1.8
    hooks:
      - id: ruff
        args: [--fix, --exit-non-zero-on-fix]
        files: ^apps/backend/
      - id: ruff-format
        files: ^apps/backend/

  - repo: https://github.com/pre-commit/mirrors-mypy
    rev: v1.8.0
    hooks:
      - id: mypy
        files: ^apps/backend/
        additional_dependencies: [types-all]
        args: [--config-file=apps/backend/pyproject.toml]

  # Python security scanning
  - repo: https://github.com/PyCQA/bandit
    rev: 1.7.5
    hooks:
      - id: bandit
        files: ^apps/backend/
        args: [--config, apps/backend/.bandit]

  # TypeScript/JavaScript formatting and linting
  - repo: https://github.com/pre-commit/mirrors-eslint
    rev: v8.56.0
    hooks:
      - id: eslint
        files: ^apps/frontend/
        types: [file]
        types_or: [javascript, jsx, ts, tsx]
        additional_dependencies:
          - eslint@8.56.0
          - eslint-config-next@14.0.4
          - '@typescript-eslint/parser@6.14.0'
          - '@typescript-eslint/eslint-plugin@6.14.0'
        args: [--fix, --max-warnings=0]

  - repo: https://github.com/pre-commit/mirrors-prettier
    rev: v4.0.0-alpha.8
    hooks:
      - id: prettier
        files: ^apps/frontend/
        types_or: [javascript, jsx, ts, tsx, json, yaml, markdown, css, scss]
        args: [--write]

  # Docker linting
  - repo: https://github.com/hadolint/hadolint
    rev: v2.12.0
    hooks:
      - id: hadolint
        files: Dockerfile.*
        args: [--config, .hadolint.yaml]

  # General file checks
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.5.0
    hooks:
      - id: trailing-whitespace
        exclude: \.md$
      - id: end-of-file-fixer
        exclude: \.svg$|\.lock$
      - id: check-yaml
        args: [--unsafe]
      - id: check-json
        exclude: tsconfig.*\.json$
      - id: check-toml
      - id: check-merge-conflict
      - id: check-added-large-files
        args: [--maxkb=500]
      - id: check-case-conflict
      - id: check-executables-have-shebangs
      - id: check-shebang-scripts-are-executable

  # Security and secrets scanning
  - repo: https://github.com/Yelp/detect-secrets
    rev: v1.4.0
    hooks:
      - id: detect-secrets
        args: [--baseline, .secrets.baseline]
        exclude: \.lock$|package-lock\.json$

  # Dependency checking
  - repo: https://github.com/Lucas-C/pre-commit-hooks-safety
    rev: v1.3.2
    hooks:
      - id: python-safety-dependencies-check
        files: ^apps/backend/
        args: [--ignore=70612]  # Jinja2 vulnerability in dev deps

  # Git hooks
  - repo: https://github.com/commitizen-tools/commitizen
    rev: v3.13.0
    hooks:
      - id: commitizen
        stages: [commit-msg]

  # Additional Python checks
  - repo: https://github.com/asottile/pyupgrade
    rev: v3.15.0
    hooks:
      - id: pyupgrade
        files: ^apps/backend/
        args: [--py311-plus]

  # Shell script linting
  - repo: https://github.com/koalaman/shellcheck-precommit
    rev: v0.9.0
    hooks:
      - id: shellcheck
        files: \.sh$
        args: [--severity=warning]

# Configuration
default_language_version:
  python: python3.11
  node: "18.17.0"

# Performance optimizations
default_stages: [commit, push]
fail_fast: false
minimum_pre_commit_version: 3.6.0