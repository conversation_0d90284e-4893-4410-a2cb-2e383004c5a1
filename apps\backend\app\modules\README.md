# Backend Modular Architecture

## Overview

The backend has been transformed into a modular, n8n-like architecture where each business logic component is a self-contained "node" with clear interfaces, configuration, and dependencies. This architecture enables:

- **Self-contained**: Each module has clear inputs, outputs, and dependencies
- **Reusable**: Modules can be used in different contexts and workflows
- **Composable**: Modules can easily connect and work together like workflow nodes
- **Configurable**: Each module accepts configuration parameters instead of hardcoded values
- **Testable**: Each module can be independently tested with clear interfaces

## Architecture Components

### 1. Core Module System (`__init__.py`)

#### BaseModule Class
The foundation for all modules providing common functionality:

```python
class BaseModule(ABC, Generic[T]):
    def __init__(self, name: str, module_type: ModuleType)
    async def initialize(self, config: ModuleConfig) -> bool
    async def execute(self, input_data: ModuleInput) -> ModuleOutput
    async def validate(self, input_data: ModuleInput) -> bool
    async def cleanup(self) -> None
```

#### Module Types
- `MODEL_MANAGER`: Ollama model management
- `TRANSCRIPTION`: Audio transcription services
- `SCANNER`: Project scanning and analysis
- `API_CLIENT`: HTTP API communication
- `CACHE`: Caching functionality
- `STORAGE`: Data storage and retrieval
- `NOTIFICATION`: Notification services
- `METRICS`: Monitoring and metrics collection

#### Data Flow
```python
@dataclass
class ModuleInput:
    data: Dict[str, Any]        # Input data
    metadata: Dict[str, Any]    # Additional context
    context: Dict[str, Any]     # Execution context

@dataclass
class ModuleOutput:
    success: bool              # Operation success
    data: Dict[str, Any]       # Output data
    error: str                 # Error message if failed
    metadata: Dict[str, Any]   # Additional metadata
```

### 2. Model Manager Module (`model_manager.py`)

#### Purpose
Manages Ollama model operations with caching and fallback strategies.

#### Features
- List available models with caching
- Pull models from Ollama registry
- Set active model
- Use models for inference
- Health checking
- Cache management

#### Configuration
```python
config = ModuleConfig(
    name="ollama_manager",
    type=ModuleType.MODEL_MANAGER,
    settings={
        'host': 'http://localhost:11434',
        'api_timeout': 30
    }
)
```

#### Usage
```python
input_data = ModuleInput(
    data={"action": "list"},
    metadata={"request_type": "api"}
)

result = await model_manager.execute(input_data)
if result.success:
    models = result.data.get("models", [])
```

### 3. Transcription Module (`transcription.py`)

#### Purpose
Provides audio transcription using Whisper models.

#### Features
- Simple and advanced transcription
- Batch processing
- Model caching
- Thread pool execution
- Progress tracking

#### Configuration
```python
config = ModuleConfig(
    name="whisper_transcriber",
    type=ModuleType.TRANSCRIPTION,
    settings={
        'model_name': 'base',
        'device': 'cpu',
        'cache_dir': '/tmp/whisper_cache'
    }
)
```

#### Usage
```python
input_data = ModuleInput(
    data={
        "audio_path": "/path/to/audio.wav",
        "language": "en"
    }
)

result = await transcriber.execute(input_data)
if result.success:
    transcription = result.data.get("transcription")
```

### 4. Scanner Module (`scanner.py`)

#### Purpose
Project scanning and code quality analysis.

#### Features
- Multi-detector support
- Parallel execution
- Configurable scanning
- Risk scoring and prioritization
- Comprehensive metrics

#### Configuration
```python
config = ModuleConfig(
    name="project_scanner",
    type=ModuleType.SCANNER,
    settings={
        'enable_code_quality': True,
        'enable_security': True,
        'enable_dependencies': True,
        'enable_structure': True,
        'enable_performance': True,
        'timeout_per_detector': 300
    }
)
```

#### Usage
```python
input_data = ModuleInput(
    data={
        "project_path": "/path/to/project",
        "scan_config": {...}
    }
)

result = await scanner.execute(input_data)
if result.success:
    issues = result.data.get("issues", [])
```

## Module Registration and Initialization

### 1. Registry Management

```python
from app.modules import registry, ModuleFactory

# Create and register modules
model_manager = ModelFactory.create_module(
    ModuleType.MODEL_MANAGER,
    "ollama_manager"
)

registry.register(model_manager)

# Configure modules
config = ModuleFactory.create_config(
    "ollama_manager",
    ModuleType.MODEL_MANAGER,
    {'host': 'localhost:11434'}
)

registry.set_config("ollama_manager", config)

# Initialize all modules
await registry.initialize_all()
```

### 2. Dependency Injection

```python
# Set up dependencies between modules
data_provider.set_dependency('api_client', api_client)

# Dependencies are automatically resolved
dependencies = module.get_dependencies()
# Returns: ['api_client']
```

## FastAPI Integration (`app_modular.py`)

The modular architecture integrates seamlessly with FastAPI:

```python
# Initialize modules on startup
@app.on_event("startup")
async def startup_event():
    await initialize_modules()

# Use modules in route handlers
@app.get("/api/v1/models")
async def list_models():
    model_manager = registry.get("ollama_manager")
    result = await model_manager.execute(input_data)
    return result.data
```

## Benefits of the Modular Architecture

### 1. Separation of Concerns
Each module has a single, well-defined responsibility:
- ModelManager: Ollama operations
- WhisperTranscription: Audio processing
- ProjectScanner: Code analysis

### 2. Testability
Modules can be tested in isolation:
```python
# Test a module independently
input_data = ModuleInput(data={"action": "list"})
result = await module.execute(input_data)
assert result.success
```

### 3. Reusability
Modules can be reused in different contexts:
```python
# Use the same scanner module for different projects
scanner_configs = [
    ModuleConfig("frontend_scanner", ...),
    ModuleConfig("backend_scanner", ...),
    ModuleConfig("mobile_scanner", ...)
]
```

### 4. Configurability
No hardcoded values - everything is configurable:
```python
# Different configurations for different environments
prod_config = ModuleConfig(
    "prod_models",
    settings={'host': 'production-ollama:11434'}
)

dev_config = ModuleConfig(
    "dev_models",
    settings={'host': 'localhost:11434'}
)
```

### 5. Monitoring and Debugging
Built-in logging and monitoring:
```python
# Each module operation is logged
logger.info(f"Module {module.name} executed", {
    'duration': duration,
    'success': result.success,
    'action': input_data.data.get('action')
})
```

## Best Practices

### 1. Module Design
- Keep modules focused on a single responsibility
- Use clear, descriptive names
- Document inputs, outputs, and configuration options
- Handle errors gracefully with meaningful messages

### 2. Configuration Management
- Never hardcode values in module implementations
- Use configuration objects for all settings
- Validate configuration at initialization time
- Support environment-specific configurations

### 3. Error Handling
- Use consistent error response format
- Log errors with context information
- Provide meaningful error messages
- Fail fast on invalid configurations

### 4. Testing
- Test each module in isolation
- Mock dependencies for unit tests
- Test both success and failure scenarios
- Validate input/output contracts

### 5. Performance
- Implement caching where appropriate
- Use async/await for I/O operations
- Monitor resource usage
- Implement connection pooling for external services

## Migration Guide

### From Monolithic to Modular

1. **Identify Components**: Break down monolithic code into logical components
2. **Define Interfaces**: Create clear input/output interfaces for each component
3. **Extract Modules**: Move each component into its own module class
4. **Configure Dependencies**: Set up dependency injection between modules
5. **Update Routes**: Modify FastAPI routes to use modular endpoints
6. **Test Integration**: Ensure all modules work together correctly

### Example Migration

**Before (Monolithic)**:
```python
# Single large service class
class ModelService:
    def __init__(self):
        self.host = "localhost:11434"  # Hardcoded!

    def list_models(self):
        # Implementation...

    def transcribe_audio(self):
        # Implementation...
```

**After (Modular)**:
```python
# Separate focused modules
class OllamaModelModule(BaseModule):
    async def initialize(self, config: ModuleConfig) -> bool:
        self.host = config.settings['host']  # Configurable!

class WhisperTranscriptionModule(BaseModule):
    # Focused only on transcription
    pass
```

## Future Enhancements

### 1. Plugin System
- Load modules dynamically from external packages
- Support third-party module development
- Module marketplace and sharing

### 2. Workflow Engine
- Visual workflow builder
- Drag-and-drop module composition
- Workflow templates and sharing

### 3. Advanced Features
- Real-time module communication
- Module versioning and rollback
- A/B testing between module versions
- Auto-scaling based on module load

### 4. Monitoring and Observability
- Module performance metrics
- Dependency graphs
- Real-time module status dashboard
- Alerting and incident management

This modular architecture provides a solid foundation for building scalable, maintainable, and extensible applications using the n8n-like node-based approach.
