"""Enhanced structured logging utilities."""
import json
from typing import Any, Dict, Optional
from loguru import logger

from app.logger_context import (
    correlation_id_context,
    request_id_context,
    user_id_context,
    LoggerContext
)


class StructuredLogger:
    """Enhanced logger with structured logging capabilities."""

    def __init__(self, name: str = "app"):
        self.name = name
        self._logger = logger.bind(service=name)

    def _get_context_data(self) -> Dict[str, Any]:
        """Get current context data for logging."""
        context = {}

        # Add correlation and request IDs if available
        correlation_id = correlation_id_context.get()
        if correlation_id:
            context["correlation_id"] = correlation_id

        request_id = request_id_context.get()
        if request_id:
            context["request_id"] = request_id

        user_id = user_id_context.get()
        if user_id:
            context["user_id"] = user_id

        return context

    def _log_with_context(self, level: str, message: str, **kwargs) -> None:
        """Log message with context data."""
        context = self._get_context_data()
        context.update(kwargs)

        # Use loguru's bind for structured logging
        log_method = getattr(self._logger.bind(**context), level.lower())
        log_method(message)

    def info(self, message: str, **kwargs) -> None:
        """Log info message with context."""
        self._log_with_context("INFO", message, **kwargs)

    def debug(self, message: str, **kwargs) -> None:
        """Log debug message with context."""
        self._log_with_context("DEBUG", message, **kwargs)

    def warning(self, message: str, **kwargs) -> None:
        """Log warning message with context."""
        self._log_with_context("WARNING", message, **kwargs)

    def error(self, message: str, **kwargs) -> None:
        """Log error message with context."""
        self._log_with_context("ERROR", message, **kwargs)

    def critical(self, message: str, **kwargs) -> None:
        """Log critical message with context."""
        self._log_with_context("CRITICAL", message, **kwargs)

    def exception(self, message: str, **kwargs) -> None:
        """Log exception with context."""
        context = self._get_context_data()
        context.update(kwargs)
        self._logger.bind(**context).exception(message)


# Global logger instance
log = StructuredLogger()


def get_logger(name: str = "app") -> StructuredLogger:
    """Get a structured logger instance."""
    return StructuredLogger(name)


def set_correlation_id(correlation_id: str) -> None:
    """Set correlation ID for current context."""
    correlation_id_context.set(correlation_id)


def get_correlation_id() -> Optional[str]:
    """Get current correlation ID."""
    return correlation_id_context.get()


def set_request_id(request_id: str) -> None:
    """Set request ID for current context."""
    request_id_context.set(request_id)


def get_request_id() -> Optional[str]:
    """Get current request ID."""
    return request_id_context.get()


def set_user_id(user_id: str) -> None:
    """Set user ID for current context."""
    user_id_context.set(user_id)


def get_user_id() -> Optional[str]:
    """Get current user ID."""
    return user_id_context.get()


def log_request_start(method: str, url: str, headers: Optional[Dict[str, str]] = None) -> str:
    """Log the start of a request with correlation ID."""
    correlation_id = str(uuid.uuid4())
    request_id = str(uuid.uuid4())

    with LoggerContext(correlation_id=correlation_id, request_id=request_id):
        log.info(
            "Request started",
            method=method,
            url=url,
            headers=headers,
            correlation_id=correlation_id,
            request_id=request_id
        )

    return correlation_id


def log_request_end(correlation_id: str, status_code: int, response_time: float, error: Optional[str] = None) -> None:
    """Log the end of a request."""
    with LoggerContext(correlation_id=correlation_id):
        if error:
            log.error(
                "Request failed",
                status_code=status_code,
                response_time=response_time,
                error=error
            )
        else:
            log.info(
                "Request completed",
                status_code=status_code,
                response_time=response_time
            )


def log_model_operation(model_name: str, operation: str, duration: Optional[float] = None, **kwargs) -> None:
    """Log model operations with timing and context."""
    context = {
        "model_name": model_name,
        "operation": operation,
        "duration_ms": duration * 1000 if duration else None,
        **kwargs
    }

    log.info(f"Model {operation}", **context)


def log_performance_metric(name: str, value: float, unit: str = "ms", **kwargs) -> None:
    """Log performance metrics."""
    log.info(f"Performance metric: {name}", metric_name=name, value=value, unit=unit, **kwargs)


def create_audit_log(action: str, resource: str, outcome: str, details: Optional[Dict[str, Any]] = None) -> None:
    """Create audit log entry."""
    audit_data = {
        "action": action,
        "resource": resource,
        "outcome": outcome,
        "details": details or {}
    }

    log.info("Audit log", audit_action=action, audit_resource=resource,
             audit_outcome=outcome, **audit_data["details"])


# Utility function to create child logger with additional context
def child_logger(parent_logger: StructuredLogger, **context) -> StructuredLogger:
    """Create a child logger with additional context."""
    child = StructuredLogger(parent_logger.name)
    # Bind additional context to the child logger
    child._logger = child._logger.bind(**context)
    return child
