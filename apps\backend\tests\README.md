# LONI Backend Tests

This directory contains all tests for the LONI backend API.

## Test Structure

- `test_api/` - API endpoint tests
- `test_services/` - Service layer tests
- `test_models/` - Database model tests
- `test_utils/` - Utility function tests
- `conftest.py` - Test configuration and fixtures

## Running Tests

```bash
# Run all tests
pytest

# Run tests with coverage
pytest --cov=app tests/

# Run specific test file
pytest tests/test_api/test_auth.py
```